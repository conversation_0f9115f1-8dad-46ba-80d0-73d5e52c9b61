/**
 * API-Database Integration Tests for Unified Architecture
 *
 * Tests the complete flow from API endpoints through to database queries:
 * - API endpoint to database operation mapping
 * - Data transformation between database results and API responses
 * - Error handling when database operations fail
 * - Performance monitoring of complete API-to-database flow
 * - Real data validation and response formatting
 *
 * Uses actual API service and database connections with comprehensive logging
 */

const request = require('supertest');
const express = require('express');
const { performance } = require('perf_hooks');
const db = require('../../../frontend/server/database');

// Import API routes (we'll need to create a test server)
const apiRoutes = require('../../../frontend/server/api');

// Test configuration
const TEST_CONFIG = {
  environment: 'qa02',
  timeout: 45000,      // 45 second timeout for API-DB operations
  maxRetries: 3,
  logDetails: true,
  port: 3001          // Use different port for testing
};

// Performance and flow tracking
const flowTracker = {
  operations: [],

  startFlow(name) {
    const start = performance.now();
    const flowId = `${name}_${Date.now()}`;

    return {
      flowId,
      name,
      start,
      steps: [],

      addStep: function(stepName, duration, data = null) {
        this.steps.push({
          name: stepName,
          duration: duration.toFixed(2),
          data: data ? JSON.stringify(data, null, 2) : null,
          timestamp: new Date().toISOString()
        });
      },

      end: function() {
        const end = performance.now();
        const totalDuration = end - this.start;

        const operation = {
          flowId: this.flowId,
          name: this.name,
          totalDuration: totalDuration.toFixed(2),
          steps: this.steps,
          timestamp: new Date().toISOString()
        };

        flowTracker.operations.push(operation);

        console.log(`\n🔄 Flow Complete: ${this.name}`);
        console.log(`   Total Duration: ${totalDuration.toFixed(2)}ms`);
        console.log(`   Steps: ${this.steps.length}`);

        this.steps.forEach((step, index) => {
          console.log(`   ${index + 1}. ${step.name}: ${step.duration}ms`);
        });

        return totalDuration;
      }
    };
  },

  getSummary() {
    const totalOps = this.operations.length;
    const totalTime = this.operations.reduce((sum, op) => sum + parseFloat(op.totalDuration), 0);
    const avgTime = totalTime / totalOps;

    return {
      totalOperations: totalOps,
      totalTime: totalTime.toFixed(2),
      averageTime: avgTime.toFixed(2),
      operations: this.operations
    };
  },

  reset() {
    this.operations = [];
  }
};

// Enhanced logging for API-DB flow
const flowLogger = {
  apiRequest: (method, endpoint, params = {}) => {
    console.log(`\n🌐 API Request: ${method.toUpperCase()} ${endpoint}`);
    if (Object.keys(params).length > 0) {
      console.log(`   Parameters:`, JSON.stringify(params, null, 2));
    }
  },

  apiResponse: (status, data, duration) => {
    console.log(`📤 API Response: ${status} (${duration.toFixed(2)}ms)`);
    if (data) {
      console.log(`   Response Data:`, JSON.stringify(data, null, 2));
    }
  },

  dbQuery: (sql, params = [], duration) => {
    console.log(`🗄️  Database Query (${duration.toFixed(2)}ms):`);
    console.log(`   SQL: ${sql}`);
    if (params.length > 0) {
      console.log(`   Parameters: [${params.join(', ')}]`);
    }
  },

  dbResult: (result, count) => {
    console.log(`📊 Database Result: ${count} rows`);
    if (result && Array.isArray(result) && result.length > 0) {
      console.log(`   Sample Row:`, JSON.stringify(result[0], null, 2));
    }
  },

  transformation: (input, output, duration) => {
    console.log(`🔄 Data Transformation (${duration.toFixed(2)}ms):`);
    console.log(`   Input Type: ${Array.isArray(input) ? 'Array' : typeof input}`);
    console.log(`   Output Type: ${Array.isArray(output) ? 'Array' : typeof output}`);
    if (Array.isArray(input)) console.log(`   Input Count: ${input.length}`);
    if (Array.isArray(output)) console.log(`   Output Count: ${output.length}`);
  },

  error: (stage, error) => {
    console.log(`❌ Error in ${stage}:`);
    console.log(`   Message: ${error.message}`);
    console.log(`   Stack: ${error.stack}`);
  },

  performance: (operation, apiTime, dbTime, totalTime) => {
    console.log(`⏱️  Performance Breakdown - ${operation}:`);
    console.log(`   API Processing: ${apiTime.toFixed(2)}ms`);
    console.log(`   Database Query: ${dbTime.toFixed(2)}ms`);
    console.log(`   Total Time: ${totalTime.toFixed(2)}ms`);
    console.log(`   DB Percentage: ${((dbTime / totalTime) * 100).toFixed(1)}%`);
  }
};

// Test server setup
let testServer;
let testApp;

describe('API-Database Integration Tests', () => {
  beforeAll(async () => {
    console.log('\n🚀 Starting API-Database Integration Tests');
    console.log(`Environment: ${TEST_CONFIG.environment}`);
    console.log(`Timeout: ${TEST_CONFIG.timeout}ms`);

    // Reset flow tracker
    flowTracker.reset();

    // Initialize database connection
    try {
      await db.init(TEST_CONFIG.environment);
      console.log('✅ Database connection established');
    } catch (error) {
      console.error('❌ Failed to establish database connection:', error);
      throw error;
    }

    // Create test Express app
    testApp = express();
    testApp.use(express.json());
    testApp.use(express.urlencoded({ extended: true }));

    // Add test routes that mirror the actual API
    testApp.get('/local/test-cases', async (req, res) => {
      const flow = flowTracker.startFlow('GET /local/test-cases');
      const apiStart = performance.now();

      try {
        flowLogger.apiRequest('GET', '/local/test-cases', req.query);

        // Simulate API processing time
        const dbStart = performance.now();
        const result = await db.getTestCases(req.query);
        const dbEnd = performance.now();
        const dbDuration = dbEnd - dbStart;

        flow.addStep('Database Query', dbDuration, { count: result.length });
        flowLogger.dbQuery('getTestCases', [JSON.stringify(req.query)], dbDuration);
        flowLogger.dbResult(result, result.length);

        // Transform data for API response
        const transformStart = performance.now();
        const apiResponse = {
          success: true,
          data: result,
          count: result.length,
          timestamp: new Date().toISOString()
        };
        const transformEnd = performance.now();
        const transformDuration = transformEnd - transformStart;

        flow.addStep('Data Transformation', transformDuration, apiResponse);
        flowLogger.transformation(result, apiResponse, transformDuration);

        const apiEnd = performance.now();
        const apiDuration = apiEnd - apiStart;
        const totalDuration = flow.end();

        flowLogger.apiResponse(200, apiResponse, apiDuration);
        flowLogger.performance('GET /local/test-cases', apiDuration - dbDuration, dbDuration, totalDuration);

        res.json(apiResponse);

      } catch (error) {
        const apiEnd = performance.now();
        const apiDuration = apiEnd - apiStart;

        flow.addStep('Error Handling', apiDuration, { error: error.message });
        flowLogger.error('API Handler', error);

        res.status(500).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });

        flow.end();
      }
    });

    testApp.get('/local/test-suites', async (req, res) => {
      const flow = flowTracker.startFlow('GET /local/test-suites');
      const apiStart = performance.now();

      try {
        flowLogger.apiRequest('GET', '/local/test-suites', req.query);

        const dbStart = performance.now();
        const result = await db.getTestSuites(req.query);
        const dbEnd = performance.now();
        const dbDuration = dbEnd - dbStart;

        flow.addStep('Database Query', dbDuration, { count: result.length });
        flowLogger.dbQuery('getTestSuites', [JSON.stringify(req.query)], dbDuration);
        flowLogger.dbResult(result, result.length);

        const transformStart = performance.now();
        const apiResponse = {
          success: true,
          testSuites: result,
          count: result.length,
          timestamp: new Date().toISOString()
        };
        const transformEnd = performance.now();
        const transformDuration = transformEnd - transformStart;

        flow.addStep('Data Transformation', transformDuration, apiResponse);
        flowLogger.transformation(result, apiResponse, transformDuration);

        const apiEnd = performance.now();
        const apiDuration = apiEnd - apiStart;
        const totalDuration = flow.end();

        flowLogger.apiResponse(200, apiResponse, apiDuration);
        flowLogger.performance('GET /local/test-suites', apiDuration - dbDuration, dbDuration, totalDuration);

        res.json(apiResponse);

      } catch (error) {
        const apiEnd = performance.now();
        const apiDuration = apiEnd - apiStart;

        flow.addStep('Error Handling', apiDuration, { error: error.message });
        flowLogger.error('API Handler', error);

        res.status(500).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });

        flow.end();
      }
    });

    testApp.get('/local/recent-runs', async (req, res) => {
      const flow = flowTracker.startFlow('GET /local/recent-runs');
      const apiStart = performance.now();

      try {
        flowLogger.apiRequest('GET', '/local/recent-runs', req.query);

        const dbStart = performance.now();
        const result = await db.getRecentRuns(req.query);
        const dbEnd = performance.now();
        const dbDuration = dbEnd - dbStart;

        flow.addStep('Database Query', dbDuration, { count: result.length });
        flowLogger.dbQuery('getRecentRuns', [JSON.stringify(req.query)], dbDuration);
        flowLogger.dbResult(result, result.length);

        const transformStart = performance.now();
        const apiResponse = {
          success: true,
          data: result,
          count: result.length,
          timestamp: new Date().toISOString()
        };
        const transformEnd = performance.now();
        const transformDuration = transformEnd - transformStart;

        flow.addStep('Data Transformation', transformDuration, apiResponse);
        flowLogger.transformation(result, apiResponse, transformDuration);

        const apiEnd = performance.now();
        const apiDuration = apiEnd - apiStart;
        const totalDuration = flow.end();

        flowLogger.apiResponse(200, apiResponse, apiDuration);
        flowLogger.performance('GET /local/recent-runs', apiDuration - dbDuration, dbDuration, totalDuration);

        res.json(apiResponse);

      } catch (error) {
        const apiEnd = performance.now();
        const apiDuration = apiEnd - apiStart;

        flow.addStep('Error Handling', apiDuration, { error: error.message });
        flowLogger.error('API Handler', error);

        res.status(500).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });

        flow.end();
      }
    });

    console.log('✅ Test server configured');

  }, TEST_CONFIG.timeout);

  afterAll(async () => {
    console.log('\n🏁 Completing API-Database Integration Tests');

    // Display comprehensive flow summary
    const summary = flowTracker.getSummary();
    console.log('\n📊 Complete Flow Summary:');
    console.log(`   Total API-DB Operations: ${summary.totalOperations}`);
    console.log(`   Total Time: ${summary.totalTime}ms`);
    console.log(`   Average Time: ${summary.averageTime}ms`);

    // Display detailed operation breakdown
    console.log('\n📋 Operation Details:');
    summary.operations.forEach((op, index) => {
      console.log(`   ${index + 1}. ${op.name}: ${op.totalDuration}ms (${op.steps.length} steps)`);
    });

    // Close database connection
    try {
      await db.close();
      console.log('✅ Database connection closed');
    } catch (error) {
      console.error('❌ Error closing database connection:', error);
    }

    // Close test server if running
    if (testServer) {
      testServer.close();
      console.log('✅ Test server closed');
    }

  }, TEST_CONFIG.timeout);

  describe('Test Cases API-Database Flow', () => {
    test('should handle complete test cases API-to-database flow', async () => {
      const flow = flowTracker.startFlow('Complete Test Cases Flow');

      try {
        console.log('\n🧪 Testing complete test cases API-to-database flow');

        const response = await request(testApp)
          .get('/local/test-cases')
          .query({ limit: 5 })
          .expect(200);

        const totalDuration = flow.end();

        // Validate API response structure
        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('count');
        expect(response.body).toHaveProperty('timestamp');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data.length).toBeLessThanOrEqual(5);

        // Validate database data structure in API response
        if (response.body.data.length > 0) {
          const testCase = response.body.data[0];
          expect(testCase).toHaveProperty('tc_id');
          expect(testCase).toHaveProperty('tc_name');

          console.log('✅ Test case structure validated');
          console.log(`   Retrieved ${response.body.count} test cases`);
          console.log(`   Sample test case ID: ${testCase.tc_id}`);
        }

        // Performance validation
        expect(totalDuration).toBeLessThan(5000); // Should complete within 5 seconds
        console.log(`✅ Performance validated: ${totalDuration.toFixed(2)}ms`);

      } catch (error) {
        flow.end();
        flowLogger.error('Complete Test Cases Flow', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);

    test('should handle test cases API with database filters', async () => {
      const flow = flowTracker.startFlow('Test Cases with Filters');

      try {
        console.log('\n🔍 Testing test cases API with database filters');

        const filters = { limit: 3, offset: 0 };
        const response = await request(testApp)
          .get('/local/test-cases')
          .query(filters)
          .expect(200);

        const totalDuration = flow.end();

        // Validate filtered response
        expect(response.body.success).toBe(true);
        expect(response.body.data.length).toBeLessThanOrEqual(3);

        console.log(`✅ Filtered query completed in ${totalDuration.toFixed(2)}ms`);
        console.log(`   Applied filters:`, JSON.stringify(filters, null, 2));
        console.log(`   Returned ${response.body.count} results`);

      } catch (error) {
        flow.end();
        flowLogger.error('Test Cases with Filters', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);
  });

  describe('Test Suites API-Database Flow', () => {
    test('should handle complete test suites API-to-database flow', async () => {
      const flow = flowTracker.startFlow('Complete Test Suites Flow');

      try {
        console.log('\n📋 Testing complete test suites API-to-database flow');

        const response = await request(testApp)
          .get('/local/test-suites')
          .query({ limit: 5 })
          .expect(200);

        const totalDuration = flow.end();

        // Validate API response structure
        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('testSuites');
        expect(response.body).toHaveProperty('count');
        expect(response.body).toHaveProperty('timestamp');
        expect(Array.isArray(response.body.testSuites)).toBe(true);
        expect(response.body.testSuites.length).toBeLessThanOrEqual(5);

        // Validate database data structure in API response
        if (response.body.testSuites.length > 0) {
          const testSuite = response.body.testSuites[0];
          expect(testSuite).toHaveProperty('ts_id');
          expect(testSuite).toHaveProperty('ts_name');

          console.log('✅ Test suite structure validated');
          console.log(`   Retrieved ${response.body.count} test suites`);
          console.log(`   Sample test suite ID: ${testSuite.ts_id}`);
        }

        // Performance validation
        expect(totalDuration).toBeLessThan(5000);
        console.log(`✅ Performance validated: ${totalDuration.toFixed(2)}ms`);

      } catch (error) {
        flow.end();
        flowLogger.error('Complete Test Suites Flow', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);
  });

  describe('Recent Runs API-Database Flow', () => {
    test('should handle complete recent runs API-to-database flow', async () => {
      const flow = flowTracker.startFlow('Complete Recent Runs Flow');

      try {
        console.log('\n📊 Testing complete recent runs API-to-database flow');

        const response = await request(testApp)
          .get('/local/recent-runs')
          .query({ limit: 10 })
          .expect(200);

        const totalDuration = flow.end();

        // Validate API response structure
        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('count');
        expect(response.body).toHaveProperty('timestamp');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data.length).toBeLessThanOrEqual(10);

        // Validate database data structure in API response
        if (response.body.data.length > 0) {
          const recentRun = response.body.data[0];
          expect(recentRun).toHaveProperty('tsn_id');
          expect(recentRun).toHaveProperty('start_time');

          console.log('✅ Recent run structure validated');
          console.log(`   Retrieved ${response.body.count} recent runs`);
          console.log(`   Sample run TSN ID: ${recentRun.tsn_id}`);
        }

        // Performance validation
        expect(totalDuration).toBeLessThan(5000);
        console.log(`✅ Performance validated: ${totalDuration.toFixed(2)}ms`);

      } catch (error) {
        flow.end();
        flowLogger.error('Complete Recent Runs Flow', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);
  });

  describe('Error Handling in API-Database Flow', () => {
    test('should handle database connection errors gracefully', async () => {
      const flow = flowTracker.startFlow('Database Error Handling');

      try {
        console.log('\n❌ Testing database error handling in API flow');

        // Temporarily close database connection to simulate error
        await db.close();

        const response = await request(testApp)
          .get('/local/test-cases')
          .query({ limit: 5 })
          .expect(500);

        const totalDuration = flow.end();

        // Validate error response structure
        expect(response.body).toHaveProperty('success', false);
        expect(response.body).toHaveProperty('error');
        expect(response.body).toHaveProperty('timestamp');

        console.log('✅ Error handling validated');
        console.log(`   Error response: ${response.body.error}`);
        console.log(`   Error handled in: ${totalDuration.toFixed(2)}ms`);

        // Restore database connection
        await db.init(TEST_CONFIG.environment);
        console.log('✅ Database connection restored');

      } catch (error) {
        flow.end();
        // Ensure database is restored even if test fails
        try {
          await db.init(TEST_CONFIG.environment);
        } catch (restoreError) {
          console.error('Failed to restore database connection:', restoreError);
        }
        throw error;
      }
    }, TEST_CONFIG.timeout);
  });

  describe('Performance Analysis', () => {
    test('should analyze API-database performance patterns', async () => {
      console.log('\n⚡ Analyzing API-database performance patterns');

      const performanceTests = [
        { endpoint: '/local/test-cases', params: { limit: 5 } },
        { endpoint: '/local/test-suites', params: { limit: 5 } },
        { endpoint: '/local/recent-runs', params: { limit: 10 } }
      ];

      const results = [];

      for (const test of performanceTests) {
        const flow = flowTracker.startFlow(`Performance Test: ${test.endpoint}`);

        try {
          const startTime = performance.now();

          const response = await request(testApp)
            .get(test.endpoint)
            .query(test.params)
            .expect(200);

          const endTime = performance.now();
          const totalDuration = flow.end();

          const result = {
            endpoint: test.endpoint,
            params: test.params,
            duration: totalDuration,
            responseSize: JSON.stringify(response.body).length,
            recordCount: response.body.count || response.body.data?.length || 0
          };

          results.push(result);

          console.log(`📊 ${test.endpoint}: ${totalDuration.toFixed(2)}ms (${result.recordCount} records)`);

        } catch (error) {
          flow.end();
          console.error(`❌ Performance test failed for ${test.endpoint}:`, error.message);
        }
      }

      // Analyze results
      const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
      const maxDuration = Math.max(...results.map(r => r.duration));
      const minDuration = Math.min(...results.map(r => r.duration));

      console.log('\n📈 Performance Analysis Summary:');
      console.log(`   Average Duration: ${avgDuration.toFixed(2)}ms`);
      console.log(`   Max Duration: ${maxDuration.toFixed(2)}ms`);
      console.log(`   Min Duration: ${minDuration.toFixed(2)}ms`);
      console.log(`   Performance Variance: ${((maxDuration - minDuration) / avgDuration * 100).toFixed(1)}%`);

      // Performance assertions
      expect(avgDuration).toBeLessThan(3000); // Average should be under 3 seconds
      expect(maxDuration).toBeLessThan(5000); // Max should be under 5 seconds

      console.log('✅ Performance analysis completed');

    }, TEST_CONFIG.timeout);
  });
});
