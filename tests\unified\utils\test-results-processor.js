/**
 * Test Results Processor for Unified Tests
 * 
 * This processor enhances the Jest test results with additional metadata:
 * - Database query performance metrics
 * - API response times
 * - Test flow tracking
 * - Error categorization
 * 
 * Used by the Jest configuration to process and enrich test reports
 */

const fs = require('fs');
const path = require('path');

/**
 * Process the test results and add additional metadata
 * @param {Object} testResults - The Jest test results object
 * @returns {Object} - The enhanced test results
 */
function processTestResults(testResults) {
  console.log(`\n📊 Processing test results for ${testResults.numTotalTests} tests`);
  
  // Track test execution metrics
  const metrics = {
    dbOperations: 0,
    apiCalls: 0,
    errorCategories: {},
    totalDuration: 0,
    avgTestDuration: 0
  };
  
  // Calculate execution metrics
  if (testResults.testResults && testResults.testResults.length > 0) {
    testResults.testResults.forEach(suite => {
      if (suite.testResults && suite.testResults.length > 0) {
        suite.testResults.forEach(test => {
          // Track test duration
          metrics.totalDuration += test.duration || 0;
          
          // Count database and API operations based on test titles
          if (test.title.toLowerCase().includes('database') || 
              test.title.toLowerCase().includes('db')) {
            metrics.dbOperations++;
          }
          
          if (test.title.toLowerCase().includes('api') || 
              test.title.toLowerCase().includes('endpoint')) {
            metrics.apiCalls++;
          }
          
          // Categorize failures
          if (test.status === 'failed' && test.failureMessages) {
            test.failureMessages.forEach(message => {
              let category = 'unknown';
              
              if (message.includes('TypeError')) {
                category = 'type_error';
              } else if (message.includes('database')) {
                category = 'database_error';
              } else if (message.includes('timeout')) {
                category = 'timeout';
              } else if (message.includes('network')) {
                category = 'network_error';
              } else if (message.includes('ECONNREFUSED')) {
                category = 'connection_refused';
              } else if (message.includes('expected') && message.includes('received')) {
                category = 'assertion_error';
              }
              
              metrics.errorCategories[category] = (metrics.errorCategories[category] || 0) + 1;
            });
          }
        });
      }
    });
    
    // Calculate average duration
    metrics.avgTestDuration = metrics.totalDuration / testResults.numTotalTests;
  }
  
  // Add metrics to test results
  testResults.metrics = metrics;
  
  // Log summary
  console.log(`✅ Tests completed: ${testResults.numPassedTests}/${testResults.numTotalTests} passed`);
  console.log(`❌ Failed tests: ${testResults.numFailedTests}`);
  console.log(`⏱️  Total duration: ${metrics.totalDuration.toFixed(2)}ms`);
  console.log(`⏱️  Average test duration: ${metrics.avgTestDuration.toFixed(2)}ms`);
  
  if (Object.keys(metrics.errorCategories).length > 0) {
    console.log('📋 Error categories:');
    Object.entries(metrics.errorCategories).forEach(([category, count]) => {
      console.log(`  - ${category}: ${count}`);
    });
  }
  
  // Save metrics to file for potential future analysis
  try {
    const metricsDir = path.join(__dirname, '..', 'coverage', 'metrics');
    if (!fs.existsSync(metricsDir)) {
      fs.mkdirSync(metricsDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const metricsFile = path.join(metricsDir, `test-metrics-${timestamp}.json`);
    
    fs.writeFileSync(
      metricsFile,
      JSON.stringify({ 
        timestamp,
        metrics,
        summary: {
          total: testResults.numTotalTests,
          passed: testResults.numPassedTests,
          failed: testResults.numFailedTests,
          duration: testResults.startTime && testResults.endTime ? 
            testResults.endTime - testResults.startTime : null
        }
      }, null, 2)
    );
  } catch (error) {
    console.error('Error saving metrics:', error);
  }
  
  return testResults;
}

module.exports = processTestResults;
