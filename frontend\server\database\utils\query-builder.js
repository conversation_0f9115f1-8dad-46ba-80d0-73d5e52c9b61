/**
 * Enhanced SQL Query Builder
 * Supports both programmatic and AI-driven query construction
 */
class QueryBuilder {
  constructor() {
    this.query = {
      type: 'SELECT',
      table: null,
      columns: ['*'],
      where: [],
      orderBy: null,
      limit: null,
      joins: [],
      groupBy: null,
      having: []
    };
  }
  
  /**
   * Select columns from a table
   * @param {string} table - Table name
   * @param {Array|string} columns - Columns to select
   * @returns {QueryBuilder} - This query builder
   */
  select(table, columns = ['*']) {
    this.query.type = 'SELECT';
    this.query.table = table;
    this.query.columns = Array.isArray(columns) ? columns : [columns];
    return this;
  }
  
  /**
   * Add a WHERE condition
   * @param {string} column - Column name
   * @param {string} operator - Comparison operator
   * @param {any} value - Value to compare
   * @returns {QueryBuilder} - This query builder
   */
  where(column, operator, value) {
    this.query.where.push({ column, operator, value });
    return this;
  }
  
  /**
   * Add a JOIN clause
   * @param {string} table - Table to join
   * @param {string} condition - Join condition
   * @param {string} type - Join type (INNER, LEFT, RIGHT)
   * @returns {QueryBuilder} - This query builder
   */
  join(table, condition, type = 'INNER') {
    this.query.joins.push({ table, condition, type });
    return this;
  }
  
  /**
   * Add a LEFT JOIN clause
   * @param {string} table - Table to join
   * @param {string} condition - Join condition
   * @returns {QueryBuilder} - This query builder
   */
  leftJoin(table, condition) {
    return this.join(table, condition, 'LEFT');
  }
  
  /**
   * Add a RIGHT JOIN clause
   * @param {string} table - Table to join
   * @param {string} condition - Join condition
   * @returns {QueryBuilder} - This query builder
   */
  rightJoin(table, condition) {
    return this.join(table, condition, 'RIGHT');
  }
  
  /**
   * Add an ORDER BY clause
   * @param {string} column - Column to order by
   * @param {string} direction - Sort direction (ASC or DESC)
   * @returns {QueryBuilder} - This query builder
   */
  orderBy(column, direction = 'ASC') {
    this.query.orderBy = { column, direction };
    return this;
  }
  
  /**
   * Add a GROUP BY clause
   * @param {string|Array} columns - Columns to group by
   * @returns {QueryBuilder} - This query builder
   */
  groupBy(columns) {
    this.query.groupBy = Array.isArray(columns) ? columns : [columns];
    return this;
  }
  
  /**
   * Add a HAVING clause
   * @param {string} condition - Having condition
   * @returns {QueryBuilder} - This query builder
   */
  having(condition) {
    this.query.having.push(condition);
    return this;
  }
  
  /**
   * Add a LIMIT clause
   * @param {number} limit - Maximum number of rows to return
   * @returns {QueryBuilder} - This query builder
   */
  limit(limit) {
    this.query.limit = limit;
    return this;
  }
  
  /**
   * Build the SQL query
   * @returns {Object} - SQL query and parameters
   */
  build() {
    const params = [];
    
    // Build SELECT clause
    let sql = `${this.query.type} ${this.query.columns.join(', ')} FROM ${this.query.table}`;
    
    // Add joins
    if (this.query.joins.length > 0) {
      sql += ' ' + this.query.joins.map(join => 
        `${join.type} JOIN ${join.table} ON ${join.condition}`
      ).join(' ');
    }
    
    // Add where conditions
    if (this.query.where.length > 0) {
      sql += ' WHERE ' + this.query.where.map(condition => {
        if (condition.value === null) {
          if (condition.operator.toUpperCase() === 'IS' || condition.operator.toUpperCase() === 'IS NOT') {
            return `${condition.column} ${condition.operator} NULL`;
          } else {
            return `${condition.column} IS NULL`;
          }
        } else {
          params.push(condition.value);
          return `${condition.column} ${condition.operator} ?`;
        }
      }).join(' AND ');
    }
    
    // Add group by
    if (this.query.groupBy) {
      sql += ` GROUP BY ${this.query.groupBy.join(', ')}`;
    }
    
    // Add having
    if (this.query.having.length > 0) {
      sql += ` HAVING ${this.query.having.join(' AND ')}`;
    }
    
    // Add order by
    if (this.query.orderBy) {
      sql += ` ORDER BY ${this.query.orderBy.column} ${this.query.orderBy.direction}`;
    }
    
    // Add limit
    if (this.query.limit) {
      sql += ' LIMIT ?';
      params.push(this.query.limit);
    }
    
    return { sql, params };
  }
  
  /**
   * Create a query builder from a structured query object
   * This allows AI to generate queries by providing a structured object
   * @param {Object} querySpec - Structured query specification
   * @returns {QueryBuilder} - New query builder instance
   */
  static fromSpec(querySpec) {
    const builder = new QueryBuilder();
    
    if (querySpec.type) builder.query.type = querySpec.type;
    if (querySpec.table) builder.query.table = querySpec.table;
    if (querySpec.columns) builder.query.columns = querySpec.columns;
    
    // Add where conditions
    if (querySpec.where && Array.isArray(querySpec.where)) {
      querySpec.where.forEach(condition => {
        builder.where(condition.column, condition.operator, condition.value);
      });
    }
    
    // Add joins
    if (querySpec.joins && Array.isArray(querySpec.joins)) {
      querySpec.joins.forEach(join => {
        builder.join(join.table, join.condition, join.type || 'INNER');
      });
    }
    
    // Add group by
    if (querySpec.groupBy) {
      builder.groupBy(querySpec.groupBy);
    }
    
    // Add having
    if (querySpec.having && Array.isArray(querySpec.having)) {
      querySpec.having.forEach(condition => {
        builder.having(condition);
      });
    }
    
    // Add order by
    if (querySpec.orderBy) {
      builder.orderBy(querySpec.orderBy.column, querySpec.orderBy.direction);
    }
    
    // Add limit
    if (querySpec.limit) {
      builder.limit(querySpec.limit);
    }
    
    return builder;
  }
  
  /**
   * Create a query builder from a natural language description
   * This is a placeholder for future AI integration
   * @param {string} description - Natural language query description
   * @returns {QueryBuilder} - New query builder instance
   */
  static fromDescription(description) {
    // This would be implemented when AI integration is added
    // For now, return a basic query builder
    console.log(`[AI] Query requested: ${description}`);
    return new QueryBuilder();
  }
}

module.exports = QueryBuilder;
