/**
 * Test Status Service
 * Handles retrieving and processing test status information
 */
const fetch = require('node-fetch');
const { isValidId } = require('../config/app-config');
const { getJsessionId } = require('./cookie-auth');

/**
 * Get the status of a test run
 * @param {string} tsnId - Test session ID
 * @param {string} uid - User ID
 * @param {string} password - Password
 * @returns {Promise<Object>} - Test status information
 */
async function getTestStatus(tsnId, uid, password) {
  if (!isValidId(tsnId)) {
    throw new Error('Missing or invalid tsn_id (test suite run ID) parameter.');
  }

  // Get a valid JSESSIONID cookie
  const jsessionId = await getJsessionId(uid, password);

  // Build the URL for the ReportSummary endpoint
  const url = `http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=${tsnId}`;

  // Make the request to the external API
  const response = await fetch(url, {
    headers: {
      'Cookie': `JSESSIONID=${jsessionId}`
    }
  });

  // Check if the response is OK
  if (!response.ok) {
    throw new Error(`Failed to fetch test status: ${response.status} ${response.statusText}`);
  }

  // Parse the HTML response
  const responseText = await response.text();

  // Extract status information from the HTML
  // This is a simplified example - in a real implementation, you would use a proper HTML parser
  const status = {};

  // Extract the test session ID
  status.tsn_id = tsnId;

  // Extract the status (PASS/FAIL)
  const statusMatch = responseText.match(/<span style='color:(red|green)'>(FAIL|PASS)<\/span>/i);
  if (statusMatch) {
    status.status = statusMatch[2].toLowerCase();
  } else {
    status.status = 'running'; // If no status found, assume it's still running
  }

  // Extract the start time
  const startTimeMatch = responseText.match(/Start Time: ([^<]+)/i);
  if (startTimeMatch) {
    status.start_time = startTimeMatch[1].trim();
  }

  // Extract the end time
  const endTimeMatch = responseText.match(/End Time: ([^<]+)/i);
  if (endTimeMatch) {
    status.end_time = endTimeMatch[1].trim();
  }

  // Extract the pass/fail counts
  const passCountMatch = responseText.match(/Case\(s\) passed: (\d+)/i);
  if (passCountMatch) {
    status.passed = parseInt(passCountMatch[1], 10);
  }

  const failCountMatch = responseText.match(/Case\(s\) failed: (\d+)/i);
  if (failCountMatch) {
    status.failed = parseInt(failCountMatch[1], 10);
  }

  // Calculate progress based on pass/fail counts
  if (status.passed !== undefined && status.failed !== undefined) {
    const total = status.passed + status.failed;
    status.progress = total > 0 ? Math.round((status.passed / total) * 100) : 0;
  } else {
    status.progress = 0;
  }

  return status;
}

module.exports = {
  getTestStatus
};
