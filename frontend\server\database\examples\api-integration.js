/**
 * Example of how to integrate the new database module with the API
 */
const express = require('express');
const router = express.Router();
const db = require('../index');
const { validateCredentials } = require('../../middleware/auth');

// Get test cases
router.get('/test-cases', validateCredentials, async (req, res) => {
  try {
    console.log('GET /local/test-cases');
    
    // Use the new database module to fetch test cases
    const testCases = await db.getTestCases(req.query);
    
    // Return as JSON with success flag
    return res.json({
      success: true,
      data: testCases || [],
      message: 'Test cases retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving test cases:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test cases',
      error: error.message
    });
  }
});

// Get test suites
router.get('/test-suites', validateCredentials, async (req, res) => {
  try {
    console.log('GET /local/test-suites');
    
    // Use the new database module to fetch test suites
    const testSuites = await db.getTestSuites(req.query);
    
    // Return as JSON with success flag
    return res.json({
      success: true,
      data: testSuites || [],
      message: 'Test suites retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving test suites:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test suites',
      error: error.message
    });
  }
});

// Get active tests
router.get('/active-tests', validateCredentials, async (req, res) => {
  try {
    console.log('GET /local/active-tests');
    
    // Use the new database module to fetch active tests
    const activeTests = await db.getActiveTests({
      userId: req.user.uid
    });
    
    // Return as JSON with success flag
    return res.json({
      success: true,
      data: activeTests || [],
      message: 'Active tests retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving active tests:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve active tests',
      error: error.message
    });
  }
});

// Get test results
router.get('/test-results/:tsnId', validateCredentials, async (req, res) => {
  try {
    const { tsnId } = req.params;
    console.log(`GET /local/test-results/${tsnId}`);
    
    // Use the new database module to fetch test results
    const testResults = await db.getTestResults(tsnId);
    
    // Return as JSON with success flag
    return res.json({
      success: true,
      data: testResults || {},
      message: 'Test results retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving test results:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test results',
      error: error.message
    });
  }
});

// Get recent runs
router.get('/recent-runs', validateCredentials, async (req, res) => {
  try {
    console.log('GET /local/recent-runs');
    
    // Use the new database module to fetch recent runs
    const recentRuns = await db.getRecentRuns(req.query);
    
    // Return as JSON with success flag
    return res.json({
      success: true,
      data: recentRuns || [],
      message: 'Recent runs retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving recent runs:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve recent runs',
      error: error.message
    });
  }
});

// AI-powered query endpoint (for future use)
router.post('/ai-query', validateCredentials, async (req, res) => {
  try {
    const { query } = req.body;
    
    if (!query) {
      return res.status(400).json({
        success: false,
        message: 'Query is required'
      });
    }
    
    console.log(`POST /local/ai-query: ${query}`);
    
    // Use the new database module to execute a natural language query
    const results = await db.executeNaturalLanguageQuery(query);
    
    // Return as JSON with success flag
    return res.json({
      success: true,
      data: results || [],
      message: 'Query executed successfully'
    });
  } catch (error) {
    console.error('Error executing AI query:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to execute query',
      error: error.message
    });
  }
});

module.exports = router;
