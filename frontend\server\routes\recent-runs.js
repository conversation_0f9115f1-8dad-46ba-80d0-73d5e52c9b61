/**
 * Recent Runs Routes
 */
const express = require('express');
const router = express.Router();
const db = require('../database');
const { validateCredentials } = require('../middleware/auth');

// Get recent test runs
router.get('/recent-runs', validateCredentials, async (req, res) => {
  try {
    console.log('GET /local/recent-runs');

    // Check if database is disabled
    if (process.env.DB_DISABLED === 'true') {
      console.log('Database is disabled, returning empty recent runs list');
      return res.json({
        success: true,
        data: [],
        message: 'Recent runs not available - database disabled'
      });
    }

    // Process query parameters
    const { limit, since_id, uid, time_range, tsn_id } = req.query;

    // Build filters object
    const filters = {};

    // Add filters if provided
    if (limit !== undefined) {
      // Handle the 'All' option (-1) by using a very large limit
      const parsedLimit = parseInt(limit, 10);
      filters.limit = parsedLimit === -1 ? 10000 : parsedLimit;
    }

    if (since_id !== undefined) {
      filters.since_id = parseInt(since_id, 10);
    }

    if (uid !== undefined) {
      filters.uid = uid;
    }

    if (time_range !== undefined) {
      filters.time_range = time_range;
    }

    if (tsn_id !== undefined) {
      filters.tsn_id = tsn_id;
    }

    console.log('Fetching recent runs with filters:', filters);

    // Use the database module to fetch recent runs with the high-level abstraction
    const recentRuns = await db.getRecentRuns(filters);

    console.log(`Retrieved ${recentRuns ? recentRuns.length : 0} recent runs`);

    // Return as JSON with success flag
    return res.json({
      success: true,
      data: recentRuns || [],
      message: 'Recent runs retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving recent runs:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve recent runs',
      error: error.message
    });
  }
});

module.exports = router;
