## Project Overview

*   **Type:** cursor_project_rules
*   **Description:** I'm enhancing an existing test automation framework built around a MySQL database. The framework is focused on improving usability and deployment capabilities so that both QA engineers and non-technical testers can easily execute tests and view comprehensive reports. It supports various test options such as smoke testing, full regression, feature-specific testing, and a daily heartbeat check. Additionally, the system leverages Azure’s Large Language Models (LLMs) for secure interactions and compliance with corporate policies.
*   **Primary Goal:** Enhance usability and deployment by enabling intuitive test execution and detailed reporting through secure, scalable Azure cloud services and intelligent LLM integration.

## Project Structure

### Framework-Specific Routing

*   **Directory Rules:**

    *   **Azure Functions v4:** Organize test execution endpoints under a dedicated directory (e.g. `functions/`) where each test type (smoke, regression, heartbeat, etc.) is exposed as an HTTP-triggered or timer-triggered function.
    *   **Azure App Services (MS Teams Integration):** Route chat commands from MS Teams to corresponding API endpoints using a proxy layer within the `app/` directory, ensuring that each command properly maps to its function endpoint.

### Core Directories

*   **Versioned Structure:**

    *   **functions/**: Contains Azure Functions code responsible for translating natural language commands into SQL-triggered tests and managing test execution.
    *   **app/**: Hosts components for the MS Teams chat interface, user-friendly dashboards, and real-time updates of test statuses.
    *   **storage/**: Holds configuration files, connection settings, and schema definitions for interacting with the MySQL database.
    *   **reports/**: Manages generated test reports and visualization assets, ensuring historical trends and results are clearly displayed.

### Key Files

*   **Stack-Versioned Patterns:**

    *   **functions/testTrigger/index.js:** Azure Functions entry point for initiating test execution based on incoming chat commands.
    *   **app/dashboard.js:** Frontend logic for displaying test statuses, detailed reports, and interactive visualizations via the MS Teams interface.

## Tech Stack Rules

*   **Version Enforcement:**

    *   **Azure Functions@v4:** Enforce the use of HTTP and timer triggered functions for handling individual test executions and scheduled tasks (e.g. daily heartbeat).
    *   **Azure App Services:** Ensure secure and scalable deployment of the frontend interface integrated with MS Teams, with robust authentication and network controls.

## PRD Compliance

*   **Non-Negotiable:**

    *   "LLM will be used to ensure secure interactions and compliance with corporate policies." This mandates that every test initiation, execution, and report generation passes through Azure LLM for secure processing, natural language interpretation, and intelligent test suggestions.

## App Flow Integration

*   **Stack-Aligned Flow:**

    *   Example: "MS Teams Chat Command → Azure Function Invocation → Test Execution & Report Generation via MySQL": The process begins with a user sending a command via MS Teams. The command is routed to the appropriate Azure Function, which then triggers SQL-based test execution. Upon completion, results are aggregated, visualized, and made available via the user dashboard and report viewer.

## Best Practices

*   **Azure Functions**

    *   Use HTTP triggered functions for clear API endpoints and modular test operations.
    *   Implement robust error handling and automated retries to manage transient failures.
    *   Leverage timer triggers for scheduled tests, such as the daily heartbeat.

*   **Azure App Services**

    *   Secure endpoints with strong authentication and network security rules.
    *   Optimize resource allocation with autoscaling features to handle variable loads.
    *   Ensure tight integration with MS Teams via webhook configurations and a custom bot framework.

*   **Azure Logic Apps**

    *   Structure workflows with clear triggers and actions to maintain reliable orchestration among services.
    *   Validate data integrity between downstream systems.
    *   Use modular components to enhance maintainability and scalability.

*   **Azure Storage**

    *   Enforce access controls using firewalls and encryption.
    *   Optimize performance through appropriate caching and indexing strategies.
    *   Maintain regular backups to secure critical configuration and report data.

*   **MS Teams**

    *   Design the conversation flow with clear command structures and adaptive cards for enhanced user interactions.
    *   Provide actionable feedback in case of command errors or system issues.
    *   Prioritize usability for both technical and non-technical users.

*   **ChatGPT, Claude, Cursor, Windsurf**

    *   Utilize AI models for intelligent code assistance and context-aware suggestions.
    *   Regularly update prompts to reflect evolving test scenarios and system requirements.
    *   Validate outputs against user expectations to ensure the correct interpretation of natural language commands.

*   **MySQL**

    *   Use optimized, structured queries and proper indexing to enhance performance.
    *   Secure data communications with encryption and robust connection pooling.
    *   Regularly optimize and back up databases to maintain data integrity and performance.

## Rules

*   Derive folder/file patterns directly from techStackDoc versions.
*   If Next.js 14 App Router is used: Enforce the use of an `app/` directory with nested route folders (not applicable in this project).
*   If Pages Router is used: Apply a flat `pages/*.tsx` structure (not applicable in this project).
*   Mirror this routing logic for other frameworks such as React Router or SvelteKit if applicable in future enhancements.
*   Never mix version patterns; ensure consistency by avoiding the use of mixed routing conventions (e.g., do not combine `app/` and `pages/` structures in the same project).
