/**
 * AI integration example for the database module
 */
const db = require('../index');
const { queryGenerator, contextProvider } = require('../ai');

async function main() {
  try {
    // Initialize the database connection
    console.log('Initializing database connection...');
    await db.init();
    
    // Get connection information
    const info = db.getConnectionInfo();
    console.log(`Connected to ${info.environment} environment`);
    
    // Get database schema
    console.log('\nGetting database schema...');
    const dbSchema = contextProvider.getDatabaseSchema();
    console.log('Database schema:');
    console.log(dbSchema.tables);
    
    // Generate a query from natural language
    console.log('\nGenerating query from natural language...');
    const description = 'Get all active test cases';
    const { sql, params } = await queryGenerator.generateQuery(description, dbSchema);
    console.log(`Generated SQL: ${sql}`);
    console.log(`Parameters: ${JSON.stringify(params)}`);
    
    // Execute the generated query
    console.log('\nExecuting generated query...');
    const results = await db.query(sql, params);
    console.log(`Retrieved ${results.length} results:`);
    console.log(results);
    
    // Use the executeNaturalLanguageQuery method
    console.log('\nUsing executeNaturalLanguageQuery method...');
    const nlResults = await db.executeNaturalLanguageQuery('Get all active test cases');
    console.log(`Retrieved ${nlResults.length} results:`);
    console.log(nlResults);
    
    // Close the connection
    console.log('\nClosing database connection...');
    await db.close();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the example
main();
