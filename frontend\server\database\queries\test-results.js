/**
 * Test Result Queries
 * Provides functions for querying test results
 */
const QueryBuilder = require('../utils/query-builder');
const formatter = require('../utils/result-formatter');

/**
 * Get test results for a specific test session
 * @param {Object} connection - Database connection
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Object>} - Test results
 */
async function getTestResults(connection, tsn_id) {
  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Base query
  queryBuilder.select('test_result r', [
    'r.tsn_id',
    'r.tc_id',
    'r.seq_index',
    'r.outcome',
    'r.creation_time',
    'o.txt'
  ]);
  queryBuilder.join('output o', 'r.cnt = o.cnt');
  queryBuilder.where('r.tsn_id', '=', tsn_id);
  queryBuilder.orderBy('r.creation_time', 'ASC');

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    const rows = await connection.query(sql, params);
    return formatter.formatTestResults(rows);
  } catch (error) {
    console.error(`Error getting test results for session ID ${tsn_id}:`, error);

    // Try a simpler fallback query if the main query fails
    try {
      console.log('Attempting fallback query for test results');
      const fallbackSql = `
        SELECT r.tsn_id, r.tc_id, r.seq_index, r.outcome, r.creation_time
        FROM test_result r
        WHERE r.tsn_id = ?
        ORDER BY r.creation_time ASC
      `;
      const fallbackRows = await connection.query(fallbackSql, [tsn_id]);

      // Create a simplified result without output data
      return {
        tsn_id: tsn_id,
        total_results: fallbackRows.length,
        pass_count: fallbackRows.filter(row => row.outcome === 'P').length,
        fail_count: fallbackRows.filter(row => row.outcome === 'F').length,
        results: []
      };
    } catch (fallbackError) {
      console.error('Fallback query also failed:', fallbackError);
      return {
        tsn_id: tsn_id,
        total_results: 0,
        pass_count: 0,
        fail_count: 0,
        results: []
      };
    }
  }
}

/**
 * Get test result summary for a specific test session
 * @param {Object} connection - Database connection
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Object>} - Test result summary
 */
async function getTestResultSummary(connection, tsn_id) {
  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Base query
  queryBuilder.select('test_result', [
    'tsn_id',
    'COUNT(*) AS total_results',
    'SUM(CASE WHEN outcome = \'P\' THEN 1 ELSE 0 END) AS pass_count',
    'SUM(CASE WHEN outcome = \'F\' THEN 1 ELSE 0 END) AS fail_count',
    'MIN(creation_time) AS start_time',
    'MAX(creation_time) AS end_time'
  ]);
  queryBuilder.where('tsn_id', '=', tsn_id);
  queryBuilder.groupBy('tsn_id');

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    const rows = await connection.query(sql, params);

    if (rows.length === 0) {
      return {
        tsn_id: tsn_id,
        total_results: 0,
        pass_count: 0,
        fail_count: 0,
        start_time: null,
        end_time: null,
        duration: '0:00'
      };
    }

    const summary = rows[0];

    // Calculate duration
    const duration = formatter.calculateDuration(summary.start_time, summary.end_time);

    return {
      tsn_id: summary.tsn_id,
      total_results: summary.total_results,
      pass_count: summary.pass_count,
      fail_count: summary.fail_count,
      start_time: summary.start_time,
      end_time: summary.end_time,
      duration
    };
  } catch (error) {
    console.error(`Error getting test result summary for session ID ${tsn_id}:`, error);
    return {
      tsn_id: tsn_id,
      total_results: 0,
      pass_count: 0,
      fail_count: 0,
      start_time: null,
      end_time: null,
      duration: '0:00'
    };
  }
}

/**
 * Get test case results for a specific test session
 * @param {Object} connection - Database connection
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Array>} - Test case results
 */
async function getTestCaseResults(connection, tsn_id) {
  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Base query
  queryBuilder.select('test_result', [
    'tc_id',
    'COUNT(*) AS step_count',
    'SUM(CASE WHEN outcome = \'P\' THEN 1 ELSE 0 END) AS pass_steps',
    'SUM(CASE WHEN outcome = \'F\' THEN 1 ELSE 0 END) AS fail_steps'
  ]);
  queryBuilder.where('tsn_id', '=', tsn_id);
  queryBuilder.groupBy('tc_id');
  queryBuilder.limit(100);

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    const rows = await connection.query(sql, params);

    return rows.map(row => ({
      tc_id: row.tc_id,
      step_count: row.step_count,
      pass_steps: row.pass_steps,
      fail_steps: row.fail_steps,
      status: row.fail_steps > 0 ? 'failed' : 'passed'
    }));
  } catch (error) {
    console.error(`Error getting test case results for session ID ${tsn_id}:`, error);
    return [];
  }
}

module.exports = {
  getTestResults,
  getTestResultSummary,
  getTestCaseResults
};
