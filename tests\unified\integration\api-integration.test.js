/**
 * Integration Tests for API Layer
 * 
 * Tests the integration between:
 * - Unified API Service
 * - External API Service
 * - Database Service
 * - Frontend modules
 * 
 * Migrated and enhanced from:
 * - frontend/reports/tests/
 * - frontend/server/tests/
 * - tests/integration/
 */

const { MockServiceFactory, TestDataGenerator, AssertionHelpers } = require('../mocks/test-helpers');
const apiResponses = require('../mocks/api-responses');

// Mock all external dependencies
jest.mock('../../../frontend/shared/services/unified-api-service');
jest.mock('../../../frontend/shared/services/external-api-service');
jest.mock('mysql2/promise');
jest.mock('axios');

describe('API Integration Tests', () => {
  let unifiedApiService;
  let externalApiService;
  let databaseService;

  beforeEach(() => {
    // Set up service mocks
    unifiedApiService = MockServiceFactory.createUnifiedApiService();
    externalApiService = MockServiceFactory.createExternalApiService();
    databaseService = MockServiceFactory.createDatabaseService();
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Test Data Flow Integration', () => {
    test('should integrate external API and database for test runs', async () => {
      // Mock external API response
      const externalRuns = apiResponses.externalApi.recentRuns;
      externalApiService.getRecentRuns.mockResolvedValue(apiResponses.success(externalRuns));
      
      // Mock database response
      const dbRuns = apiResponses.database.testRuns;
      databaseService.getTestRuns.mockResolvedValue(dbRuns);
      
      // Mock unified service combining both sources
      const combinedData = [...externalRuns, ...dbRuns];
      unifiedApiService.getTestRuns.mockResolvedValue(apiResponses.success(combinedData));

      // Test the integration
      const response = await unifiedApiService.getTestRuns();

      // Verify all services were called
      expect(unifiedApiService.getTestRuns).toHaveBeenCalledTimes(1);
      
      // Verify response structure
      AssertionHelpers.assertApiResponse(response, combinedData);
      expect(response.data.length).toBeGreaterThan(0);
      
      // Verify data contains both external and database sources
      expect(response.data.length).toBe(externalRuns.length + dbRuns.length);
    });

    test('should handle external API failure with database fallback', async () => {
      // Mock external API failure
      const externalError = apiResponses.error('External API unavailable', 503);
      externalApiService.getRecentRuns.mockRejectedValue(externalError);
      
      // Mock database success as fallback
      const dbRuns = apiResponses.database.testRuns;
      databaseService.getTestRuns.mockResolvedValue(dbRuns);
      unifiedApiService.getTestRuns.mockResolvedValue(apiResponses.success(dbRuns));

      // Test fallback behavior
      const response = await unifiedApiService.getTestRuns();

      // Verify fallback worked
      AssertionHelpers.assertApiResponse(response, dbRuns);
      expect(response.data).toEqual(dbRuns);
    });

    test('should handle database failure with external API fallback', async () => {
      // Mock database failure
      const dbError = new Error('Database connection failed');
      databaseService.getTestRuns.mockRejectedValue(dbError);
      
      // Mock external API success as fallback
      const externalRuns = apiResponses.externalApi.recentRuns;
      externalApiService.getRecentRuns.mockResolvedValue(apiResponses.success(externalRuns));
      unifiedApiService.getTestRuns.mockResolvedValue(apiResponses.success(externalRuns));

      // Test fallback behavior
      const response = await unifiedApiService.getTestRuns();

      // Verify fallback worked
      AssertionHelpers.assertApiResponse(response, externalRuns);
      expect(response.data).toEqual(externalRuns);
    });
  });

  describe('Authentication Integration', () => {
    test('should authenticate and access protected resources', async () => {
      // Mock authentication
      const authResponse = apiResponses.externalApi.authResponse;
      externalApiService.authenticate.mockResolvedValue(apiResponses.success(authResponse));
      
      // Mock authenticated request
      const protectedData = TestDataGenerator.testRuns(3);
      unifiedApiService.getTestRuns.mockResolvedValue(apiResponses.success(protectedData));

      // Test authentication flow
      const authResult = await externalApiService.authenticate('testuser', 'password');
      expect(authResult.data.success).toBe(true);
      expect(authResult.data.token).toBeDefined();

      // Test accessing protected resource
      const dataResult = await unifiedApiService.getTestRuns();
      AssertionHelpers.assertApiResponse(dataResult, protectedData);
    });

    test('should handle authentication failure in protected requests', async () => {
      // Mock authentication failure
      const authError = apiResponses.error('Invalid credentials', 401);
      externalApiService.authenticate.mockRejectedValue(authError);

      // Test authentication failure
      await expect(externalApiService.authenticate('invalid', 'credentials'))
        .rejects.toMatchObject({
          response: {
            status: 401
          }
        });
    });
  });

  describe('Data Consistency Integration', () => {
    test('should maintain data consistency across services', async () => {
      const testId = '3180';
      
      // Mock external API details
      const externalDetails = apiResponses.externalApi.testDetails;
      externalApiService.getTestDetails.mockResolvedValue(apiResponses.success(externalDetails));
      
      // Mock database test cases for the same test
      const dbTestCases = TestDataGenerator.testCases(5, testId);
      databaseService.getTestCases.mockResolvedValue(dbTestCases);
      
      // Mock unified service combining both
      const combinedDetails = {
        ...externalDetails,
        test_cases: dbTestCases
      };
      unifiedApiService.getTestDetails.mockResolvedValue(apiResponses.success(combinedDetails));

      // Test data consistency
      const response = await unifiedApiService.getTestDetails(testId);

      // Verify data structure
      expect(response.data.test_id).toBe(testId);
      expect(response.data.test_cases).toBeDefined();
      expect(Array.isArray(response.data.test_cases)).toBe(true);
      expect(response.data.test_cases.length).toBe(5);
      
      // Verify all test cases belong to the correct test
      response.data.test_cases.forEach(testCase => {
        expect(testCase.test_run_id).toBe(testId);
      });
    });

    test('should handle data synchronization conflicts', async () => {
      // Mock conflicting data from different sources
      const externalStatus = 'Passed';
      const dbStatus = 'Failed';
      
      const externalDetails = { ...apiResponses.externalApi.testDetails, status: externalStatus };
      const dbDetails = { ...apiResponses.database.testCases[0], status: dbStatus };
      
      externalApiService.getTestDetails.mockResolvedValue(apiResponses.success(externalDetails));
      databaseService.getTestCases.mockResolvedValue([dbDetails]);
      
      // Mock unified service resolving conflict (external takes precedence)
      const resolvedDetails = { ...externalDetails, db_status: dbStatus };
      unifiedApiService.getTestDetails.mockResolvedValue(apiResponses.success(resolvedDetails));

      const response = await unifiedApiService.getTestDetails('3180');

      // Verify conflict resolution
      expect(response.data.status).toBe(externalStatus);
      expect(response.data.db_status).toBe(dbStatus);
    });
  });

  describe('Performance Integration', () => {
    test('should handle concurrent requests efficiently', async () => {
      // Mock multiple concurrent requests
      const requests = Array.from({ length: 5 }, (_, i) => {
        const mockData = TestDataGenerator.testRuns(2, { environment: `QA0${i + 1}` });
        unifiedApiService.getTestRuns.mockResolvedValueOnce(apiResponses.success(mockData));
        return unifiedApiService.getTestRuns({ environment: `QA0${i + 1}` });
      });

      // Execute concurrent requests
      const responses = await Promise.all(requests);

      // Verify all requests completed successfully
      expect(responses).toHaveLength(5);
      responses.forEach(response => {
        AssertionHelpers.assertApiResponse(response);
        expect(response.data).toHaveLength(2);
      });
    });

    test('should handle request timeouts gracefully', async () => {
      // Mock timeout error
      const timeoutError = new Error('Request timeout');
      timeoutError.code = 'ECONNABORTED';
      unifiedApiService.getTestRuns.mockRejectedValue(timeoutError);

      // Test timeout handling
      await expect(unifiedApiService.getTestRuns()).rejects.toThrow('Request timeout');
    });
  });

  describe('Error Propagation Integration', () => {
    test('should propagate errors correctly through service layers', async () => {
      // Mock error at database layer
      const dbError = new Error('Database connection lost');
      databaseService.getTestRuns.mockRejectedValue(dbError);
      
      // Mock unified service propagating the error
      unifiedApiService.getTestRuns.mockRejectedValue(dbError);

      // Test error propagation
      await expect(unifiedApiService.getTestRuns()).rejects.toThrow('Database connection lost');
    });

    test('should handle partial failures gracefully', async () => {
      // Mock partial success scenario
      const externalError = apiResponses.error('External API timeout', 408);
      externalApiService.getRecentRuns.mockRejectedValue(externalError);
      
      // Mock database success
      const dbRuns = apiResponses.database.testRuns;
      databaseService.getTestRuns.mockResolvedValue(dbRuns);
      
      // Mock unified service handling partial failure
      const partialResponse = {
        data: dbRuns,
        warnings: ['External API unavailable, showing database data only'],
        partial: true
      };
      unifiedApiService.getTestRuns.mockResolvedValue(apiResponses.success(partialResponse));

      const response = await unifiedApiService.getTestRuns();

      // Verify partial success handling
      expect(response.data.partial).toBe(true);
      expect(response.data.warnings).toContain('External API unavailable, showing database data only');
      expect(response.data.data).toEqual(dbRuns);
    });
  });
});
