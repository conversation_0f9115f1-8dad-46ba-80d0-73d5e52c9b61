/**
 * Setup file for Node.js tests
 * 
 * This file sets up the environment for running tests in Node.js.
 */

// Mock DOM
const { JSDOM } = require('jsdom');
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
global.window = dom.window;
global.document = dom.window.document;
global.HTMLElement = dom.window.HTMLElement;
global.Element = dom.window.Element;

// Mock DOM elements
global.elements = {
  reportsTable: document.createElement('tbody'),
  testDetailsSection: document.createElement('div'),
  testDetailsTitle: document.createElement('h3'),
  testDetailsInfo: document.createElement('div'),
  testCasesTable: document.createElement('tbody')
};

// Add elements to the document
document.body.appendChild(elements.reportsTable);
document.body.appendChild(elements.testDetailsSection);
document.body.appendChild(elements.testDetailsTitle);
document.body.appendChild(elements.testDetailsInfo);
document.body.appendChild(elements.testCasesTable);

// Mock current state
global.currentState = {
  reports: [],
  currentTestDetails: null
};

// Mock config
global.config = {
  reportingEndpoint: '/local/recent-runs',
  testDetailsEndpoint: '/local/test-details',
  refreshInterval: 10000,
  useDirectExternalApi: true,
  externalApiBaseUrl: 'http://mprts-qa02.lab.wagerworks.com:9080',
  maxReportsToShow: 20
};

// Mock helper functions
global.formatDate = function(dateString) {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleString();
};

global.getStatusBadgeClass = function(status) {
  switch (status ? status.toLowerCase() : '') {
    case 'success':
    case 'passed':
      return 'badge-success';
    case 'failed':
    case 'error':
      return 'badge-danger';
    case 'running':
      return 'badge-primary';
    case 'warning':
      return 'badge-warning';
    default:
      return 'badge-secondary';
  }
};

global.updateCharts = function() {
  // Mock implementation
};

// Mock services
class ExternalApiService {
  constructor() {
    this.baseUrl = 'http://mprts-qa02.lab.wagerworks.com:9080';
    this.jsessionId = null;
    this.jsessionExpiry = null;
    console.log(`External API Service initialized with baseUrl: ${this.baseUrl}`);
  }

  isSessionValid() {
    return this.jsessionId !== null && 
           this.jsessionExpiry !== null && 
           this.jsessionExpiry > Date.now();
  }

  async login(uid, password) {
    this.jsessionId = '58A7C523CB9EE2B9F6C822C475C74139';
    this.jsessionExpiry = Date.now() + 30 * 60 * 1000;
    return this.jsessionId;
  }

  async getValidSession(uid, password) {
    if (this.isSessionValid()) {
      return this.jsessionId;
    }
    return await this.login(uid, password);
  }

  async makeAuthenticatedRequest(path, params, uid, password, method = 'GET') {
    return {
      ok: true,
      text: async () => '<html><span style="color:green">PASS</span></html>'
    };
  }

  async getReportSummary(tsnId, uid, password) {
    return {
      tsn_id: tsnId,
      test_id: '3180',
      type: 'Test Case',
      environment: 'QA02',
      status: 'Success',
      start_time: '2023-01-01 12:00:00',
      end_time: '2023-01-01 12:05:00',
      duration: '5:00',
      total_cases: 10,
      passed_cases: 10,
      failed_cases: 0,
      pass_rate: 100
    };
  }

  async getReportDetails(tsnId, index, uid, password) {
    return {
      tsn_id: tsnId,
      test_cases: [
        {
          tc_id: '3180',
          seq_index: '1',
          status: 'Passed',
          description: 'Login to the system',
          input_output: 'Input: username=test, password=test\nOutput: Login successful',
          error_message: ''
        }
      ],
      pagination: {
        currentPage: 1,
        totalPages: 1
      }
    };
  }

  async stopTestSession(tsnId, uid, password) {
    return true;
  }

  async getRecentTestRuns(sessionIds, uid, password, limit = 10) {
    return sessionIds.slice(0, limit).map(tsnId => ({
      tsn_id: tsnId,
      test_id: '3180',
      type: 'Test Case',
      environment: 'QA02',
      status: 'Success',
      start_time: '2023-01-01 12:00:00',
      end_time: '2023-01-01 12:05:00',
      duration: '5:00',
      total_cases: 10,
      passed_cases: 10,
      failed_cases: 0,
      pass_rate: 100
    }));
  }

  parseReportSummaryHtml(html, tsnId) {
    return {
      tsn_id: tsnId,
      test_id: '3180',
      type: 'Test Case',
      environment: 'QA02',
      status: 'Success',
      start_time: '2023-01-01 12:00:00',
      end_time: '2023-01-01 12:05:00',
      duration: '5:00',
      total_cases: 10,
      passed_cases: 10,
      failed_cases: 0,
      pass_rate: 100
    };
  }

  parseReportDetailsHtml(html, tsnId) {
    return {
      tsn_id: tsnId,
      test_cases: [
        {
          tc_id: '3180',
          seq_index: '1',
          status: 'Passed',
          description: 'Login to the system',
          input_output: 'Input: username=test, password=test\nOutput: Login successful',
          error_message: ''
        }
      ],
      pagination: {
        currentPage: 1,
        totalPages: 1
      }
    };
  }

  extractTextFromCell(cell) {
    return cell ? cell.textContent || '' : '';
  }
}

class SessionIdService {
  constructor() {
    this.cacheKey = 'smarttest_recent_session_ids';
    this.cacheTtl = 5 * 60 * 1000; // 5 minutes
    this.fallbackIds = ['13782', '13781', '13780', '13779', '13778'];
  }

  async getRecentSessionIds(credentials, limit = 10) {
    return this.fallbackIds.slice(0, limit);
  }

  getCachedSessionIds() {
    return null;
  }

  cacheSessionIds(ids) {
    // Mock implementation
  }

  async getSessionIdsFromApi(credentials, limit = 10) {
    return this.fallbackIds.slice(0, limit);
  }
}

// Attach services to window
global.window.externalApiService = new ExternalApiService();
global.window.sessionIdService = new SessionIdService();

// Mock sessionStorage
global.window.sessionStorage = {
  getItem: function(key) {
    if (key === 'smarttest_uid') return '<EMAIL>';
    if (key === 'smarttest_pwd') return 'test123';
    return null;
  },
  setItem: function() {},
  removeItem: function() {}
};

// Import reports.js functions
const {
  loadReportsData,
  loadReportsFromExternalApi,
  loadReportsFromDatabaseApi,
  loadTestDetails,
  loadTestDetailsFromExternalApi,
  loadTestDetailsFromDatabaseApi,
  updateReportsTable,
  displayTestDetails,
  updateTestCasesTable
} = require('./mocks/reports.js');

// Attach reports.js functions to global scope
global.loadReportsData = loadReportsData;
global.loadReportsFromExternalApi = loadReportsFromExternalApi;
global.loadReportsFromDatabaseApi = loadReportsFromDatabaseApi;
global.loadTestDetails = loadTestDetails;
global.loadTestDetailsFromExternalApi = loadTestDetailsFromExternalApi;
global.loadTestDetailsFromDatabaseApi = loadTestDetailsFromDatabaseApi;
global.updateReportsTable = updateReportsTable;
global.displayTestDetails = displayTestDetails;
global.updateTestCasesTable = updateTestCasesTable;
