/**
 * SSH Tunnel Connection
 * Implements database access through an SSH tunnel
 */
const { Client } = require('ssh2');
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const net = require('net');

class SshTunnelConnection {
  constructor(config) {
    this.config = config;
    this.sshClient = null;
    this.mysqlPool = null;
    this.server = null;
    this.localPort = null;
    this.isConnected = false;
    this.debugMode = process.env.DB_DEBUG === 'true';
  }

  /**
   * Log debug information if debug mode is enabled
   * @param {string} message - Message to log
   */
  log(message) {
    if (this.debugMode) {
      console.log(`[SSHTunnel] ${message}`);
    }
  }

  /**
   * Initialize the connection
   * @returns {Promise<boolean>} - True if initialization was successful
   */
  async init() {
    try {
      this.log('Initializing SSH tunnel connection...');

      // Create SSH client
      this.sshClient = new Client();

      // Set up SSH tunnel
      await this._setupSshTunnel();

      // Create MySQL connection pool
      await this._createMysqlPool();

      // Test MySQL connectivity
      await this._testMysqlConnectivity();

      this.isConnected = true;
      this.log('SSH tunnel connection initialized successfully');
      return true;
    } catch (error) {
      console.error(`Failed to initialize SSH tunnel connection: ${error.message}`);

      // Clean up any partial connections
      await this._cleanup();

      this.isConnected = false;
      throw error;
    }
  }

  /**
   * Set up SSH tunnel
   * @returns {Promise<void>}
   */
  async _setupSshTunnel() {
    return new Promise((resolve, reject) => {
      // Get SSH configuration
      const sshHost = this.config.SSH_HOST;
      const sshPort = parseInt(this.config.SSH_PORT || '22', 10);
      const sshUsername = this.config.SSH_USER;
      const sshKeyPath = this.config.SSH_KEY_PATH;

      // Get database configuration
      const dbHost = this.config.DB_HOST;
      const dbPort = parseInt(this.config.DB_PORT || '3306', 10);

      // Validate SSH configuration
      if (!sshHost) {
        return reject(new Error('SSH_HOST is required'));
      }

      if (!sshUsername) {
        return reject(new Error('SSH_USER is required'));
      }

      // Prepare SSH configuration
      const sshConfig = {
        host: sshHost,
        port: sshPort,
        username: sshUsername,
        readyTimeout: 30000, // 30 seconds
        algorithms: {
          kex: [
            'diffie-hellman-group1-sha1',
            'diffie-hellman-group14-sha1',
            'diffie-hellman-group-exchange-sha1',
            'diffie-hellman-group-exchange-sha256',
            'ecdh-sha2-nistp256',
            'ecdh-sha2-nistp384',
            'ecdh-sha2-nistp521',
            '<EMAIL>'
          ],
          cipher: [
            'aes128-ctr',
            'aes192-ctr',
            'aes256-ctr',
            'aes128-gcm',
            '<EMAIL>',
            'aes256-gcm',
            '<EMAIL>',
            'aes256-cbc',
            'aes192-cbc',
            'aes128-cbc',
            '3des-cbc'
          ],
          serverHostKey: [
            'ssh-rsa',
            'ssh-dss',
            'ecdsa-sha2-nistp256',
            'ecdsa-sha2-nistp384',
            'ecdsa-sha2-nistp521',
            'rsa-sha2-256',
            'rsa-sha2-512'
          ],
          hmac: [
            'hmac-sha2-256',
            'hmac-sha2-512',
            'hmac-sha1',
            'hmac-md5',
            'hmac-sha2-256-96',
            'hmac-sha2-512-96',
            'hmac-sha1-96',
            'hmac-md5-96'
          ]
        }
      };

      // Add private key if available
      if (sshKeyPath && fs.existsSync(sshKeyPath)) {
        this.log(`Using SSH key: ${sshKeyPath}`);
        sshConfig.privateKey = fs.readFileSync(sshKeyPath);
      } else {
        console.warn(`SSH key not found at ${sshKeyPath}, falling back to password authentication`);
        // Use password authentication as fallback
        sshConfig.password = this.config.SSH_PASSWORD || 'password';
      }

      // Connect to SSH server
      this.sshClient.on('ready', () => {
        this.log(`SSH connection established to ${sshHost}:${sshPort}`);

        // Create a server on localhost that will forward connections to the database server
        this.server = net.createServer((socket) => {
          this.log('New connection to tunnel');

          // Forward connection to database server
          this.sshClient.forwardOut(
            socket.remoteAddress,
            socket.remotePort,
            dbHost,
            dbPort,
            (err, stream) => {
              if (err) {
                socket.end();
                return;
              }

              // Pipe data between socket and stream
              socket.pipe(stream);
              stream.pipe(socket);

              // Handle socket events
              socket.on('error', (err) => {
                this.log(`Socket error: ${err.message}`);
                stream.end();
              });

              // Handle stream events
              stream.on('error', (err) => {
                this.log(`Stream error: ${err.message}`);
                socket.end();
              });
            }
          );
        });

        // Listen on a random port
        this.server.listen(0, '127.0.0.1', () => {
          this.localPort = this.server.address().port;
          this.log(`SSH tunnel established on port ${this.localPort}`);
          resolve();
        });

        // Handle server errors
        this.server.on('error', (err) => {
          reject(new Error(`Server error: ${err.message}`));
        });
      });

      this.sshClient.on('error', (err) => {
        reject(new Error(`SSH connection error: ${err.message}`));
      });

      this.log(`Connecting to SSH server ${sshHost}:${sshPort} as ${sshUsername}...`);
      this.sshClient.connect(sshConfig);
    });
  }

  /**
   * Create MySQL connection pool
   * @returns {Promise<void>}
   */
  async _createMysqlPool() {
    this.log('Creating MySQL connection pool...');

    // Get database configuration
    const dbUser = this.config.DB_USER || 'rgs_rw';
    const dbPassword = this.config.DB_PASSWORD || 'rgs_rw';
    const dbName = this.config.DB_NAME || 'rgs_test';

    // Create connection pool
    this.mysqlPool = mysql.createPool({
      host: '127.0.0.1',
      port: this.localPort,
      user: dbUser,
      password: dbPassword,
      database: dbName,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
      connectTimeout: 10000 // 10 seconds
    });

    this.log('MySQL connection pool created');
  }

  /**
   * Test MySQL connectivity
   * @returns {Promise<void>}
   */
  async _testMysqlConnectivity() {
    this.log('Testing MySQL connectivity...');

    try {
      // Execute a simple query to test connectivity
      const [rows] = await this.mysqlPool.execute('SELECT 1 AS connection_test');

      if (rows.length > 0 && rows[0].connection_test === 1) {
        this.log('MySQL connectivity test successful');
      } else {
        throw new Error('MySQL connectivity test failed: unexpected result');
      }
    } catch (error) {
      throw new Error(`MySQL connectivity test failed: ${error.message}`);
    }
  }

  /**
   * Execute a SQL query
   * @param {string} sql - SQL query
   * @param {Array} params - Query parameters
   * @returns {Promise<Array>} - Query results
   */
  async query(sql, params = []) {
    if (!this.isConnected || !this.mysqlPool) {
      throw new Error('Not connected to MySQL server');
    }

    this.log(`Executing query: ${sql}`);

    try {
      // Execute the query
      const [rows] = await this.mysqlPool.execute(sql, params);
      return rows;
    } catch (error) {
      throw new Error(`Query error: ${error.message}`);
    }
  }

  /**
   * Clean up resources
   * @returns {Promise<void>}
   */
  async _cleanup() {
    // Close MySQL connection pool
    if (this.mysqlPool) {
      try {
        await this.mysqlPool.end();
      } catch (error) {
        console.error(`Error closing MySQL connection pool: ${error.message}`);
      }

      this.mysqlPool = null;
    }

    // Close server
    if (this.server) {
      try {
        this.server.close();
      } catch (error) {
        console.error(`Error closing server: ${error.message}`);
      }

      this.server = null;
    }

    // Close SSH client
    if (this.sshClient) {
      try {
        this.sshClient.end();
      } catch (error) {
        console.error(`Error closing SSH client: ${error.message}`);
      }

      this.sshClient = null;
    }
  }

  /**
   * Close the connection
   * @returns {Promise<void>}
   */
  async close() {
    this.log('Closing SSH tunnel connection...');

    await this._cleanup();

    this.isConnected = false;
    this.log('SSH tunnel connection closed');
  }
}

module.exports = SshTunnelConnection;
