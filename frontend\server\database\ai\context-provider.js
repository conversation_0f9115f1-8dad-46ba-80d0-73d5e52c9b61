/**
 * Database Context Provider for AI
 * Provides database schema and context information to AI components
 */

/**
 * Get database schema information
 * @returns {Object} - Database schema information
 */
function getDatabaseSchema() {
  // This would be implemented to provide schema information to AI
  return {
    tables: {
      test_case: {
        columns: ['tc_id', 'uid', 'status', 'case_driver', 'tp_id', 'comments', 'tickets', 'name'],
        primaryKey: 'tc_id'
      },
      test_case_group: {
        columns: ['tcg_id', 'ts_id', 'tc_id', 'seq_index', 'uid', 'status', 'pj_id', 'name', 'comments', 'tickets', 'tag'],
        primaryKey: 'tcg_id'
      },
      test_result: {
        columns: ['cnt', 'tsn_id', 'tc_id', 'seq_index', 'outcome', 'creation_time'],
        primaryKey: 'cnt'
      },
      test_session: {
        columns: ['tsn_id', 'tc_id', 'uid', 'start_ts', 'end_ts'],
        primaryKey: 'tsn_id'
      },
      output: {
        columns: ['cnt', 'txt'],
        primaryKey: 'cnt'
      }
    },
    relationships: [
      { from: 'test_case_group', fromColumn: 'tc_id', to: 'test_case', toColumn: 'tc_id' },
      { from: 'test_result', fromColumn: 'tc_id', to: 'test_case', toColumn: 'tc_id' },
      { from: 'test_result', fromColumn: 'cnt', to: 'output', toColumn: 'cnt' }
    ]
  };
}

/**
 * Get common query patterns
 * @returns {Object} - Common query patterns
 */
function getQueryPatterns() {
  return {
    getTestCases: {
      description: 'Get test cases with optional filtering',
      template: 'SELECT tc_id, name, status FROM test_case WHERE {conditions} LIMIT {limit}'
    },
    getTestSuites: {
      description: 'Get test suites with optional filtering',
      template: 'SELECT ts_id, name, status FROM test_case_group WHERE {conditions} LIMIT {limit}'
    },
    getActiveTests: {
      description: 'Get active test sessions',
      template: 'SELECT tsn_id, tc_id, uid, start_ts FROM test_session WHERE end_ts IS NULL LIMIT {limit}'
    },
    getTestResults: {
      description: 'Get test results for a specific test session',
      template: 'SELECT r.tsn_id, r.tc_id, r.outcome, r.creation_time, o.txt FROM test_result r JOIN output o ON r.cnt = o.cnt WHERE r.tsn_id = {tsn_id}'
    }
  };
}

module.exports = {
  getDatabaseSchema,
  getQueryPatterns
};
