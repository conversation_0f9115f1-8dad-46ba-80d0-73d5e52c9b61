/**
 * Tests for API and database integration
 *
 * These tests verify that the API and database layers work together correctly,
 * with parameters being passed correctly between them.
 */

const request = require('supertest');

// Create a mock database module that tracks parameter passing
const mockDb = {
  init: jest.fn().mockResolvedValue(),
  close: jest.fn().mockResolvedValue(),
  getTestCases: jest.fn().mockImplementation(async (filters) => {
    // Return mock data based on filters
    return [
      { tc_id: 1, name: 'Test Case 1', status: filters.status || 'active' },
      { tc_id: 2, name: 'Test Case 2', status: filters.status || 'inactive' }
    ];
  }),
  getTestSuites: jest.fn().mockImplementation(async (filters) => {
    // Return mock data based on filters
    return [
      { ts_id: 101, name: 'Test Suite 1', uid: filters.uid || 'user1' },
      { ts_id: 102, name: 'Test Suite 2', uid: filters.uid || 'user2' }
    ];
  }),
  getActiveTests: jest.fn().mockImplementation(async (filters) => {
    // Return mock data based on filters
    return [
      { tsn_id: 1001, tc_id: 1, uid: filters.uid || 'user1', start_ts: '2023-01-01' },
      { tsn_id: 1002, tc_id: 2, uid: filters.uid || 'user2', start_ts: '2023-01-02' }
    ];
  }),
  getTestResults: jest.fn().mockImplementation(async (tsn_id) => {
    // Return mock data based on tsn_id
    return [
      { tsn_id, tc_id: 1, outcome: 'pass' },
      { tsn_id, tc_id: 2, outcome: 'fail' }
    ];
  }),
  getTestSessionDetails: jest.fn().mockImplementation(async (tsn_id) => {
    // Return mock data based on tsn_id
    return {
      tsn_id,
      tc_id: 1,
      uid: 'user1',
      start_ts: '2023-01-01',
      end_ts: '2023-01-02'
    };
  }),
  searchTestCases: jest.fn().mockImplementation(async (criteria) => {
    // Return mock data based on criteria
    return [
      { tc_id: 1, name: criteria.name || 'Test Case 1', status: criteria.status || 'active' },
      { tc_id: 2, name: criteria.name || 'Test Case 2', status: criteria.status || 'inactive' }
    ];
  }),
  getTestResultSummary: jest.fn().mockImplementation(async (tsn_id) => {
    // Return mock data based on tsn_id
    return {
      tsn_id,
      total_cases: 2,
      passed_cases: 1,
      failed_cases: 1,
      skipped_cases: 0
    };
  }),
  getTestCaseResults: jest.fn().mockImplementation(async (tsn_id) => {
    // Return mock data based on tsn_id
    return [
      { tsn_id, tc_id: 1, outcome: 'pass', duration: 10 },
      { tsn_id, tc_id: 2, outcome: 'fail', duration: 15 }
    ];
  })
};

// Mock the database module
jest.mock('../database', () => mockDb);

// Mock the case-runner service
jest.mock('../services/case-runner', () => ({
  runTest: jest.fn().mockImplementation(async (params) => {
    return {
      tsn_id: '1001',
      message: `Test ${params.tc_id || params.ts_id} started successfully`
    };
  })
}));

// Mock the authentication middleware
jest.mock('../middleware/auth', () => ({
  validateCredentials: (req, res, next) => {
    // Add user object to request
    req.user = { uid: req.body.uid || req.query.uid || 'test_user' };
    next();
  }
}));

// Load the API after the mocks are set up
const app = require('../api');

describe('API and Database Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Test the complete flow from API to database and back
  describe('Complete API to Database Flow', () => {
    test('GET /local/test-cases should pass parameters correctly to database', async () => {
      // Make request with parameters
      const response = await request(app)
        .get('/local/test-cases')
        .query({ ts_id: 101, status: 'active', limit: 10 });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBe(2);
      expect(response.body.data[0]).toHaveProperty('tc_id', 1);

      // Verify database function was called with correct parameters
      expect(mockDb.getTestCases).toHaveBeenCalledWith(expect.objectContaining({
        ts_id: '101',
        status: 'active',
        limit: '10'
      }));
    });

    test('GET /local/test-suites should pass parameters correctly to database', async () => {
      // Make request with parameters
      const response = await request(app)
        .get('/local/test-suites')
        .query({ uid: 'user1', name: 'Test Suite', limit: 10 });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBe(2);
      expect(response.body.data[0]).toHaveProperty('ts_id', 101);

      // Verify database function was called with correct parameters
      expect(mockDb.getTestSuites).toHaveBeenCalledWith(expect.objectContaining({
        uid: 'user1',
        name: 'Test Suite',
        limit: '10'
      }));
    });

    test('GET /local/active-tests should pass parameters correctly to database', async () => {
      // Make request with parameters
      const response = await request(app)
        .get('/local/active-tests')
        .query({ uid: 'user1', limit: 5 });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBe(2);
      expect(response.body.data[0]).toHaveProperty('tsn_id', 1001);

      // Verify database function was called with correct parameters
      expect(mockDb.getActiveTests).toHaveBeenCalledWith(expect.objectContaining({
        uid: 'user1',
        limit: '5'
      }));
    });

    test('GET /api/test-reports/:tsn_id should pass tsn_id parameter correctly to database', async () => {
      // Make request with tsn_id parameter
      const response = await request(app)
        .get('/api/test-reports/1001');

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('reports');

      // Verify database functions were called with correct parameter
      expect(mockDb.getTestResults).toHaveBeenCalledWith('1001');
    });

    test('POST /api/case-runner should pass parameters correctly to service', async () => {
      // Make request with parameters
      const response = await request(app)
        .post('/api/case-runner')
        .send({
          uid: 'user1',
          password: 'password',
          tc_id: 1,
          envir: 'qa02',
          shell_host: 'test-host'
        });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('tsn_id', '1001');

      // Verify service function was called with correct parameters
      const caseRunnerService = require('../services/case-runner');
      expect(caseRunnerService.runTest).toHaveBeenCalledWith(expect.objectContaining({
        tc_id: 1,
        uid: 'user1',
        envir: 'qa02',
        shell_host: 'test-host'
      }));
    });

    test('GET /api/test-reports/:tsn_id/summary should pass tsn_id parameter correctly to database', async () => {
      // Make request with tsn_id parameter
      const response = await request(app)
        .get('/api/test-reports/1001/summary');

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('summary');
      expect(response.body.summary).toHaveProperty('tsn_id', '1001');
      expect(response.body.summary).toHaveProperty('total_cases', 2);

      // Verify database function was called with correct parameter
      expect(mockDb.getTestResultSummary).toHaveBeenCalledWith('1001');
    });

    test('GET /api/test-reports/:tsn_id/test-cases should pass tsn_id parameter correctly to database', async () => {
      // Make request with tsn_id parameter
      const response = await request(app)
        .get('/api/test-reports/1001/test-cases');

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('test_cases');
      expect(Array.isArray(response.body.test_cases)).toBe(true);
      expect(response.body.test_cases.length).toBe(2);
      expect(response.body.test_cases[0]).toHaveProperty('tsn_id', '1001');

      // Verify database function was called with correct parameter
      expect(mockDb.getTestCaseResults).toHaveBeenCalledWith('1001');
    });
  });

  // Test error handling
  describe('Error Handling', () => {
    test('should handle database errors gracefully', async () => {
      // Mock database function to throw an error
      mockDb.getTestCases.mockRejectedValueOnce(new Error('Database error'));

      // Make request
      const response = await request(app)
        .get('/local/test-cases')
        .query({ ts_id: 101 });

      // Verify response
      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Failed to retrieve test cases');
    });

    test('should handle missing required parameters', async () => {
      // Mock case-runner service to validate parameters
      const caseRunnerService = require('../services/case-runner');
      caseRunnerService.runTest.mockImplementationOnce(async (params) => {
        if (!params.tc_id && !params.ts_id) {
          throw new Error('Missing required parameter: tc_id or ts_id');
        }
        return {
          tsn_id: '1001',
          message: 'Test started successfully'
        };
      });

      // Make request without required parameters
      const response = await request(app)
        .post('/api/case-runner')
        .send({
          uid: 'user1',
          password: 'password',
          envir: 'qa02'
        });

      // Verify response
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Either tc_id (test case ID) or ts_id (test suite ID) is required');
    });
  });
});
