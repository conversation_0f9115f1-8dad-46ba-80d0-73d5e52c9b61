# SmartTest Database Layer

This module provides a unified interface for database operations in the SmartTest application.

## Overview

The SmartTest Database Layer is designed to provide a clean, maintainable, and extensible interface for interacting with the database. It supports multiple connection methods (direct SSH and SSH tunnel) and provides a comprehensive API for querying test cases, test suites, test sessions, and test results.

## Features

- **Multiple Connection Methods**: Supports both direct SSH and SSH tunnel connections
- **Environment-Specific Configuration**: Automatically selects the best connection method based on the environment
- **Comprehensive API**: Provides a clean API for common database operations
- **Query Builder**: Includes a flexible SQL query builder for advanced queries
- **AI Integration**: Designed to be easily extensible for future AI integration
- **Error Handling**: Robust error handling with fallback mechanisms

## Directory Structure

```
database/
├── index.js                  # Main entry point
├── config/                   # Configuration
│   ├── index.js              # Configuration loader
│   └── environments.js       # Environment-specific configs
├── connections/              # Connection implementations
│   ├── index.js              # Connection factory
│   ├── direct-ssh.js         # Direct SSH implementation
│   └── ssh-tunnel.js         # SSH tunnel implementation
├── queries/                  # Pre-defined queries
│   ├── index.js              # Query module exports
│   ├── test-cases.js         # Test case queries
│   ├── test-suites.js        # Test suite queries
│   ├── test-sessions.js      # Test session queries
│   └── test-results.js       # Test result queries
├── utils/                    # Database utilities
│   ├── query-builder.js      # SQL query builder
│   └── result-formatter.js   # Result formatting utilities
└── ai/                       # AI integration components
    ├── index.js              # AI module exports
    ├── query-generator.js    # AI-driven query generation
    ├── request-generator.js  # AI-driven HTTP request generation
    └── context-provider.js   # Database context for AI
```

## Usage

### Basic Usage

```javascript
const db = require('./database');

// Initialize the database connection
await db.init();

// Get test cases
const testCases = await db.getTestCases({ limit: 10 });

// Get test suites
const testSuites = await db.getTestSuites({ projectId: 1 });

// Get active tests
const activeTests = await db.getActiveTests();

// Get test results
const testResults = await db.getTestResults('12345');

// Close the connection when done
await db.close();
```

### Advanced Usage with Query Builder

```javascript
const db = require('./database');
const { QueryBuilder } = db;

// Initialize the database connection
await db.init();

// Create a query builder
const queryBuilder = new QueryBuilder();

// Build a complex query
queryBuilder.select('test_result r', ['r.tsn_id', 'r.tc_id', 'r.outcome', 'r.creation_time', 'o.txt']);
queryBuilder.join('output o', 'r.cnt = o.cnt');
queryBuilder.where('r.tsn_id', '=', '12345');
queryBuilder.orderBy('r.creation_time', 'ASC');

// Execute the query
const { sql, params } = queryBuilder.build();
const results = await db.query(sql, params);

// Close the connection when done
await db.close();
```

### Environment-Specific Configuration

```javascript
const db = require('./database');

// Initialize with a specific environment
await db.init('qa02');

// Force direct SSH connection
await db.init('qa02', { forceDirect: true });

// Force SSH tunnel connection
await db.init('qa02', { forceTunnel: true });

// Get connection information
const info = db.getConnectionInfo();
console.log(`Connected to ${info.environment} using ${info.connectionMethod} method`);
```

### AI Integration (Future)

```javascript
const db = require('./database');

// Initialize the database connection
await db.init();

// Execute a natural language query
const results = await db.executeNaturalLanguageQuery('Get all failed test cases from the last 24 hours');

// Close the connection when done
await db.close();
```

## API Reference

### Core Functions

- `init(environment, options)`: Initialize the database connection
- `query(sql, params)`: Execute a raw SQL query
- `close()`: Close the database connection
- `getConnectionInfo()`: Get information about the current connection

### Test Case Functions

- `getTestCases(filters)`: Get test cases with optional filtering
- `getTestCaseById(tcId)`: Get a test case by ID
- `searchTestCases(criteria)`: Search test cases

### Test Suite Functions

- `getTestSuites(filters)`: Get test suites with optional filtering
- `getTestSuiteById(tsId)`: Get a test suite by ID
- `getTestSuiteInfo(tsId)`: Get test cases in a test suite

### Test Session Functions

- `getActiveTests(filters)`: Get active test sessions
- `getRecentRuns(filters)`: Get recent test sessions
- `getTestSessionDetails(tsnId)`: Get test session details

### Test Result Functions

- `getTestResults(tsnId)`: Get test results for a specific test session
- `getTestResultSummary(tsnId)`: Get test result summary for a specific test session
- `getTestCaseResults(tsnId)`: Get test case results for a specific test session

### AI-Related Functions

- `executeNaturalLanguageQuery(description)`: Execute a query generated from natural language

### Environment Utilities

- `setEnvironment(environment)`: Set environment variables for the specified environment
- `detectEnvironment()`: Detect current environment from environment variables
- `environments`: Environment configurations

## Error Handling

The database layer includes robust error handling with fallback mechanisms. If a query fails, it will attempt to execute a simpler fallback query. If the connection fails, it will attempt to reconnect.

## Future Development

The database layer is designed to be easily extensible for future AI integration. The AI integration components are currently placeholders, but they provide a foundation for future development.

## License

This module is part of the SmartTest application and is subject to the same license terms.
