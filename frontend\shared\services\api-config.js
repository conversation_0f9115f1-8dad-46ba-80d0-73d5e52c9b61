/**
 * Centralized API Configuration
 * 
 * This module provides centralized configuration for all API services
 * in the SmartTest application, ensuring consistency and maintainability.
 */

export class ApiConfig {
  constructor() {
    // Environment-based configuration
    this.environment = this.detectEnvironment();
    
    // Base URLs for different environments
    this.baseUrls = {
      development: {
        api: '/api',
        local: '',
        external: 'http://mprts-qa02.lab.wagerworks.com:9080'
      },
      production: {
        api: '/api',
        local: '',
        external: 'http://mprts-qa02.lab.wagerworks.com:9080'
      }
    };

    // Centralized endpoint definitions
    this.endpoints = {
      // Test execution endpoints
      caseRunner: '/case-runner',
      testStatus: '/test-status',
      testReport: '/test-report',
      stopTest: '/stop-test',
      rerunFailed: '/rerun-failed',
      runSuite: '/run-suite',
      
      // Data retrieval endpoints (local)
      testSuites: '/local/test-suites',
      testCases: '/local/test-cases',
      activeTests: '/local/active-tests',
      recentRuns: '/local/recent-runs',
      testDetails: '/local/test-details',
      
      // Report endpoints
      testReports: '/test-reports',
      
      // External API endpoints
      external: {
        login: '/Login',
        reportSummary: '/test-reports/{tsnId}/summary',
        reportDetails: '/test-reports/{tsnId}',
        removeSession: '/RemoveSession'
      }
    };

    // Default request configurations
    this.defaultConfig = {
      timeout: 30000,
      retries: 3,
      retryDelay: 1000
    };

    // Default test parameters
    this.defaultTestParams = {
      environment: 'qa02',
      shell_host: 'jps-qa10-app01',
      file_path: '/home/<USER>/',
      operatorConfigs: 'operatorNameConfigs',
      kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
      dataCenter: 'GU',
      rgs_env: 'qa02',
      old_version: '0',
      networkType1: 'multi-site',
      networkType2: 'multi-site',
      sign: '-',
      rate_src: 'local'
    };
  }

  /**
   * Detect the current environment
   * @returns {string} Environment name
   */
  detectEnvironment() {
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname;
      if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return 'development';
      }
    }
    return 'production';
  }

  /**
   * Get base URL for a specific service type
   * @param {string} type - Service type (api, local, external)
   * @returns {string} Base URL
   */
  getBaseUrl(type = 'api') {
    return this.baseUrls[this.environment][type] || '';
  }

  /**
   * Get full URL for an endpoint
   * @param {string} endpoint - Endpoint key or path
   * @param {string} type - Service type (api, local, external)
   * @returns {string} Full URL
   */
  getUrl(endpoint, type = 'api') {
    const baseUrl = this.getBaseUrl(type);
    
    // Handle endpoint keys vs direct paths
    const endpointPath = this.endpoints[endpoint] || endpoint;
    
    // Handle local endpoints specially
    if (endpointPath.startsWith('/local/')) {
      return endpointPath;
    }
    
    return baseUrl + endpointPath;
  }

  /**
   * Get external API URL with parameter substitution
   * @param {string} endpoint - External endpoint key
   * @param {Object} params - Parameters for substitution
   * @returns {string} Full external URL
   */
  getExternalUrl(endpoint, params = {}) {
    const baseUrl = this.getBaseUrl('external');
    let endpointPath = this.endpoints.external[endpoint];
    
    // Substitute parameters in the path
    Object.entries(params).forEach(([key, value]) => {
      endpointPath = endpointPath.replace(`{${key}}`, value);
    });
    
    return baseUrl + endpointPath;
  }

  /**
   * Get default test parameters
   * @returns {Object} Default test parameters
   */
  getDefaultTestParams() {
    return { ...this.defaultTestParams };
  }

  /**
   * Get request configuration
   * @returns {Object} Request configuration
   */
  getRequestConfig() {
    return { ...this.defaultConfig };
  }
}

// Create singleton instance
export const apiConfig = new ApiConfig();

// Export for CommonJS compatibility
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = { ApiConfig, apiConfig };
}
