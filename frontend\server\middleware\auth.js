/**
 * Authentication middleware
 */
const { TEST_USER, TEST_PASSWORD, ALT_TEST_USERS } = require('../config/app-config');

// Authentication middleware
const validateCredentials = (req, res, next) => {
  // Extract credentials from request
  const uid = req.body.uid || req.query.uid;
  const password = req.body.password || req.query.password;
  
  // Skip authentication for development/testing
  if (process.env.NODE_ENV === 'development' && process.env.SKIP_AUTH === 'true') {
    req.user = { uid: uid || TEST_USER };
    return next();
  }
  
  try {
    // In a real application, validate against database
    // For simplicity, allow test user or admin
    if ((uid === TEST_USER && password === TEST_PASSWORD) || 
        ALT_TEST_USERS.some(user => user.uid === uid && user.password === password)) {
      req.user = { uid };
      next();
    } else {
      return res.status(401).json({ 
        success: false, 
        message: 'Authentication failed. Invalid credentials.' 
      });
    }
  } catch (err) {
    next(err);
  }
};

module.exports = {
  validateCredentials
};
