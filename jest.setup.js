/**
 * Root setup file for Jest tests
 * 
 * This file runs before each test file.
 * It sets up common mocks and configurations for all tests.
 */

// Set timeout for all tests
jest.setTimeout(30000);

// Suppress console errors during tests
const originalConsoleError = console.error;
console.error = (...args) => {
  if (typeof args[0] === 'string' && args[0].includes('Warning:')) {
    return;
  }
  originalConsoleError(...args);
};

// Mock fetch if it's not available
if (!global.fetch) {
  global.fetch = jest.fn();
}

// Setup environment variables
process.env.NODE_ENV = 'test';
