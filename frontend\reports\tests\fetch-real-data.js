/**
 * Fetch Real Data from External API
 * 
 * This script fetches real data from the external API endpoints
 * to use as accurate mocks for testing.
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  baseUrl: 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun',
  credentials: {
    uid: '<EMAIL>',  // Replace with your actual credentials
    password: 'test'  // Replace with your actual password
  },
  tsnId: '14683', // Test session ID to fetch
  outputDir: path.join(__dirname, 'mocks')
};

// Helper to save response data
function saveResponse(filename, data, isHtml = true) {
  const filePath = path.join(config.outputDir, filename);
  const content = isHtml ? data : JSON.stringify(data, null, 2);
  
  fs.writeFileSync(filePath, content);
  console.log(`Saved response to ${filePath}`);
}

// Login to the external API
async function login() {
  console.log('Logging in to external API...');
  
  try {
    const formData = new URLSearchParams();
    formData.append('uid', config.credentials.uid);
    formData.append('password', config.credentials.password);
    
    const response = await axios.post(`${config.baseUrl}/Login`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      maxRedirects: 5,
      withCredentials: true
    });
    
    console.log('Login response status:', response.status);
    
    if (response.headers['set-cookie']) {
      const cookies = response.headers['set-cookie'];
      const jsessionId = cookies.find(cookie => cookie.includes('JSESSIONID='));
      
      if (jsessionId) {
        console.log('Login successful, got JSESSIONID cookie');
        return jsessionId;
      }
    }
    
    throw new Error('Login failed: No JSESSIONID cookie found');
  } catch (error) {
    console.error('Login error:', error.message);
    throw error;
  }
}

// Fetch report summary
async function fetchReportSummary(jsessionId) {
  console.log(`Fetching report summary for test session ${config.tsnId}...`);
  
  try {
    const response = await axios.get(`${config.baseUrl}/ReportSummary`, {
      params: { tsn_id: config.tsnId },
      headers: {
        Cookie: jsessionId
      }
    });
    
    console.log('Report summary fetched successfully');
    
    // Save raw HTML response
    saveResponse('real-summary-response.html', response.data);
    
    // Save metadata as JSON
    saveResponse('real-summary-metadata.json', {
      url: response.config.url,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      config: {
        url: response.config.url,
        method: response.config.method,
        params: response.config.params
      }
    }, false);
    
    return response.data;
  } catch (error) {
    console.error('Error fetching report summary:', error.message);
    throw error;
  }
}

// Fetch report details
async function fetchReportDetails(jsessionId) {
  console.log(`Fetching report details for test session ${config.tsnId}...`);
  
  try {
    const response = await axios.get(`${config.baseUrl}/ReportDetails`, {
      params: { tsn_id: config.tsnId },
      headers: {
        Cookie: jsessionId
      }
    });
    
    console.log('Report details fetched successfully');
    
    // Save raw HTML response
    saveResponse('real-details-response.html', response.data);
    
    // Save metadata as JSON
    saveResponse('real-details-metadata.json', {
      url: response.config.url,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      config: {
        url: response.config.url,
        method: response.config.method,
        params: response.config.params
      }
    }, false);
    
    return response.data;
  } catch (error) {
    console.error('Error fetching report details:', error.message);
    throw error;
  }
}

// Main function
async function main() {
  try {
    // Login to get session cookie
    const jsessionId = await login();
    
    // Fetch report summary
    await fetchReportSummary(jsessionId);
    
    // Fetch report details
    await fetchReportDetails(jsessionId);
    
    console.log('All data fetched and saved successfully');
  } catch (error) {
    console.error('Error in main process:', error.message);
  }
}

// Run the script
main();
