
# SmartTest

SmartTest is an application for managing and monitoring automated test cases, connecting to an existing testing framework API and a MySQL database.

## Features

- Dashboard displaying test statistics and performance metrics
- Test case browser with filtering and search capabilities
- Detailed test reports with charts and visualizations
- Test case executor that integrates with external testing framework
- Database connection with SSH key authentication

## Setup for New Users

This application requires setting up:
1. Environment variables for your credentials
2. SSH key authentication for secure database connections
3. Node.js dependencies

### Quick Setup (Automated)

Run the setup script to automate the installation process:

```powershell
# From the project root:
.\setup.ps1
```

The setup script will:
1. Configure environment files with your username
2. Update code files to use environment variables
3. Set up SSH key authentication if needed
4. Install dependencies and build the application

### Manual Setup

If you prefer to set up manually, follow these steps:

1. **Environment Setup**
   - Copy the sample environment files:
     - `.env.sample` → `.env` (QA02 environment)
     - `.env.01.sample` → `.env.01` (QA01 environment)
     - `.env.03.sample` → `.env.03` (QA03 environment)
   - Edit each file to add your credentials

2. **SSH Key Setup**
   - Run the SSH setup utility:
     ```powershell
     .\utils\simplified_ssh_setup.ps1
     ```
   - Follow the instructions to generate and deploy your SSH key

3. **Install Dependencies & Build**
   ```
   npm install
   npm run build
   ```

## Running the Application

Start the application server:
```
npm start
```

Then access the UI in your browser at http://localhost:3000

## Troubleshooting

### Database Connection Issues
- Verify your environment files contain the correct credentials
- Check SSH key permissions and deployment
- Run the SSH diagnostics script:
  ```powershell
  .\utils\ssh_diagnostics.ps1
  ```

### API Integration Issues
- Verify the API credentials in your environment files
- Check the network connectivity to the API servers
- Review the logs for detailed error messages

## Project Structure

- `frontend/` - UI components and client-side logic
- `frontend/server/` - Node.js backend server
- `frontend/server/database/` - Database connection and queries
- `documentation/` - Project documentation and guides
- `utils/` - Utility scripts for setup and diagnostics

## Documentation

- [API Documentation](frontend/server/documentation/API/README.md)
- [Database Documentation](frontend/server/documentation/Database/README.md)
- [Integration Documentation](frontend/server/documentation/Integration/README.md)
