/**
 * Routes Index
 * Combines all route modules
 */
const express = require('express');
const router = express.Router();

// Import route modules
const testCasesRoutes = require('./test-cases');
const testSuitesRoutes = require('./test-suites');
const testReportsRoutes = require('./test-reports');
const activeTestsRoutes = require('./active-tests');
const recentRunsRoutes = require('./recent-runs');
const testDetailsRoutes = require('./test-details');
const caseRunnerRoutes = require('./case-runner');
const proxyRoutes = require('./proxy-routes');
const testSessionsRoutes = require('./test-sessions');
const inputQueriesRoutes = require('./input-queries');

// Register routes
router.use('/local', testCasesRoutes);
router.use('/local', testSuitesRoutes);
router.use('/local', activeTestsRoutes);
router.use('/local', recentRunsRoutes);
router.use('/local', testDetailsRoutes);

// API routes
router.use('/api', testReportsRoutes);
router.use('/api', caseRunnerRoutes);
router.use('/api', proxyRoutes);

// AutoRun routes
router.use('/AutoRun', testSessionsRoutes);
router.use('/AutoRun', inputQueriesRoutes);

// Note: Backward compatibility routes have been removed as part of the server refactoring
// All frontend code now uses the correct endpoints directly

module.exports = router;
