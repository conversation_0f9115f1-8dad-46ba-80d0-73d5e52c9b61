/**
 * Test Suites Routes
 */
const express = require('express');
const router = express.Router();
const db = require('../database');
const { validateCredentials } = require('../middleware/auth');

// Get test suites
router.get('/test-suites', validateCredentials, async (req, res) => {
  try {
    console.log('GET /local/test-suites');
    // Use the database module to fetch test suites
    const testSuites = await db.getTestSuites(req.query);

    // Return as JSON with success flag
    return res.json({
      success: true,
      data: testSuites || [],
      message: 'Test suites retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving test suites:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test suites',
      error: error.message
    });
  }
});

module.exports = router;
