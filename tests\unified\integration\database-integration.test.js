/**
 * Database Integration Tests for Unified API Architecture
 *
 * Tests real database connectivity and operations:
 * - Database connection handling and health checks
 * - All database service methods with real queries
 * - Query parameter passing and result formatting
 * - Performance monitoring and logging
 * - Error scenarios and connection recovery
 *
 * Uses actual database connections with comprehensive logging
 */

const db = require('../../../frontend/server/database');
const { performance } = require('perf_hooks');

// Test configuration
const TEST_CONFIG = {
  environment: 'qa02', // Use QA02 for testing
  timeout: 30000,      // 30 second timeout for database operations
  maxRetries: 3,       // Maximum retry attempts for failed operations
  logQueries: true     // Enable detailed query logging
};

// Performance tracking
const performanceTracker = {
  operations: [],

  startOperation(name) {
    const start = performance.now();
    return {
      name,
      start,
      end: () => {
        const end = performance.now();
        const duration = end - start;
        this.operations.push({ name, duration });
        console.log(`⏱️  ${name}: ${duration.toFixed(2)}ms`);
        return duration;
      }
    };
  },

  getSummary() {
    const total = this.operations.reduce((sum, op) => sum + op.duration, 0);
    const avg = total / this.operations.length;
    return {
      totalOperations: this.operations.length,
      totalTime: total.toFixed(2),
      averageTime: avg.toFixed(2),
      operations: this.operations
    };
  },

  reset() {
    this.operations = [];
  }
};

// Enhanced logging utility
const logger = {
  info: (message, data = null) => {
    console.log(`ℹ️  ${message}`);
    if (data) console.log('   Data:', JSON.stringify(data, null, 2));
  },

  success: (message, data = null) => {
    console.log(`✅ ${message}`);
    if (data) console.log('   Result:', JSON.stringify(data, null, 2));
  },

  error: (message, error = null) => {
    console.log(`❌ ${message}`);
    if (error) console.log('   Error:', error.message || error);
  },

  query: (sql, params = [], result = null) => {
    console.log(`🔍 SQL Query: ${sql}`);
    if (params.length > 0) console.log(`   Parameters: [${params.join(', ')}]`);
    if (result) {
      console.log(`   Result Count: ${Array.isArray(result) ? result.length : 'N/A'} rows`);
      if (Array.isArray(result) && result.length > 0) {
        console.log(`   Sample Row:`, JSON.stringify(result[0], null, 2));
      }
    }
  },

  performance: (operation, duration) => {
    console.log(`⏱️  Performance - ${operation}: ${duration.toFixed(2)}ms`);
  }
};

describe('Database Integration Tests', () => {
  let connectionInfo = null;

  beforeAll(async () => {
    logger.info('🚀 Starting Database Integration Tests');
    logger.info(`Environment: ${TEST_CONFIG.environment}`);
    logger.info(`Timeout: ${TEST_CONFIG.timeout}ms`);

    // Reset performance tracker
    performanceTracker.reset();
  }, TEST_CONFIG.timeout);

  afterAll(async () => {
    logger.info('🏁 Completing Database Integration Tests');

    // Display performance summary
    const summary = performanceTracker.getSummary();
    console.log('\n📊 Performance Summary:');
    console.log(`   Total Operations: ${summary.totalOperations}`);
    console.log(`   Total Time: ${summary.totalTime}ms`);
    console.log(`   Average Time: ${summary.averageTime}ms`);

    // Close database connection
    try {
      await db.close();
      logger.success('Database connection closed successfully');
    } catch (error) {
      logger.error('Error closing database connection', error);
    }
  }, TEST_CONFIG.timeout);

  describe('Database Connection Management', () => {
    test('should establish database connection successfully', async () => {
      const timer = performanceTracker.startOperation('Database Connection');

      try {
        logger.info(`Initializing database connection to ${TEST_CONFIG.environment}`);

        const result = await db.init(TEST_CONFIG.environment);
        const duration = timer.end();

        expect(result).toBe(true);
        logger.success(`Database connection established in ${duration.toFixed(2)}ms`);

        // Get connection info
        connectionInfo = db.getConnectionInfo();
        logger.info('Connection Info', connectionInfo);

        expect(connectionInfo.initialized).toBe(true);
        expect(connectionInfo.environment).toBe(TEST_CONFIG.environment);

      } catch (error) {
        timer.end();
        logger.error('Failed to establish database connection', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);

    test('should handle connection health check', async () => {
      const timer = performanceTracker.startOperation('Connection Health Check');

      try {
        logger.info('Performing connection health check');

        // Test with a simple query
        const result = await db.query('SELECT 1 as health_check');
        const duration = timer.end();

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
        expect(result.length).toBe(1);
        expect(result[0].health_check).toBe(1);

        logger.success(`Health check passed in ${duration.toFixed(2)}ms`, result[0]);

      } catch (error) {
        timer.end();
        logger.error('Health check failed', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);
  });

  describe('Test Cases Database Operations', () => {
    test('should retrieve test cases with filters', async () => {
      const timer = performanceTracker.startOperation('Get Test Cases');

      try {
        logger.info('Retrieving test cases with limit filter');

        const filters = { limit: 5 };
        const result = await db.getTestCases(filters);
        const duration = timer.end();

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
        expect(result.length).toBeLessThanOrEqual(5);

        logger.success(`Retrieved ${result.length} test cases in ${duration.toFixed(2)}ms`);

        if (result.length > 0) {
          const sampleCase = result[0];
          logger.info('Sample Test Case', sampleCase);

          // Validate test case structure
          expect(sampleCase).toHaveProperty('tc_id');
          expect(sampleCase).toHaveProperty('tc_name');
        }

      } catch (error) {
        timer.end();
        logger.error('Failed to retrieve test cases', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);

    test('should retrieve specific test case by ID', async () => {
      const timer = performanceTracker.startOperation('Get Test Case By ID');

      try {
        // First get a test case ID
        const testCases = await db.getTestCases({ limit: 1 });

        if (testCases.length === 0) {
          logger.info('No test cases available, skipping test');
          timer.end();
          return;
        }

        const tcId = testCases[0].tc_id;
        logger.info(`Retrieving test case by ID: ${tcId}`);

        const result = await db.getTestCaseById(tcId);
        const duration = timer.end();

        expect(result).toBeDefined();
        expect(result.tc_id).toBe(tcId);

        logger.success(`Retrieved test case ${tcId} in ${duration.toFixed(2)}ms`, result);

      } catch (error) {
        timer.end();
        logger.error('Failed to retrieve test case by ID', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);

    test('should search test cases with criteria', async () => {
      const timer = performanceTracker.startOperation('Search Test Cases');

      try {
        logger.info('Searching test cases with criteria');

        const criteria = { limit: 3 };
        const result = await db.searchTestCases(criteria);
        const duration = timer.end();

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);

        logger.success(`Found ${result.length} test cases in ${duration.toFixed(2)}ms`);

        if (result.length > 0) {
          logger.info('Search Results Sample', result[0]);
        }

      } catch (error) {
        timer.end();
        logger.error('Failed to search test cases', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);
  });

  describe('Test Suites Database Operations', () => {
    test('should retrieve test suites', async () => {
      const timer = performanceTracker.startOperation('Get Test Suites');

      try {
        logger.info('Retrieving test suites');

        const filters = { limit: 5 };
        const result = await db.getTestSuites(filters);
        const duration = timer.end();

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);

        logger.success(`Retrieved ${result.length} test suites in ${duration.toFixed(2)}ms`);

        if (result.length > 0) {
          const sampleSuite = result[0];
          logger.info('Sample Test Suite', sampleSuite);

          // Validate test suite structure
          expect(sampleSuite).toHaveProperty('ts_id');
          expect(sampleSuite).toHaveProperty('ts_name');
        }

      } catch (error) {
        timer.end();
        logger.error('Failed to retrieve test suites', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);

    test('should retrieve specific test suite by ID', async () => {
      const timer = performanceTracker.startOperation('Get Test Suite By ID');

      try {
        // First get a test suite ID
        const testSuites = await db.getTestSuites({ limit: 1 });

        if (testSuites.length === 0) {
          logger.info('No test suites available, skipping test');
          timer.end();
          return;
        }

        const tsId = testSuites[0].ts_id;
        logger.info(`Retrieving test suite by ID: ${tsId}`);

        const result = await db.getTestSuiteById(tsId);
        const duration = timer.end();

        expect(result).toBeDefined();
        expect(result.ts_id).toBe(tsId);

        logger.success(`Retrieved test suite ${tsId} in ${duration.toFixed(2)}ms`, result);

      } catch (error) {
        timer.end();
        logger.error('Failed to retrieve test suite by ID', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);

    test('should retrieve test suite info with test cases', async () => {
      const timer = performanceTracker.startOperation('Get Test Suite Info');

      try {
        // First get a test suite ID
        const testSuites = await db.getTestSuites({ limit: 1 });

        if (testSuites.length === 0) {
          logger.info('No test suites available, skipping test');
          timer.end();
          return;
        }

        const tsId = testSuites[0].ts_id;
        logger.info(`Retrieving test suite info for ID: ${tsId}`);

        const result = await db.getTestSuiteInfo(tsId);
        const duration = timer.end();

        expect(result).toBeDefined();

        logger.success(`Retrieved test suite info for ${tsId} in ${duration.toFixed(2)}ms`, result);

      } catch (error) {
        timer.end();
        logger.error('Failed to retrieve test suite info', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);
  });

  describe('Test Sessions Database Operations', () => {
    test('should retrieve active tests', async () => {
      const timer = performanceTracker.startOperation('Get Active Tests');

      try {
        logger.info('Retrieving active test sessions');

        const filters = { limit: 10 };
        const result = await db.getActiveTests(filters);
        const duration = timer.end();

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);

        logger.success(`Retrieved ${result.length} active tests in ${duration.toFixed(2)}ms`);

        if (result.length > 0) {
          const sampleTest = result[0];
          logger.info('Sample Active Test', sampleTest);

          // Validate active test structure
          expect(sampleTest).toHaveProperty('tsn_id');
        }

      } catch (error) {
        timer.end();
        logger.error('Failed to retrieve active tests', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);

    test('should retrieve recent test runs', async () => {
      const timer = performanceTracker.startOperation('Get Recent Runs');

      try {
        logger.info('Retrieving recent test runs');

        const filters = { limit: 10 };
        const result = await db.getRecentRuns(filters);
        const duration = timer.end();

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);

        logger.success(`Retrieved ${result.length} recent runs in ${duration.toFixed(2)}ms`);

        if (result.length > 0) {
          const sampleRun = result[0];
          logger.info('Sample Recent Run', sampleRun);

          // Validate recent run structure
          expect(sampleRun).toHaveProperty('tsn_id');
          expect(sampleRun).toHaveProperty('start_time');
        }

      } catch (error) {
        timer.end();
        logger.error('Failed to retrieve recent runs', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);

    test('should retrieve test session details', async () => {
      const timer = performanceTracker.startOperation('Get Test Session Details');

      try {
        // First get a recent run to get a valid tsn_id
        const recentRuns = await db.getRecentRuns({ limit: 1 });

        if (recentRuns.length === 0) {
          logger.info('No recent runs available, skipping test');
          timer.end();
          return;
        }

        const tsnId = recentRuns[0].tsn_id;
        logger.info(`Retrieving test session details for TSN: ${tsnId}`);

        const result = await db.getTestSessionDetails(tsnId);
        const duration = timer.end();

        expect(result).toBeDefined();

        logger.success(`Retrieved session details for ${tsnId} in ${duration.toFixed(2)}ms`, result);

      } catch (error) {
        timer.end();
        logger.error('Failed to retrieve test session details', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);
  });

  describe('Test Results Database Operations', () => {
    test('should retrieve test results', async () => {
      const timer = performanceTracker.startOperation('Get Test Results');

      try {
        // First get a recent run to get a valid tsn_id
        const recentRuns = await db.getRecentRuns({ limit: 1 });

        if (recentRuns.length === 0) {
          logger.info('No recent runs available, skipping test');
          timer.end();
          return;
        }

        const tsnId = recentRuns[0].tsn_id;
        logger.info(`Retrieving test results for TSN: ${tsnId}`);

        const result = await db.getTestResults(tsnId);
        const duration = timer.end();

        expect(result).toBeDefined();

        logger.success(`Retrieved test results for ${tsnId} in ${duration.toFixed(2)}ms`, result);

      } catch (error) {
        timer.end();
        logger.error('Failed to retrieve test results', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);

    test('should retrieve test result summary', async () => {
      const timer = performanceTracker.startOperation('Get Test Result Summary');

      try {
        // First get a recent run to get a valid tsn_id
        const recentRuns = await db.getRecentRuns({ limit: 1 });

        if (recentRuns.length === 0) {
          logger.info('No recent runs available, skipping test');
          timer.end();
          return;
        }

        const tsnId = recentRuns[0].tsn_id;
        logger.info(`Retrieving test result summary for TSN: ${tsnId}`);

        const result = await db.getTestResultSummary(tsnId);
        const duration = timer.end();

        expect(result).toBeDefined();

        logger.success(`Retrieved test result summary for ${tsnId} in ${duration.toFixed(2)}ms`, result);

      } catch (error) {
        timer.end();
        logger.error('Failed to retrieve test result summary', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);

    test('should retrieve test case results', async () => {
      const timer = performanceTracker.startOperation('Get Test Case Results');

      try {
        // First get a recent run to get a valid tsn_id
        const recentRuns = await db.getRecentRuns({ limit: 1 });

        if (recentRuns.length === 0) {
          logger.info('No recent runs available, skipping test');
          timer.end();
          return;
        }

        const tsnId = recentRuns[0].tsn_id;
        logger.info(`Retrieving test case results for TSN: ${tsnId}`);

        const result = await db.getTestCaseResults(tsnId);
        const duration = timer.end();

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);

        logger.success(`Retrieved ${result.length} test case results for ${tsnId} in ${duration.toFixed(2)}ms`);

        if (result.length > 0) {
          logger.info('Sample Test Case Result', result[0]);
        }

      } catch (error) {
        timer.end();
        logger.error('Failed to retrieve test case results', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);
  });

  describe('Raw Query Operations', () => {
    test('should execute raw SQL queries', async () => {
      const timer = performanceTracker.startOperation('Raw SQL Query');

      try {
        logger.info('Executing raw SQL query');

        const sql = 'SELECT COUNT(*) as total_test_cases FROM test_case LIMIT 1';
        const result = await db.query(sql);
        const duration = timer.end();

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
        expect(result.length).toBe(1);
        expect(result[0]).toHaveProperty('total_test_cases');

        logger.query(sql, [], result);
        logger.success(`Raw query executed in ${duration.toFixed(2)}ms`, result[0]);

      } catch (error) {
        timer.end();
        logger.error('Failed to execute raw SQL query', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);

    test('should execute parameterized queries', async () => {
      const timer = performanceTracker.startOperation('Parameterized Query');

      try {
        logger.info('Executing parameterized SQL query');

        const sql = 'SELECT tc_id, tc_name FROM test_case WHERE tc_id > ? LIMIT ?';
        const params = [0, 3];
        const result = await db.query(sql, params);
        const duration = timer.end();

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
        expect(result.length).toBeLessThanOrEqual(3);

        logger.query(sql, params, result);
        logger.success(`Parameterized query executed in ${duration.toFixed(2)}ms`);

        if (result.length > 0) {
          result.forEach((row, index) => {
            logger.info(`Row ${index + 1}`, row);
          });
        }

      } catch (error) {
        timer.end();
        logger.error('Failed to execute parameterized query', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle invalid query gracefully', async () => {
      const timer = performanceTracker.startOperation('Invalid Query Error Handling');

      try {
        logger.info('Testing error handling with invalid query');

        const invalidSql = 'SELECT * FROM non_existent_table';

        await expect(db.query(invalidSql)).rejects.toThrow();

        const duration = timer.end();
        logger.success(`Error handling test completed in ${duration.toFixed(2)}ms`);

      } catch (error) {
        timer.end();
        logger.error('Unexpected error in error handling test', error);
        throw error;
      }
    }, TEST_CONFIG.timeout);

    test('should handle invalid test case ID gracefully', async () => {
      const timer = performanceTracker.startOperation('Invalid ID Error Handling');

      try {
        logger.info('Testing error handling with invalid test case ID');

        const invalidId = 999999999;
        const result = await db.getTestCaseById(invalidId);
        const duration = timer.end();

        // Should return null or empty result for non-existent ID
        expect(result).toBeNull();

        logger.success(`Invalid ID handling test completed in ${duration.toFixed(2)}ms`);

      } catch (error) {
        timer.end();
        logger.error('Error in invalid ID test', error);
        // This might throw an error depending on implementation
        // Both null return and error throw are acceptable
      }
    }, TEST_CONFIG.timeout);
  });
});
