/**
 * Stop Test Service
 * Handles stopping test runs via the external RemoveSession API
 */
const fetch = require('node-fetch');
const { getJsessionId } = require('./cookie-auth');

/**
 * Stops a test run via the external RemoveSession API
 * @param {string} tsnId - Test session ID
 * @param {string} uid - User ID
 * @param {string} password - Password
 * @returns {Promise<Object>} - API response
 */
async function stopTest(tsnId, uid, password) {
  if (!tsnId) {
    throw new Error('Missing or invalid tsn_id (test session ID) parameter.');
  }
  
  // Get a valid JSESSIONID cookie
  const jsessionId = await getJsessionId(uid, password);
  
  // Build the URL for the RemoveSession endpoint
  const url = 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/RemoveSession';
  
  // Prepare the form data
  const formData = new URLSearchParams();
  formData.append('tsn_id', tsnId);
  
  // Make the request to the external API
  const response = await fetch(url, {
    method: 'POST',
    body: formData,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      'Origin': 'http://mprts-qa02.lab.wagerworks.com:9080',
      'Referer': `http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=${tsnId}`,
      'X-Requested-With': 'XMLHttpRequest',
      'Cookie': `JSESSIONID=${jsessionId}`
    }
  });
  
  // Check if the response is OK
  if (!response.ok) {
    throw new Error(`Failed to stop test: ${response.status} ${response.statusText}`);
  }
  
  // Parse the response
  const responseText = await response.text();
  
  // Check if the response indicates success
  if (responseText.trim() === 'Removed') {
    return {
      success: true,
      message: `Test session ${tsnId} stopped successfully`
    };
  } else {
    throw new Error(`Failed to stop test: ${responseText}`);
  }
}

module.exports = {
  stopTest
};
