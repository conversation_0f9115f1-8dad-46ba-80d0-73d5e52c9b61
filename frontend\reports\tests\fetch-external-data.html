<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>External API Data Fetcher</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            padding: 8px;
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #45a049;
        }
        .log {
            height: 300px;
            overflow-y: auto;
            background-color: #333;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success { color: #4CAF50; }
        .warning { color: #FF9800; }
        .error { color: #F44336; }
        .info { color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>External API Data Fetcher</h1>
            <p>This tool fetches real data from the external API and saves it as HTML and JSON files.</p>
            
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" value="<EMAIL>" placeholder="Enter your username">
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="test" placeholder="Enter your password">
            </div>
            
            <div class="form-group">
                <label for="testId">Test Session ID:</label>
                <input type="text" id="testId" value="14683" placeholder="Enter the test session ID">
            </div>
            
            <div class="form-group">
                <button id="fetchButton">Fetch Data</button>
            </div>
        </div>
        
        <div class="card">
            <h2>Console Log</h2>
            <div class="log" id="consoleLog"></div>
        </div>
    </div>
    
    <script>
        // Custom console logger
        const logger = {
            log: function(message, type = 'default') {
                const logElement = document.getElementById('consoleLog');
                const logEntry = document.createElement('div');
                logEntry.className = type;
                
                const timestamp = new Date().toISOString().split('T')[1].slice(0, -1);
                logEntry.innerHTML = `[${timestamp}] ${typeof message === 'object' ? JSON.stringify(message, null, 2) : message}`;
                
                logElement.appendChild(logEntry);
                logElement.scrollTop = logElement.scrollHeight;
                
                // Also log to browser console
                console.log(message);
            },
            info: function(message) {
                this.log(message, 'info');
            },
            success: function(message) {
                this.log(message, 'success');
            },
            warn: function(message) {
                this.log(message, 'warning');
                console.warn(message);
            },
            error: function(message) {
                this.log(message, 'error');
                console.error(message);
            }
        };
        
        // Function to save data as a file
        function saveAsFile(filename, data, type = 'text/html') {
            // Create a blob from the data
            const blob = new Blob([data], { type });
            
            // Create a link element
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            
            // Append to body, click, and remove
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            logger.success(`Downloaded ${filename}`);
        }
        
        // Login to the external API
        async function login(credentials) {
            logger.info(`Logging in as ${credentials.uid}...`);
            
            try {
                // Make direct request to the API
                const url = `http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login`;
                logger.info(`Login URL: ${url}`);
                
                // Create form data
                const formData = new URLSearchParams();
                formData.append('uid', credentials.uid);
                formData.append('password', credentials.password);
                logger.info(`Form data prepared: uid=${credentials.uid}, password=******`);
                
                logger.info('Sending login request...');
                const response = await fetch(url, {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: formData,
                    mode: 'cors' // Enable CORS
                });
                
                logger.info(`Login response status: ${response.status} ${response.statusText}`);
                
                if (response.status === 200 || response.status === 302) {
                    logger.success('Login successful');
                    return true;
                } else {
                    throw new Error(`Login failed with status ${response.status}`);
                }
            } catch (error) {
                logger.error(`Error logging in: ${error.message}`);
                throw error;
            }
        }
        
        // Fetch report summary
        async function fetchReportSummary(tsnId) {
            logger.info(`Fetching report summary for test session ${tsnId}...`);
            
            try {
                const url = `http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=${tsnId}`;
                
                const response = await fetch(url, {
                    method: 'GET',
                    credentials: 'include',
                    mode: 'cors'
                });
                
                if (!response.ok) {
                    throw new Error(`Failed to fetch report summary: ${response.status} ${response.statusText}`);
                }
                
                // Get the response as text (HTML)
                const html = await response.text();
                logger.success(`Retrieved summary HTML (${html.length} bytes)`);
                
                // Save the HTML response
                saveAsFile(`real-summary-${tsnId}.html`, html);
                
                return html;
            } catch (error) {
                logger.error(`Error fetching report summary: ${error.message}`);
                throw error;
            }
        }
        
        // Fetch report details
        async function fetchReportDetails(tsnId) {
            logger.info(`Fetching report details for test session ${tsnId}...`);
            
            try {
                const url = `http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportDetails?tsn_id=${tsnId}`;
                
                const response = await fetch(url, {
                    method: 'GET',
                    credentials: 'include',
                    mode: 'cors'
                });
                
                if (!response.ok) {
                    throw new Error(`Failed to fetch report details: ${response.status} ${response.statusText}`);
                }
                
                // Get the response as text (HTML)
                const html = await response.text();
                logger.success(`Retrieved details HTML (${html.length} bytes)`);
                
                // Save the HTML response
                saveAsFile(`real-details-${tsnId}.html`, html);
                
                return html;
            } catch (error) {
                logger.error(`Error fetching report details: ${error.message}`);
                throw error;
            }
        }
        
        // Main function to fetch all data
        async function fetchAllData() {
            const uidElement = document.getElementById('username');
            const passwordElement = document.getElementById('password');
            const testIdElement = document.getElementById('testId');
            
            const credentials = {
                uid: uidElement.value.trim(),
                password: passwordElement.value
            };
            
            const tsnId = testIdElement.value.trim();
            
            if (!credentials.uid || !credentials.password) {
                logger.error('Please enter valid credentials');
                return;
            }
            
            if (!tsnId) {
                logger.error('Please enter a valid test session ID');
                return;
            }
            
            try {
                logger.info(`Starting data fetch for test session ${tsnId}...`);
                
                // Login first
                await login(credentials);
                
                // Fetch report summary
                const summaryHtml = await fetchReportSummary(tsnId);
                
                // Fetch report details
                const detailsHtml = await fetchReportDetails(tsnId);
                
                logger.success('All data fetched and saved successfully');
                
                // Also save a JSON summary of what was fetched
                const summary = {
                    testSessionId: tsnId,
                    fetchTime: new Date().toISOString(),
                    summarySize: summaryHtml.length,
                    detailsSize: detailsHtml.length,
                    files: [
                        `real-summary-${tsnId}.html`,
                        `real-details-${tsnId}.html`
                    ]
                };
                
                saveAsFile(`fetch-summary-${tsnId}.json`, JSON.stringify(summary, null, 2), 'application/json');
                
                return {
                    summary: summaryHtml,
                    details: detailsHtml
                };
            } catch (error) {
                logger.error(`Error fetching data: ${error.message}`);
                throw error;
            }
        }
        
        // Initialize the UI
        document.addEventListener('DOMContentLoaded', function() {
            logger.info('External API Data Fetcher loaded!');
            
            // Set up the fetch button
            document.getElementById('fetchButton').addEventListener('click', function() {
                fetchAllData().catch(error => {
                    logger.error(`Fetch operation failed: ${error.message}`);
                });
            });
        });
    </script>
</body>
</html>
