/**
 * Database Configuration Module
 * 
 * Loads and manages environment-specific configuration
 */
const environments = require('./environments');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env file if it exists
const envPath = path.resolve(process.cwd(), '.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
}

/**
 * Get configuration for an environment
 * @param {string} environment - Environment name (qa01, qa02, qa03)
 * @returns {Object} - Environment configuration
 */
function getConfig(environment) {
  // If no environment specified, try to detect from environment variables
  if (!environment) {
    environment = detectEnvironment();
  }
  
  // If still no environment, use default
  if (!environment || !environments[environment]) {
    console.log(`No valid environment specified, using default environment: qa02`);
    environment = 'qa02';
  }
  
  const config = environments[environment];
  
  // Override with environment variables if they exist
  Object.keys(config).forEach(key => {
    if (process.env[key]) {
      config[key] = process.env[key];
    }
  });
  
  return config;
}

/**
 * Get current environment configuration based on environment variables
 * @returns {Object} - Current environment configuration
 */
function getCurrentConfig() {
  const environment = detectEnvironment();
  return getConfig(environment);
}

/**
 * Detect current environment from environment variables
 * @returns {string|null} - Detected environment name
 */
function detectEnvironment() {
  // Try to detect from DB_HOST
  if (process.env.DB_HOST) {
    const dbHost = process.env.DB_HOST.toLowerCase();
    
    for (const [env, config] of Object.entries(environments)) {
      if (dbHost.includes(env)) {
        return env;
      }
    }
  }
  
  // Try to detect from explicit environment variable
  if (process.env.DB_ENVIRONMENT) {
    return process.env.DB_ENVIRONMENT;
  }
  
  return null;
}

/**
 * Set environment variables for the specified environment
 * @param {string} environment - Environment name (qa01, qa02, qa03)
 * @returns {Object} - The environment configuration that was applied
 */
function setEnvironment(environment) {
  if (!environments[environment]) {
    throw new Error(`Unknown environment: ${environment}. Available environments: ${Object.keys(environments).join(', ')}`);
  }
  
  const config = environments[environment];
  
  // Apply environment variables
  Object.entries(config).forEach(([key, value]) => {
    process.env[key] = String(value);
  });
  
  console.log(`Environment set to ${environment}`);
  
  return config;
}

module.exports = {
  getConfig,
  getCurrentConfig,
  detectEnvironment,
  setEnvironment,
  environments
};
