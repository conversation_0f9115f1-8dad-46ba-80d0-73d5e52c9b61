/**
 * Case Runner Routes
 */
const express = require('express');
const router = express.Router();
const fetch = require('node-fetch');
const { validateCredentials } = require('../middleware/auth');
const { runTest } = require('../services/case-runner');
const { PORT } = require('../config/app-config');

// CaseRunner API proxy endpoint - wraps the external API
router.post('/case-runner', validateCredentials, async (req, res) => {
  try {
    // Make a copy of the request body to avoid consuming it multiple times
    const requestBody = { ...req.body };

    console.log(`[API /case-runner] Received request with params:`, requestBody);

    // Determine if this is a test case run or a test suite run
    const isTestSuite = requestBody.ts_id !== undefined;
    const isTestCase = requestBody.tc_id !== undefined;

    if (!isTestCase && !isTestSuite) {
      return res.status(400).json({
        success: false,
        message: 'Either tc_id (test case ID) or ts_id (test suite ID) is required.'
      });
    }

    try {
      // Run the test using the case-runner service
      const result = await runTest(requestBody);

      // Return the result
      return res.json({
        success: true,
        ...result
      });
    } catch (error) {
      console.error(`[API /case-runner] Error running test:`, error);
      return res.status(500).json({
        success: false,
        message: `Failed to run test: ${error.message}`
      });
    }
  } catch (error) {
    console.error('Error in case-runner endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Note: The /run-suite endpoint has been deprecated in favor of /case-runner
// which can handle both test cases and test suites

module.exports = router;
