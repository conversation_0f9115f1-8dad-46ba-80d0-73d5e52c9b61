/**
 * Simple test for test-details API database functions
 * 
 * This directly tests the database query function and our null handling fixes
 * without requiring Jest or other complex testing frameworks.
 */

const db = require('../frontend/server/database');

// Test configuration
const TEST_SESSION_IDS = {
  valid: 14749,
  missingData: 14846  // The problematic session ID that was causing errors
};

// Simple assertion helper
function assertEqual(actual, expected, message) {
  if (actual !== expected) {
    console.error(`❌ ASSERTION FAILED: ${message}`);
    console.error(`  Expected: ${expected}`);
    console.error(`  Actual:   ${actual}`);
    return false;
  }
  console.log(`✅ PASSED: ${message}`);
  return true;
}

function assertHasProperty(obj, prop, message) {
  if (!obj || !obj.hasOwnProperty(prop)) {
    console.error(`❌ ASSERTION FAILED: ${message}`);
    console.error(`  Object does not have property: ${prop}`);
    return false;
  }
  console.log(`✅ PASSED: ${message}`);
  return true;
}

// Main test function
async function runTests() {
  console.log('🧪 STARTING TEST-DETAILS DATABASE TESTS 🧪');
  console.log('==========================================');
  
  let allTestsPassed = true;
  
  try {
    console.log('\n📋 TEST 1: Testing null handling with problematic session ID');
    console.log('-------------------------------------------------------');
    // Test with the problematic ID that was causing TypeError
    try {
      const result = await db.getTestSessionDetails(TEST_SESSION_IDS.missingData);
      console.log(`   Result for session ${TEST_SESSION_IDS.missingData}: ${result ? 'Data returned' : 'Null returned'}`);
      
      // If data is returned, it should have the required properties
      if (result) {
        allTestsPassed = assertHasProperty(result, 'tsn_id', 
          'Result should have tsn_id property') && allTestsPassed;
        allTestsPassed = assertHasProperty(result, 'test_name',
          'Result should have test_name property (even if empty)') && allTestsPassed;
      }
      
      console.log('   ✅ No TypeError occurred - our fix is working!');
    } catch (error) {
      console.error('   ❌ ERROR:', error.message);
      if (error.message.includes('Cannot read properties of undefined')) {
        console.error('   ❌ TEST FAILED: Still getting undefined property error after our fix');
        allTestsPassed = false;
      } else {
        console.log('   ⚠️ Other error occurred, but not the undefined property error we fixed');
      }
    }
    
    console.log('\n📋 TEST 2: Testing with valid session ID');
    console.log('-------------------------------------------------------');
    // Test with a known good session ID
    try {
      const result = await db.getTestSessionDetails(TEST_SESSION_IDS.valid);
      
      if (!result) {
        console.error(`   ❌ No data returned for valid session ID: ${TEST_SESSION_IDS.valid}`);
        allTestsPassed = false;
      } else {
        console.log(`   ✅ Data successfully returned for session ${TEST_SESSION_IDS.valid}`);
        
        // Basic validation of returned data structure
        allTestsPassed = assertHasProperty(result, 'tsn_id', 
          'Result should have tsn_id property') && allTestsPassed;
        allTestsPassed = assertHasProperty(result, 'test_name', 
          'Result should have test_name property') && allTestsPassed;
        allTestsPassed = assertHasProperty(result, 'status', 
          'Result should have status property') && allTestsPassed;
        allTestsPassed = assertHasProperty(result, 'start_time', 
          'Result should have start_time property') && allTestsPassed;
        
        // Verify test cases if present
        if (result.test_cases && result.test_cases.length > 0) {
          console.log(`   ℹ️ Found ${result.test_cases.length} test cases in result`);
          const testCase = result.test_cases[0];
          allTestsPassed = assertHasProperty(testCase, 'tc_id', 
            'Test case should have tc_id property') && allTestsPassed;
          allTestsPassed = assertHasProperty(testCase, 'test_case_name', 
            'Test case should have test_case_name property') && allTestsPassed;
          allTestsPassed = assertHasProperty(testCase, 'outcome', 
            'Test case should have outcome property') && allTestsPassed;
        } else {
          console.log('   ℹ️ No test cases found in result');
        }
      }
    } catch (error) {
      console.error('   ❌ ERROR testing valid session:', error.message);
      allTestsPassed = false;
    }
    
  } catch (err) {
    console.error('❌ UNHANDLED ERROR:', err);
    allTestsPassed = false;
  } finally {
    // Print final results
    console.log('\n==========================================');
    if (allTestsPassed) {
      console.log('✅ ALL TESTS PASSED! Our null handling fixes are working.');
    } else {
      console.log('❌ SOME TESTS FAILED. See errors above.');
    }
    console.log('==========================================\n');
    
    // Make sure to exit the process
    process.exit(allTestsPassed ? 0 : 1);
  }
}

// Run the tests
runTests().catch(err => {
  console.error('Fatal error running tests:', err);
  process.exit(1);
});
