/**
 * Test Sessions Routes
 */
const express = require('express');
const router = express.Router();
const db = require('../database');
const { v4: uuidv4 } = require('uuid');
const { validateCredentials } = require('../middleware/auth');
const { DEFAULT_PARAMS } = require('../config/app-config');

/**
 * Create a new test session
 * POST /AutoRun/TestSession
 */
router.post('/TestSession', validateCredentials, async (req, res, next) => {
  try {
    const { test_type, environment = DEFAULT_PARAMS.environment, description = '' } = req.body;

    if (!test_type) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameter: test_type'
      });
    }

    const sessionId = uuidv4();
    const timestamp = new Date().toISOString().slice(0, 19).replace('T', ' ');

    // Insert new session into database using the new db module
    await db.query(
      'INSERT INTO test_session (session_id, test_type, environment, description, created_by, created_at, status) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [sessionId, test_type, environment, description, req.user.uid, timestamp, 'created']
    );

    res.json({
      success: true,
      session_id: sessionId,
      message: 'Test session created successfully'
    });
  } catch (err) {
    next(err);
  }
});

/**
 * Get a specific test session
 * GET /AutoRun/TestSession/:id
 */
router.get('/TestSession/:id', validateCredentials, async (req, res, next) => {
  try {
    const { id } = req.params;

    const sessions = await db.query(
      'SELECT * FROM test_session WHERE session_id = ?',
      [id]
    );

    if (sessions.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Test session not found'
      });
    }

    res.json({
      success: true,
      session: sessions[0]
    });
  } catch (err) {
    next(err);
  }
});

/**
 * Get all test sessions
 * GET /AutoRun/TestSession
 */
router.get('/TestSession', validateCredentials, async (req, res, next) => {
  try {
    const { limit = 20, offset = 0, status } = req.query;

    let query = 'SELECT * FROM test_session';
    const params = [];

    if (status) {
      query += ' WHERE status = ?';
      params.push(status);
    }

    // Order by start_ts as created_at doesn't exist in the legacy schema
    query += ' ORDER BY start_ts DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const sessions = await db.query(query, params);

    res.json({
      success: true,
      sessions
    });
  } catch (err) {
    next(err);
  }
});

/**
 * Update a test session status
 * POST /AutoRun/TestSession/:id/status
 */
router.post('/TestSession/:id/status', validateCredentials, async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status, progress = null } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        message: 'Status is required'
      });
    }

    // Valid statuses
    const validStatuses = ['created', 'running', 'completed', 'failed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Status must be one of: ${validStatuses.join(', ')}`
      });
    }

    const timestamp = new Date().toISOString().slice(0, 19).replace('T', ' ');

    // Update session in database using the new db module
    const [result] = await db.query(
      'UPDATE test_session SET status = ?, progress = ?, updated_at = ? WHERE session_id = ?',
      [status, progress, timestamp, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Test session not found'
      });
    }

    res.json({
      success: true,
      session_id: id,
      status,
      progress
    });
  } catch (err) {
    next(err);
  }
});

/**
 * Get test session report
 * GET /AutoRun/TestSession/:id/Report
 */
router.get('/TestSession/:id/Report', validateCredentials, async (req, res, next) => {
  try {
    const { id } = req.params;

    // Check if session exists
    const sessions = await db.query(
      'SELECT * FROM test_session WHERE session_id = ?',
      [id]
    );

    if (sessions.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Test session not found'
      });
    }

    // Get test cases for this session
    const [testCases] = await db.query(
      'SELECT * FROM test_cases WHERE session_id = ?',
      [id]
    );

    // Get input queries for this session
    const [inputQueries] = await db.query(
      'SELECT * FROM input_queries WHERE session_id = ?',
      [id]
    );

    // Create report
    const session = sessions[0];
    const report = {
      session_id: session.session_id,
      test_type: session.test_type,
      environment: session.environment,
      description: session.description,
      status: session.status,
      created_at: session.created_at,
      updated_at: session.updated_at,
      duration: session.duration,
      test_cases: testCases,
      input_queries: inputQueries
    };

    res.json({
      success: true,
      report
    });
  } catch (err) {
    next(err);
  }
});

/**
 * Run a test case or suite
 * POST /AutoRun/case-runner
 */
router.post('/case-runner', validateCredentials, async (req, res, next) => {
  try {
    // Extract parameters
    const { tc_id, ts_id, environment = DEFAULT_PARAMS.environment } = req.body;

    // Validate parameters
    if (!tc_id && !ts_id) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameter: tc_id or ts_id'
      });
    }

    // Generate a session ID
    const sessionId = uuidv4();

    // Insert a new session
    await db.query(
      'INSERT INTO test_session (session_id, test_type, environment, created_by, created_at, status) VALUES (?, ?, ?, ?, ?, ?)',
      [sessionId, tc_id ? 'TestCase' : 'TestSuite', environment, req.user.uid, new Date().toISOString().slice(0, 19).replace('T', ' '), 'running']
    );

    // For test case, simulate test execution with a resolved promise
    const tsnId = Math.floor(Math.random() * 100000);

    // Return success with the test run ID
    res.json({
      success: true,
      tsn_id: tsnId,
      session_id: sessionId,
      message: `Test ${tc_id ? 'case' : 'suite'} ${tc_id || ts_id} started successfully`
    });
  } catch (err) {
    next(err);
  }
});

/**
 * Database schema creation endpoint (for setup)
 */
router.post('/setup', validateCredentials, async (req, res, next) => {
  try {
    // Verify admin credentials
    if (req.user.uid !== '<EMAIL>') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    // Create test_sessions table
    await db.query(`
      CREATE TABLE IF NOT EXISTS test_session (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id VARCHAR(36) NOT NULL UNIQUE,
        test_type VARCHAR(50) NOT NULL,
        environment VARCHAR(50) NOT NULL,
        description TEXT,
        status VARCHAR(20) NOT NULL,
        progress INT,
        created_by VARCHAR(100) NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME,
        duration VARCHAR(50),
        INDEX idx_session_id (session_id),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      )
    `);

    // Create test_cases table
    await db.query(`
      CREATE TABLE IF NOT EXISTS test_cases (
        id INT AUTO_INCREMENT PRIMARY KEY,
        case_id VARCHAR(36) NOT NULL UNIQUE,
        session_id VARCHAR(36) NOT NULL,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        status VARCHAR(20) NOT NULL,
        execution_time INT,
        error_message TEXT,
        created_at DATETIME NOT NULL,
        INDEX idx_case_id (case_id),
        INDEX idx_session_id (session_id),
        INDEX idx_status (status),
        FOREIGN KEY (session_id) REFERENCES test_session(session_id) ON DELETE CASCADE
      )
    `);

    // Create input_queries table
    await db.query(`
      CREATE TABLE IF NOT EXISTS input_queries (
        id INT AUTO_INCREMENT PRIMARY KEY,
        query_id VARCHAR(36) NOT NULL UNIQUE,
        session_id VARCHAR(36) NOT NULL,
        query TEXT NOT NULL,
        execution_time INT NOT NULL,
        status VARCHAR(20) NOT NULL,
        result TEXT,
        created_at DATETIME NOT NULL,
        INDEX idx_query_id (query_id),
        INDEX idx_session_id (session_id),
        INDEX idx_status (status),
        FOREIGN KEY (session_id) REFERENCES test_session(session_id) ON DELETE CASCADE
      )
    `);

    res.json({
      success: true,
      message: 'Database schema created successfully'
    });
  } catch (err) {
    next(err);
  }
});

module.exports = router;
