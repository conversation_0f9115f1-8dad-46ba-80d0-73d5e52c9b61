/**
 * Integration tests for the Reports Page
 *
 * These tests verify that the entire flow from the reports page to the external API and back works correctly:
 * - Loading reports data
 * - Displaying reports in the table
 * - Loading test details
 * - Displaying test details
 */

describe('Reports Page Integration', () => {
  // Mock DOM elements
  const mockElements = {
    reportsTable: document.createElement('tbody'),
    testDetailsSection: document.createElement('div'),
    testDetailsTitle: document.createElement('h3'),
    testDetailsInfo: document.createElement('div'),
    testCasesTable: document.createElement('tbody')
  };

  // Mock current state
  const mockCurrentState = {
    reports: [],
    currentTestDetails: null
  };

  // Mock credentials
  const mockCredentials = {
    uid: '<EMAIL>',
    password: 'test123'
  };

  // Mock session IDs
  const mockSessionIds = ['13782', '13781', '13780'];

  // Mock reports with realistic data
  const mockReports = [
    {
      tsn_id: '13782',
      test_id: '3180',
      type: 'Test Case',
      environment: 'QA02',
      status: 'Success',
      start_time: '2023-01-01T12:00:00Z',
      end_time: '2023-01-01T12:05:00Z',
      duration: '5:00',
      total_cases: 10,
      passed_cases: 10,
      failed_cases: 0,
      pass_rate: 100
    },
    {
      tsn_id: '13781',
      test_id: '3181',
      type: 'Test Case',
      environment: 'QA02',
      status: 'Failed',
      start_time: '2023-01-01T12:10:00Z',
      end_time: '2023-01-01T12:15:00Z',
      duration: '5:00',
      total_cases: 10,
      passed_cases: 8,
      failed_cases: 2,
      pass_rate: 80
    },
    {
      tsn_id: '13780',
      test_id: '3182',
      type: 'Test Suite',
      environment: 'QA02',
      status: 'Success',
      start_time: '2023-01-01T12:20:00Z',
      end_time: '2023-01-01T12:30:00Z',
      duration: '10:00',
      total_cases: 5,
      passed_cases: 5,
      failed_cases: 0,
      pass_rate: 100
    }
  ];

  // Mock test details with realistic data
  const mockTestDetails = {
    tsn_id: '13782',
    test_id: '3180',
    type: 'Test Case',
    environment: 'QA02',
    status: 'Success',
    start_time: '2023-01-01T12:00:00Z',
    end_time: '2023-01-01T12:05:00Z',
    duration: '5:00',
    total_cases: 10,
    passed_cases: 10,
    failed_cases: 0,
    pass_rate: 100,
    test_cases: [
      {
        tc_id: '3180',
        seq_index: '1',
        status: 'Passed',
        description: 'Login to the system',
        input_output: 'Input: username=test, password=test\nOutput: Login successful',
        error_message: ''
      },
      {
        tc_id: '3181',
        seq_index: '2',
        status: 'Passed',
        description: 'Navigate to account page',
        input_output: 'Input: click("Account")\nOutput: Account page displayed',
        error_message: ''
      },
      {
        tc_id: '3182',
        seq_index: '3',
        status: 'Passed',
        description: 'Verify account balance',
        input_output: 'Input: getBalance()\nOutput: Balance: $1000.00',
        error_message: ''
      }
    ]
  };

  // Setup before each test
  beforeEach(() => {
    // Reset DOM elements
    mockElements.reportsTable.innerHTML = '';
    mockElements.testDetailsSection.classList.add('d-none');
    mockElements.testDetailsTitle.textContent = '';
    mockElements.testDetailsInfo.innerHTML = '';
    mockElements.testCasesTable.innerHTML = '';

    // Reset current state
    mockCurrentState.reports = [];
    mockCurrentState.currentTestDetails = null;

    // Mock global objects
    global.elements = mockElements;
    global.currentState = mockCurrentState;
    global.config = {
      reportingEndpoint: '/local/recent-runs',
      testDetailsEndpoint: '/local/test-details',
      refreshInterval: 10000,
      useDirectExternalApi: true,
      externalApiBaseUrl: 'http://mprts-qa02.lab.wagerworks.com:9080',
      maxReportsToShow: 20
    };

    // Mock session storage
    global.sessionStorage = {
      getItem: jest.fn().mockImplementation(key => {
        if (key === 'smarttest_uid') return mockCredentials.uid;
        if (key === 'smarttest_pwd') return mockCredentials.password;
        return null;
      })
    };

    // Mock services
    global.window = {
      location: {
        origin: 'http://localhost:3000'
      },
      sessionIdService: {
        getRecentSessionIds: jest.fn().mockResolvedValue(mockSessionIds)
      },
      externalApiService: {
        getRecentTestRuns: jest.fn().mockResolvedValue(mockReports),
        getReportSummary: jest.fn().mockResolvedValue(mockReports[0]),
        getReportDetails: jest.fn().mockResolvedValue({
          tsn_id: '13782',
          test_cases: mockTestDetails.test_cases
        })
      }
    };

    // Mock helper functions
    global.formatDate = jest.fn().mockReturnValue('01/01/2023 12:00:00');
    global.getStatusBadgeClass = jest.fn().mockReturnValue('badge-success');
    global.updateCharts = jest.fn();
  });

  // Cleanup after each test
  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('loadReportsData', () => {
    test('should load reports data from external API', async () => {
      // Call loadReportsData
      await loadReportsData();

      // Verify sessionIdService.getRecentSessionIds was called with correct parameters
      expect(window.sessionIdService.getRecentSessionIds).toHaveBeenCalledWith(
        mockCredentials,
        config.maxReportsToShow
      );

      // Verify externalApiService.getRecentTestRuns was called with correct parameters
      expect(window.externalApiService.getRecentTestRuns).toHaveBeenCalledWith(
        mockSessionIds,
        mockCredentials.uid,
        mockCredentials.password,
        config.maxReportsToShow
      );

      // Verify the reports were stored in currentState
      expect(currentState.reports).toHaveLength(2);
      expect(currentState.reports[0].tsn_id).toBe('13782');
      expect(currentState.reports[1].tsn_id).toBe('13781');

      // Verify the reports table was updated
      expect(elements.reportsTable.innerHTML).not.toBe('');
      expect(elements.reportsTable.querySelectorAll('tr')).toHaveLength(2);

      // Verify updateCharts was called
      expect(updateCharts).toHaveBeenCalled();
    });

    test('should show error message if loading fails', async () => {
      // Mock externalApiService.getRecentTestRuns to throw an error
      window.externalApiService.getRecentTestRuns.mockRejectedValue(
        new Error('Failed to load reports')
      );

      // Call loadReportsData
      await loadReportsData();

      // Verify the error message was displayed
      expect(elements.reportsTable.innerHTML).toContain('Error loading reports');
      expect(elements.reportsTable.innerHTML).toContain('Failed to load reports');
    });

    test('should show message if no reports are found', async () => {
      // Mock externalApiService.getRecentTestRuns to return empty array
      window.externalApiService.getRecentTestRuns.mockResolvedValue([]);

      // Call loadReportsData
      await loadReportsData();

      // Verify the message was displayed
      expect(elements.reportsTable.innerHTML).toContain('No reports found');
    });
  });

  describe('loadTestDetails', () => {
    test('should load test details from external API', async () => {
      // Call loadTestDetails
      await loadTestDetails('13782');

      // Verify externalApiService.getReportSummary was called with correct parameters
      expect(window.externalApiService.getReportSummary).toHaveBeenCalledWith(
        '13782',
        mockCredentials.uid,
        mockCredentials.password
      );

      // Verify externalApiService.getReportDetails was called with correct parameters
      expect(window.externalApiService.getReportDetails).toHaveBeenCalledWith(
        '13782',
        1,
        mockCredentials.uid,
        mockCredentials.password
      );

      // Verify the test details were stored in currentState
      expect(currentState.currentTestDetails).toBeDefined();
      expect(currentState.currentTestDetails.tsn_id).toBe('13782');

      // Verify the test details section was displayed
      expect(elements.testDetailsSection.classList.contains('d-none')).toBe(false);

      // Verify the test cases table was updated
      expect(elements.testCasesTable.innerHTML).not.toBe('');
    });

    test('should show error message if loading fails', async () => {
      // Mock externalApiService.getReportSummary to throw an error
      window.externalApiService.getReportSummary.mockRejectedValue(
        new Error('Failed to load test details')
      );

      // Call loadTestDetails
      await loadTestDetails('13782');

      // Verify the error message was displayed
      expect(elements.testDetailsTitle.textContent).toContain('Error Loading Test Details');
    });

    test('should use existing test details if available', async () => {
      // Set current reports
      currentState.reports = [
        {
          id: '13782',
          tsn_id: '13782',
          type: 'Test Case',
          environment: 'QA02',
          status: 'Success',
          startTime: '2023-01-01T12:00:00Z',
          duration: '5:00',
          totalCases: 10,
          passedCases: 10,
          failedCases: 0,
          cases: [
            {
              tc_id: '3180',
              seq_index: '1',
              status: 'Passed',
              description: 'Login to the system'
            }
          ]
        }
      ];

      // Call loadTestDetails
      await loadTestDetails('13782');

      // Verify externalApiService.getReportSummary was called (because useDirectExternalApi is true)
      expect(window.externalApiService.getReportSummary).toHaveBeenCalled();

      // Verify the test details section was displayed
      expect(elements.testDetailsSection.classList.contains('d-none')).toBe(false);
    });
  });

  describe('updateReportsTable', () => {
    test('should update the reports table with current reports', () => {
      // Set current reports
      currentState.reports = [
        {
          id: '13782',
          tsn_id: '13782',
          type: 'Test Case',
          environment: 'QA02',
          status: 'Success',
          startTime: '2023-01-01T12:00:00Z',
          duration: '5:00',
          totalCases: 10,
          passedCases: 10,
          failedCases: 0
        }
      ];

      // Call updateReportsTable
      updateReportsTable();

      // Verify the reports table was updated
      expect(elements.reportsTable.innerHTML).not.toBe('');
      expect(elements.reportsTable.querySelectorAll('tr')).toHaveLength(1);

      // Verify the row contains the correct data
      const row = elements.reportsTable.querySelector('tr');
      expect(row.innerHTML).toContain('13782');
      expect(row.innerHTML).toContain('Test Case');
      expect(row.innerHTML).toContain('QA02');
      expect(row.innerHTML).toContain('Success');

      // Verify the details button has an event listener
      const detailsBtn = row.querySelector('.view-details-btn');
      expect(detailsBtn).toBeDefined();
    });

    test('should show message if no reports are available', () => {
      // Set empty reports
      currentState.reports = [];

      // Call updateReportsTable
      updateReportsTable();

      // Verify the message was displayed
      expect(elements.reportsTable.innerHTML).toContain('No reports found');
    });
  });

  describe('displayTestDetails', () => {
    test('should display test details', () => {
      // Set current test details
      currentState.currentTestDetails = {
        id: '13782',
        tsn_id: '13782',
        type: 'Test Case',
        environment: 'QA02',
        status: 'Success',
        startTime: '2023-01-01T12:00:00Z',
        endTime: '2023-01-01T12:05:00Z',
        duration: '5:00',
        user: '<EMAIL>',
        trigger: 'Manual',
        totalCases: 10,
        passedCases: 10,
        failedCases: 0,
        skippedCases: 0
      };

      // Call displayTestDetails
      displayTestDetails();

      // Verify the test details title was updated
      expect(elements.testDetailsTitle.textContent).toContain('Test Case');
      expect(elements.testDetailsTitle.textContent).toContain('13782');

      // Verify the test details info was updated
      expect(elements.testDetailsInfo.innerHTML).not.toBe('');
      expect(elements.testDetailsInfo.innerHTML).toContain('QA02');
      expect(elements.testDetailsInfo.innerHTML).toContain('Success');
      expect(elements.testDetailsInfo.innerHTML).toContain('5:00');
      expect(elements.testDetailsInfo.innerHTML).toContain('<EMAIL>');
    });
  });

  describe('updateTestCasesTable', () => {
    test('should update the test cases table', () => {
      // Mock test cases
      const testCases = [
        {
          tc_id: '3180',
          seq_index: '1',
          status: 'Passed',
          description: 'Login to the system',
          input_output: 'Input: username=test, password=test\nOutput: Login successful',
          error_message: ''
        },
        {
          tc_id: '3181',
          seq_index: '2',
          status: 'Failed',
          description: 'Verify balance',
          input_output: 'Input: account=123\nOutput: Error: Account not found',
          error_message: 'Account not found'
        }
      ];

      // Call updateTestCasesTable
      updateTestCasesTable(testCases);

      // Verify the test cases table was updated
      expect(elements.testCasesTable.innerHTML).not.toBe('');
      expect(elements.testCasesTable.querySelectorAll('tr')).toHaveLength(2);

      // Verify the rows contain the correct data
      const rows = elements.testCasesTable.querySelectorAll('tr');
      expect(rows[0].innerHTML).toContain('3180');
      expect(rows[0].innerHTML).toContain('Passed');
      expect(rows[0].innerHTML).toContain('Login to the system');

      expect(rows[1].innerHTML).toContain('3181');
      expect(rows[1].innerHTML).toContain('Failed');
      expect(rows[1].innerHTML).toContain('Verify balance');
      expect(rows[1].innerHTML).toContain('Account not found');
    });

    test('should show message if no test cases are available', () => {
      // Call updateTestCasesTable with empty array
      updateTestCasesTable([]);

      // Verify the message was displayed
      expect(elements.testCasesTable.innerHTML).toContain('No test cases found');
    });
  });
});
