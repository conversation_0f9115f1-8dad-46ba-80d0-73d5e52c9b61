/**
 * <PERSON>ie Authentication Service
 * Handles authentication with the external API using cookies
 */
const fetch = require('node-fetch');
const { parse } = require('cookie');

// Cache for JSESSIONID cookies
const cookieCache = new Map();

/**
 * Login to the external API and get a JSESSIONID cookie
 * @param {string} uid - User ID
 * @param {string} password - Password
 * @returns {Promise<string>} - JSESSIONID cookie
 */
async function login(uid, password) {
  // Build the login URL
  const loginUrl = 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login';
  
  // Prepare the form data
  const formData = new URLSearchParams();
  formData.append('uid', uid);
  formData.append('password', password);
  
  // Make the login request
  const response = await fetch(loginUrl, {
    method: 'POST',
    body: formData,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      'Origin': 'http://mprts-qa02.lab.wagerworks.com:9080',
      'Referer': 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login'
    },
    redirect: 'manual' // Don't follow redirects to capture cookies
  });
  
  // Check if the response is OK
  if (!response.ok && response.status !== 302) {
    throw new Error(`Login failed: ${response.status} ${response.statusText}`);
  }
  
  // Get the Set-Cookie header
  const setCookieHeader = response.headers.get('set-cookie');
  if (!setCookieHeader) {
    throw new Error('No Set-Cookie header in login response');
  }
  
  // Parse the cookie
  const cookies = setCookieHeader.split(',').map(cookie => cookie.trim());
  let jsessionId = null;
  
  for (const cookie of cookies) {
    const parsedCookie = parse(cookie);
    if (parsedCookie.JSESSIONID) {
      jsessionId = parsedCookie.JSESSIONID;
      break;
    }
  }
  
  if (!jsessionId) {
    throw new Error('No JSESSIONID cookie in login response');
  }
  
  // Cache the cookie
  cookieCache.set(uid, {
    jsessionId,
    expires: Date.now() + 30 * 60 * 1000 // 30 minutes
  });
  
  return jsessionId;
}

/**
 * Get a valid JSESSIONID cookie for a user
 * @param {string} uid - User ID
 * @param {string} password - Password
 * @returns {Promise<string>} - JSESSIONID cookie
 */
async function getJsessionId(uid, password) {
  // Check if we have a valid cached cookie
  const cachedCookie = cookieCache.get(uid);
  if (cachedCookie && cachedCookie.expires > Date.now()) {
    return cachedCookie.jsessionId;
  }
  
  // Otherwise, login and get a new cookie
  return login(uid, password);
}

module.exports = {
  getJsessionId
};
