/**
 * Input Queries Routes
 */
const express = require('express');
const router = express.Router();
const db = require('../database');
const { v4: uuidv4 } = require('uuid');
const { validateCredentials } = require('../middleware/auth');

/**
 * Log a new input query
 * POST /AutoRun/InputQuery
 */
router.post('/InputQuery', validateCredentials, async (req, res, next) => {
  try {
    const { session_id, query, execution_time, status, result = null } = req.body;

    // Validate required fields
    if (!session_id || !query || execution_time === undefined || !status) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters: session_id, query, execution_time, status'
      });
    }

    // Validate status
    const validStatuses = ['success', 'error', 'warning'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Status must be one of: ${validStatuses.join(', ')}`
      });
    }

    // Check if session exists
    const sessions = await db.query(
      'SELECT * FROM test_session WHERE session_id = ?',
      [session_id]
    );

    if (sessions.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Test session not found'
      });
    }

    const queryId = uuidv4();
    const timestamp = new Date().toISOString().slice(0, 19).replace('T', ' ');

    // Insert query into database using the new db module
    await db.query(
      'INSERT INTO input_queries (query_id, session_id, query, execution_time, status, result, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [queryId, session_id, query, execution_time, status, result, timestamp]
    );

    res.json({
      success: true,
      query_id: queryId,
      message: 'Input query logged successfully'
    });
  } catch (err) {
    next(err);
  }
});

/**
 * Get input queries for a session
 * GET /AutoRun/InputQuery/:sessionId
 */
router.get('/InputQuery/:sessionId', validateCredentials, async (req, res, next) => {
  try {
    const { sessionId } = req.params;

    const queries = await db.query(
      'SELECT * FROM input_queries WHERE session_id = ?',
      [sessionId]
    );

    res.json({
      success: true,
      queries
    });
  } catch (err) {
    next(err);
  }
});

/**
 * Get input query stats for a session
 * GET /AutoRun/InputQuery/:sessionId/Stats
 */
router.get('/InputQuery/:sessionId/Stats', validateCredentials, async (req, res, next) => {
  try {
    const { sessionId } = req.params;

    const stats = await db.query(
      'SELECT AVG(execution_time) as avg_execution_time, COUNT(*) as total_queries, ' +
      'SUM(CASE WHEN status = "success" THEN 1 ELSE 0 END) / COUNT(*) * 100 as success_rate ' +
      'FROM input_queries WHERE session_id = ?',
      [sessionId]
    );

    res.json({
      success: true,
      stats: stats[0] || {
        avg_execution_time: 0,
        total_queries: 0,
        success_rate: 0
      }
    });
  } catch (err) {
    next(err);
  }
});

module.exports = router;
