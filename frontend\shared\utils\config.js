/**
 * Unified Configuration for SmartTest Application
 * Centralizes all configuration from dashboard, reports, and config modules
 */
export class AppConfig {
  constructor() {
    this.environment = this.detectEnvironment();
    this.initializeEndpoints();
    this.initializeDefaults();
  }

  detectEnvironment() {
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname;
      return (hostname === 'localhost' || hostname === '127.0.0.1') ? 'development' : 'production';
    }
    return 'development';
  }

  initializeEndpoints() {
    this.endpoints = {
      // Local API endpoints (database)
      local: {
        testSuites: '/local/test-suites',
        testCases: '/local/test-cases', 
        activeTests: '/local/active-tests',
        recentRuns: '/local/recent-runs',
        testDetails: '/local/test-details'
      },
      
      // API endpoints (business logic)
      api: {
        caseRunner: '/api/case-runner',
        runSuite: '/api/run-suite',
        testStatus: '/api/test-status',
        testReport: '/api/test-report',
        stopTest: '/api/stop-test',
        rerunFailed: '/api/rerun-failed',
        testReports: '/api/test-reports'
      },
      
      // External API endpoints (port 9080)
      external: {
        baseUrl: 'http://mprts-qa02.lab.wagerworks.com:9080',
        login: '/AutoRun/Login',
        caseRunner: '/AutoRun/CaseRunner',
        reportSummary: '/AutoRun/ReportSummary',
        reportDetails: '/AutoRun/ReportDetails',
        removeSession: '/AutoRun/RemoveSession'
      }
    };
  }

  initializeDefaults() {
    // Default test parameters (from existing services)
    this.defaultTestParams = {
      environment: 'qa02',
      shell_host: 'jps-qa10-app01',
      file_path: '/home/<USER>/',
      operatorConfigs: 'operatorNameConfigs',
      kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
      dataCenter: 'GU',
      rgs_env: 'qa02',
      old_version: '0',
      networkType1: 'multi-site',
      networkType2: 'multi-site',
      sign: '-',
      rate_src: 'local'
    };

    // Request configuration
    this.requestConfig = {
      timeout: 30000,
      retries: 3,
      retryDelay: 1000
    };

    // Reports configuration (from reports module)
    this.reportsConfig = {
      reportingEndpoint: '/local/recent-runs',
      testDetailsEndpoint: '/local/test-details',
      refreshInterval: 30000,
      useDirectExternalApi: false,
      externalApiBaseUrl: '/api',
      maxReportsToShow: 25,
      autoRefresh: false
    };
  }

  getEndpoint(category, endpoint) {
    return this.endpoints[category]?.[endpoint] || endpoint;
  }

  getDefaultTestParams() {
    return { ...this.defaultTestParams };
  }

  getRequestConfig() {
    return { ...this.requestConfig };
  }

  getReportsConfig() {
    return { ...this.reportsConfig };
  }
}

// Create singleton instance
export const appConfig = new AppConfig();

// CommonJS compatibility
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = { AppConfig, appConfig };
}
