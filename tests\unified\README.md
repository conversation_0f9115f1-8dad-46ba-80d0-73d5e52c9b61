# Unified Testing Suite for SmartTest

## Overview

This directory contains the unified testing suite for the SmartTest application after the unified architecture migration. All tests have been consolidated and organized to provide comprehensive coverage of the new unified architecture.

## Current Status
- ✅ Testing structure created
- 🔄 Migrating existing tests
- ⏳ Creating unified test configuration

## Directory Structure

```
tests/unified/
├── unit/                           # Unit tests for individual components
│   ├── services/                   # Service layer tests
│   │   ├── unified-api-service.test.js
│   │   ├── external-api-service.test.js
│   │   ├── base-api-service.test.js
│   │   └── api-config.test.js
│   ├── utils/                      # Utility function tests
│   │   └── config.test.js
│   └── components/                 # Component tests (future)
├── integration/                    # Integration tests
│   ├── api-integration.test.js             # API layer integration
│   ├── database-integration.test.js        # Database layer integration ✅
│   ├── api-database-integration.test.js    # API-Database flow integration ✅
│   ├── external-api-integration.test.js    # External API integration
│   └── module-integration.test.js          # Cross-module integration
├── e2e/                           # End-to-end tests
│   ├── dashboard-e2e.test.js      # Dashboard module E2E
│   ├── reports-e2e.test.js        # Reports module E2E
│   └── config-e2e.test.js         # Config module E2E
├── mocks/                         # Shared mock data and utilities
│   ├── api-responses.js           # Mock API responses
│   ├── database-data.js           # Mock database data
│   └── test-helpers.js            # Shared test utilities
├── coverage/                      # Coverage reports
└── config/                        # Test configuration
    ├── jest.config.js             # Main Jest configuration
    ├── setup.js                   # Global test setup
    └── teardown.js                # Global test teardown
```

## Test Categories

### 1. Unit Tests
- **Services**: Test individual service methods in isolation
- **Utils**: Test utility functions and configurations
- **Components**: Test UI components (when applicable)

### 2. Integration Tests
- **API Integration**: Test API endpoints with real/mock database
- **Database Integration**: Test database operations and queries
- **External API Integration**: Test external API service integration
- **Module Integration**: Test cross-module functionality

### 3. End-to-End Tests
- **Dashboard E2E**: Complete user workflows in dashboard
- **Reports E2E**: Complete user workflows in reports
- **Config E2E**: Complete user workflows in config

## Running Tests

### All Tests
```bash
npm run test:unified
```

### Specific Test Categories
```bash
# Unit tests only
npm run test:unit

# Integration tests only
npm run test:integration

# E2E tests only
npm run test:e2e

# With coverage
npm run test:coverage

# Database integration tests
npm run test:database
npm run test:database:integration
npm run test:database:api
```

### Individual Test Files
```bash
# Specific service tests
npm test -- tests/unified/unit/services/unified-api-service.test.js

# Specific integration tests
npm test -- tests/unified/integration/api-integration.test.js
```

## Test Standards

### 1. Naming Conventions
- Test files: `*.test.js`
- Test descriptions: Clear, descriptive names
- Test groups: Organized by functionality

### 2. Test Structure
- **Arrange**: Set up test data and mocks
- **Act**: Execute the function/method being tested
- **Assert**: Verify the expected outcome

### 3. Mock Strategy
- Use real data structures from actual API responses
- Mock external dependencies (database, external APIs)
- Preserve actual business logic in tests

### 4. Coverage Requirements
- **Unit Tests**: 90%+ coverage for all services and utilities
- **Integration Tests**: Cover all API endpoints and database operations
- **E2E Tests**: Cover critical user workflows

## Migration from Old Tests

### Completed Migrations
- ✅ Reports tests migrated and enhanced
- ✅ Server API tests consolidated
- ✅ Database tests unified
- ✅ External API tests enhanced

### Test Data Preservation
All existing test data and mock responses have been preserved and enhanced:
- Real API response formats maintained
- Actual database schema reflected in mocks
- External API authentication flows preserved

## Coverage Reporting

Coverage reports are generated in multiple formats:
- **HTML**: `tests/unified/coverage/lcov-report/index.html`
- **JSON**: `tests/unified/coverage/coverage-final.json`
- **Text**: Console output during test runs

## Continuous Integration

Tests are configured to run in CI/CD pipelines with:
- Parallel test execution
- Coverage reporting
- Failure notifications
- Performance monitoring

## Best Practices

1. **Test Independence**: Each test should be able to run independently
2. **Real Data**: Use actual API response formats in mocks
3. **Error Testing**: Test both success and failure scenarios
4. **Performance**: Monitor test execution time
5. **Maintenance**: Keep tests updated with code changes
