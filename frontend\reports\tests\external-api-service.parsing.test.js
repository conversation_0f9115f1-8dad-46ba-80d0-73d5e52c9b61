/**
 * Unit tests for the External API Service - HTML Parsing
 *
 * These tests verify that the External API Service correctly:
 * - Parses HTML responses from the external API
 * - Extracts structured data from HTML
 * - Handles missing or invalid data
 */

describe('ExternalApiService - HTML Parsing', () => {
  let service;

  // Mock session ID
  const mockTsnId = '13782';

  // Setup before each test
  beforeEach(() => {
    // Use the global service instance
    service = window.externalApiService;
  });

  // Cleanup after each test
  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('parseReportSummaryHtml', () => {
    test('should parse HTML and extract report data', () => {
      // Use the realistic HTML mock
      const fs = require('fs');
      const path = require('path');
      const summaryHtml = fs.readFileSync(
        path.join(__dirname, 'mocks/summary-response.html'),
        'utf8'
      );

      // Create a mock HTML document based on the realistic HTML
      const mockDoc = document.createElement('div');

      // Mock status span
      const statusSpan = document.createElement('span');
      statusSpan.style.color = 'green';
      statusSpan.textContent = 'PASS';
      mockDoc.appendChild(statusSpan);

      // Mock list items
      const ul = document.createElement('ul');

      const startTimeLi = document.createElement('li');
      startTimeLi.textContent = 'Start Time: 2023-01-01 12:00:00';
      ul.appendChild(startTimeLi);

      const endTimeLi = document.createElement('li');
      endTimeLi.textContent = 'End Time: 2023-01-01 12:05:00';
      ul.appendChild(endTimeLi);

      const passedLi = document.createElement('li');
      passedLi.textContent = 'Case(s) passed: 8';
      ul.appendChild(passedLi);

      const failedLi = document.createElement('li');
      failedLi.textContent = 'Case(s) failed: 2';
      ul.appendChild(failedLi);

      const variablesLi = document.createElement('li');
      variablesLi.textContent = 'Variables: envir=qa02, host=test-host, shell_host=jps-qa10-app01';
      ul.appendChild(variablesLi);

      mockDoc.appendChild(ul);

      // Mock test case link
      const link = document.createElement('a');
      link.href = 'CaseEditor?tc_id=3180';
      link.textContent = '3180';
      mockDoc.appendChild(link);

      // Mock DOMParser to return our mock document
      global.DOMParser.mockImplementation(() => ({
        parseFromString: jest.fn().mockReturnValue(mockDoc)
      }));

      // Call parseReportSummaryHtml with the realistic HTML
      const result = service.parseReportSummaryHtml(summaryHtml, mockTsnId);

      // Verify the result contains the expected data
      expect(result).toEqual({
        tsn_id: mockTsnId,
        test_id: '3180',
        type: 'Test Case',
        environment: 'QA02',
        status: 'Success',
        start_time: '2023-01-01 12:00:00',
        end_time: '2023-01-01 12:05:00',
        duration: '5:00',
        total_cases: 10,
        passed_cases: 8,
        failed_cases: 2,
        pass_rate: 80
      });
    });

    test('should handle failed test status', () => {
      // Create a mock HTML document
      const mockDoc = document.createElement('div');

      // Mock status span
      const statusSpan = document.createElement('span');
      statusSpan.style.color = 'red';
      statusSpan.textContent = 'FAIL';
      mockDoc.appendChild(statusSpan);

      // Mock list items
      const ul = document.createElement('ul');

      const startTimeLi = document.createElement('li');
      startTimeLi.textContent = 'Start Time: 2023-01-01 12:00:00';
      ul.appendChild(startTimeLi);

      const endTimeLi = document.createElement('li');
      endTimeLi.textContent = 'End Time: 2023-01-01 12:05:00';
      ul.appendChild(endTimeLi);

      const passedLi = document.createElement('li');
      passedLi.textContent = 'Case(s) passed: 8';
      ul.appendChild(passedLi);

      const failedLi = document.createElement('li');
      failedLi.textContent = 'Case(s) failed: 2';
      ul.appendChild(failedLi);

      mockDoc.appendChild(ul);

      // Mock DOMParser to return our mock document
      global.DOMParser.mockImplementation(() => ({
        parseFromString: jest.fn().mockReturnValue(mockDoc)
      }));

      // Call parseReportSummaryHtml
      const result = service.parseReportSummaryHtml('<html>Mock HTML</html>', mockTsnId);

      // Verify the status is 'Failed'
      expect(result.status).toBe('Failed');
    });

    test('should handle test suite type', () => {
      // Create a mock HTML document
      const mockDoc = document.createElement('div');

      // Mock status span
      const statusSpan = document.createElement('span');
      statusSpan.style.color = 'green';
      statusSpan.textContent = 'PASS';
      mockDoc.appendChild(statusSpan);

      // Mock test suite link
      const link = document.createElement('a');
      link.href = 'SuiteEditor?ts_id=101';
      link.textContent = '101';
      mockDoc.appendChild(link);

      // Mock DOMParser to return our mock document
      global.DOMParser.mockImplementation(() => ({
        parseFromString: jest.fn().mockReturnValue(mockDoc)
      }));

      // Call parseReportSummaryHtml
      const result = service.parseReportSummaryHtml('<html>Mock HTML</html>', mockTsnId);

      // Verify the type is 'Test Suite'
      expect(result.type).toBe('Test Suite');
      expect(result.test_id).toBe('101');
    });

    test('should handle missing or invalid data', () => {
      // Create a minimal HTML document
      const mockDoc = document.createElement('div');

      // Mock DOMParser to return an empty document
      global.DOMParser.mockImplementation(() => ({
        parseFromString: jest.fn().mockReturnValue(mockDoc)
      }));

      // Call parseReportSummaryHtml
      const result = service.parseReportSummaryHtml('<html>Mock HTML</html>', mockTsnId);

      // Verify the result contains default values
      expect(result).toEqual({
        tsn_id: mockTsnId,
        test_id: '',
        type: 'Test Case',
        environment: 'UNKNOWN',
        status: 'Unknown',
        start_time: null,
        end_time: null,
        duration: null,
        total_cases: 0,
        passed_cases: 0,
        failed_cases: 0,
        pass_rate: 0
      });
    });

    test('should handle parsing errors', () => {
      // Mock DOMParser to throw an error
      global.DOMParser.mockImplementation(() => ({
        parseFromString: jest.fn().mockImplementation(() => {
          throw new Error('Parsing error');
        })
      }));

      // Call parseReportSummaryHtml
      const result = service.parseReportSummaryHtml('<html>Invalid HTML</html>', mockTsnId);

      // Verify the result contains error information
      expect(result).toEqual({
        tsn_id: mockTsnId,
        status: 'Error',
        error: 'Parsing error'
      });
    });
  });

  describe('parseReportDetailsHtml', () => {
    test('should parse HTML and extract test case details', () => {
      // Use the realistic HTML mock
      const fs = require('fs');
      const path = require('path');
      const detailsHtml = fs.readFileSync(
        path.join(__dirname, 'mocks/details-response.html'),
        'utf8'
      );

      // Create a mock HTML document
      const mockDoc = document.createElement('div');

      // Mock table
      const table = document.createElement('table');

      // Header row
      const headerRow = document.createElement('tr');
      ['TC ID', 'Seq', 'Outcome', 'Description', 'Input/Output'].forEach(header => {
        const th = document.createElement('th');
        th.textContent = header;
        headerRow.appendChild(th);
      });
      table.appendChild(headerRow);

      // Data row 1 - Passed
      const dataRow1 = document.createElement('tr');

      const tcIdCell = document.createElement('td');
      tcIdCell.textContent = '3180';
      dataRow1.appendChild(tcIdCell);

      const seqCell = document.createElement('td');
      seqCell.textContent = '1';
      dataRow1.appendChild(seqCell);

      const outcomeCell = document.createElement('td');
      const outcomeLink = document.createElement('a');
      outcomeLink.classList.add('P');
      outcomeLink.textContent = 'P';
      outcomeCell.appendChild(outcomeLink);
      dataRow1.appendChild(outcomeCell);

      const descCell = document.createElement('td');
      descCell.textContent = 'Login to the system';
      dataRow1.appendChild(descCell);

      const ioCell = document.createElement('td');
      ioCell.textContent = 'Input: username=test, password=test\nOutput: Login successful';
      dataRow1.appendChild(ioCell);

      table.appendChild(dataRow1);

      // Data row 2 - Failed
      const dataRow2 = document.createElement('tr');

      const tcIdCell2 = document.createElement('td');
      tcIdCell2.textContent = '3181';
      dataRow2.appendChild(tcIdCell2);

      const seqCell2 = document.createElement('td');
      seqCell2.textContent = '2';
      dataRow2.appendChild(seqCell2);

      const outcomeCell2 = document.createElement('td');
      const outcomeLink2 = document.createElement('a');
      outcomeLink2.classList.add('F');
      outcomeLink2.textContent = 'F';
      outcomeCell2.appendChild(outcomeLink2);
      dataRow2.appendChild(outcomeCell2);

      const descCell2 = document.createElement('td');
      descCell2.textContent = 'Verify balance';
      dataRow2.appendChild(descCell2);

      const ioCell2 = document.createElement('td');
      ioCell2.textContent = 'Input: account=123\nOutput: Error: Account not found';
      dataRow2.appendChild(ioCell2);

      table.appendChild(dataRow2);

      // Data row 3 - Passed
      const dataRow3 = document.createElement('tr');

      const tcIdCell3 = document.createElement('td');
      tcIdCell3.textContent = '3182';
      dataRow3.appendChild(tcIdCell3);

      const seqCell3 = document.createElement('td');
      seqCell3.textContent = '3';
      dataRow3.appendChild(seqCell3);

      const outcomeCell3 = document.createElement('td');
      const outcomeLink3 = document.createElement('a');
      outcomeLink3.classList.add('P');
      outcomeLink3.textContent = 'P';
      outcomeCell3.appendChild(outcomeLink3);
      dataRow3.appendChild(outcomeCell3);

      const descCell3 = document.createElement('td');
      descCell3.textContent = 'Logout from the system';
      dataRow3.appendChild(descCell3);

      const ioCell3 = document.createElement('td');
      ioCell3.textContent = 'Input: None\nOutput: Logout successful';
      dataRow3.appendChild(ioCell3);

      table.appendChild(dataRow3);

      mockDoc.appendChild(table);

      // Mock pagination
      const pagination = document.createElement('div');
      pagination.classList.add('pagination');
      pagination.textContent = 'Page 1 of 2';
      mockDoc.appendChild(pagination);

      // Mock DOMParser to return our mock document
      global.DOMParser.mockImplementation(() => ({
        parseFromString: jest.fn().mockReturnValue(mockDoc)
      }));

      // Call parseReportDetailsHtml with the realistic HTML
      const result = service.parseReportDetailsHtml(detailsHtml, mockTsnId);

      // Verify the result contains the expected data
      expect(result).toEqual({
        tsn_id: mockTsnId,
        test_cases: [
          {
            tc_id: '3180',
            seq_index: '1',
            status: 'Passed',
            description: 'Login to the system',
            input_output: 'Input: username=test, password=test\nOutput: Login successful',
            error_message: ''
          },
          {
            tc_id: '3181',
            seq_index: '2',
            status: 'Failed',
            description: 'Verify balance',
            input_output: 'Input: account=123\nOutput: Error: Account not found',
            error_message: 'Account not found'
          },
          {
            tc_id: '3182',
            seq_index: '3',
            status: 'Passed',
            description: 'Logout from the system',
            input_output: 'Input: None\nOutput: Logout successful',
            error_message: ''
          }
        ],
        pagination: {
          currentPage: 1,
          totalPages: 2
        }
      });
    });

    test('should handle missing or invalid data', () => {
      // Create a minimal HTML document
      const mockDoc = document.createElement('div');

      // Mock DOMParser to return an empty document
      global.DOMParser.mockImplementation(() => ({
        parseFromString: jest.fn().mockReturnValue(mockDoc)
      }));

      // Call parseReportDetailsHtml
      const result = service.parseReportDetailsHtml('<html>Mock HTML</html>', mockTsnId);

      // Verify the result contains default values
      expect(result).toEqual({
        tsn_id: mockTsnId,
        test_cases: [],
        pagination: {
          currentPage: 1,
          totalPages: 1
        }
      });
    });

    test('should handle parsing errors', () => {
      // Mock DOMParser to throw an error
      global.DOMParser.mockImplementation(() => ({
        parseFromString: jest.fn().mockImplementation(() => {
          throw new Error('Parsing error');
        })
      }));

      // Call parseReportDetailsHtml
      const result = service.parseReportDetailsHtml('<html>Invalid HTML</html>', mockTsnId);

      // Verify the result contains error information
      expect(result).toEqual({
        tsn_id: mockTsnId,
        test_cases: [],
        error: 'Parsing error'
      });
    });
  });

  describe('extractTextFromCell', () => {
    test('should extract text from a cell with a link', () => {
      // Create a cell with a link
      const cell = document.createElement('td');
      const link = document.createElement('a');
      link.textContent = 'Link Text';
      cell.appendChild(link);

      // Call extractTextFromCell
      const result = service.extractTextFromCell(cell);

      // Verify the result is the link text
      expect(result).toBe('Link Text');
    });

    test('should extract text from a cell without a link', () => {
      // Create a cell without a link
      const cell = document.createElement('td');
      cell.textContent = 'Cell Text';

      // Call extractTextFromCell
      const result = service.extractTextFromCell(cell);

      // Verify the result is the cell text
      expect(result).toBe('Cell Text');
    });

    test('should handle null or undefined cell', () => {
      // Call extractTextFromCell with null
      const result1 = service.extractTextFromCell(null);

      // Verify the result is an empty string
      expect(result1).toBe('');

      // Call extractTextFromCell with undefined
      const result2 = service.extractTextFromCell(undefined);

      // Verify the result is an empty string
      expect(result2).toBe('');
    });
  });
});
