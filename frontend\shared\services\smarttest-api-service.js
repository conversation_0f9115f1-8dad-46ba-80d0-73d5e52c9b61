/**
 * SmartTest API Service
 * 
 * Centralized API service for all SmartTest application functionality.
 * This service extends BaseApiService and provides specific methods for
 * test execution, data retrieval, and report generation.
 */

import { BaseApiService } from './base-api-service.js';

export class SmartTestApiService extends BaseApiService {
  constructor() {
    super();
    console.log('SmartTest API Service initialized');
  }

  // ==================== TEST EXECUTION METHODS ====================

  /**
   * Run a specific test case by ID
   * @param {string} tcId - Test case ID to run
   * @param {Object} params - Additional parameters
   * @returns {Promise<Object>} Response from the API
   */
  async runTestCase(tcId, params = {}) {
    console.log(`Running test case ${tcId} with params:`, params);

    const runParams = {
      tc_id: tcId,
      user_id: this.credentials.uid,
      username: this.credentials.uid,
      ...this.config.getDefaultTestParams(),
      ...params
    };

    try {
      const response = await this.postRequest('caseRunner', runParams);
      
      if (response.success || response.tsn_id) {
        console.log(`Test case ${tcId} run initiated:`, response);
        return response;
      } else {
        throw new Error(response.message || `Failed to run test case ${tcId}`);
      }
    } catch (error) {
      console.error(`Error running test case ${tcId}:`, error);
      throw error;
    }
  }

  /**
   * Run a test suite
   * @param {number} tsId - Test suite ID
   * @param {Object} params - Additional parameters
   * @returns {Promise<string>} Test suite run/session ID (tsn_id)
   */
  async runTestSuite(tsId, params = {}) {
    try {
      if (!tsId) {
        throw new Error('Test suite ID is required');
      }

      const testParams = {
        ts_id: tsId,
        user_id: this.credentials.uid,
        username: this.credentials.uid,
        ...this.config.getDefaultTestParams(),
        ...params
      };

      const response = await this.postRequest('runSuite', testParams);
      
      if (response && response.tsn_id) {
        console.log(`Test suite ${tsId} running with session ID: ${response.tsn_id}`);
        return response.tsn_id;
      } else {
        throw new Error(response.message || `Failed to run test suite ${tsId}`);
      }
    } catch (error) {
      console.error(`Error running test suite ${tsId}:`, error);
      throw error;
    }
  }

  /**
   * Stop a running test
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<boolean>} Success status
   */
  async stopTest(tsnId) {
    try {
      const response = await this.postRequest('stopTest', { tsn_id: tsnId });
      return response.success === true;
    } catch (error) {
      console.error(`Error stopping test ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Rerun failed tests from a previous test run
   * @param {number} tsnId - Original test suite run ID
   * @param {Object} params - Additional parameters
   * @returns {Promise<number>} New test suite run ID
   */
  async rerunFailedTests(tsnId, params = {}) {
    try {
      const response = await this.postRequest('rerunFailed', {
        tsn_id: tsnId,
        user_id: this.credentials.uid,
        username: this.credentials.uid,
        ...params
      });

      if (response && response.tsn_id) {
        return response.tsn_id;
      } else {
        throw new Error('Failed to get test suite run ID for rerun');
      }
    } catch (error) {
      console.error(`Error rerunning failed tests from ${tsnId}:`, error);
      throw error;
    }
  }

  // ==================== DATA RETRIEVAL METHODS ====================

  /**
   * Get available test suites
   * @returns {Promise<Array>} List of test suites
   */
  async getTestSuites() {
    try {
      const response = await this.getRequest('testSuites');
      return this.extractDataFromResponse(response, 'testSuites', []);
    } catch (error) {
      console.error('Error getting test suites:', error);
      throw error;
    }
  }

  /**
   * Get available test cases
   * @returns {Promise<Array>} List of test cases
   */
  async getTestCases() {
    try {
      const response = await this.getRequest('testCases');
      return this.extractDataFromResponse(response, 'testCases', []);
    } catch (error) {
      console.error('Error getting test cases:', error);
      throw error;
    }
  }

  /**
   * Get active tests
   * @returns {Promise<Array>} List of active tests
   */
  async getActiveTests() {
    try {
      const response = await this.getRequest('activeTests');
      return this.extractDataFromResponse(response, 'activeTests', []);
    } catch (error) {
      console.error('Error getting active tests:', error);
      throw error;
    }
  }

  /**
   * Get recent test runs
   * @param {Object} options - Query options (limit, timeRange, etc.)
   * @returns {Promise<Array>} List of recent runs
   */
  async getRecentRuns(options = {}) {
    try {
      const response = await this.getRequest('recentRuns', options);
      return this.extractDataFromResponse(response, 'data', []);
    } catch (error) {
      console.error('Error getting recent runs:', error);
      throw error;
    }
  }

  /**
   * Get test details
   * @param {string|number} tsnId - Test session ID
   * @returns {Promise<Object>} Test details
   */
  async getTestDetails(tsnId) {
    try {
      console.log(`Getting test details for ${tsnId}`);
      const response = await this.getRequest(`testDetails/${tsnId}`);
      
      // Handle different response formats
      if (response.success && response.test) {
        return response.test;
      } else if (response.success && response.data) {
        return response.data;
      } else if (response.test) {
        return response.test;
      }
      
      return response;
    } catch (error) {
      console.error(`Error getting test details for ${tsnId}:`, error);
      throw error;
    }
  }

  // ==================== REPORT METHODS ====================

  /**
   * Get test status
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<Object>} Test status data
   */
  async getTestStatus(tsnId) {
    try {
      return await this.getRequest('testStatus', { tsn_id: tsnId });
    } catch (error) {
      console.error(`Error getting test status for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get report summary for a test run
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<Object>} Test report data
   */
  async getReportSummary(tsnId) {
    try {
      return await this.getRequest(`testReports/${tsnId}/summary`);
    } catch (error) {
      console.error(`Error getting report summary for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get test reports for the reports page
   * @param {Object} params - Optional parameters like timeRange
   * @returns {Promise<Object>} Test reports response
   */
  async getTestReports(params = {}) {
    try {
      const response = await this.getRequest('testReports', params);
      return response;
    } catch (error) {
      console.error('Error getting test reports:', error);
      return { success: false, message: error.message || 'Network or API error' };
    }
  }

  // ==================== DASHBOARD METHODS ====================

  /**
   * Get test results data for dashboard display
   * @returns {Promise<Object>} Dashboard data
   */
  async getDashboardData() {
    try {
      const activeTestsData = await this.getActiveTests();

      // Transform data for dashboard
      const recentRuns = activeTestsData.map(test => ({
        id: test.tsn_id,
        type: 'Test Case',
        environment: 'QA02',
        status: test.status || 'running',
        startTime: test.latest_activity || new Date().toISOString(),
        duration: 0
      }));

      // Calculate summary statistics
      const total = activeTestsData.length;
      const successful = activeTestsData.filter(test => test.outcome === 'P').length;
      const failed = activeTestsData.filter(test => test.outcome === 'F').length;
      const running = activeTestsData.filter(test => !test.outcome).length;

      return {
        summary: { total, successful, failed, running },
        recent: recentRuns,
        environment: 'QA02'
      };
    } catch (error) {
      console.error('Error getting dashboard data:', error);
      throw error;
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Extract data from API response with fallback handling
   * @param {Object} response - API response
   * @param {string} dataKey - Key to extract data from
   * @param {any} fallback - Fallback value if extraction fails
   * @returns {any} Extracted data or fallback
   */
  extractDataFromResponse(response, dataKey, fallback) {
    if (response.success && Array.isArray(response.data)) {
      return response.data;
    } else if (response.success && response[dataKey]) {
      return response[dataKey];
    } else if (Array.isArray(response)) {
      return response;
    } else if (response[dataKey]) {
      return response[dataKey];
    }
    return fallback;
  }
}

// Create and export singleton instance
export const smartTestApi = new SmartTestApiService();

// Make available globally for backward compatibility
if (typeof window !== 'undefined') {
  window.smartTestApi = smartTestApi;
  // Maintain backward compatibility
  window.apiService = smartTestApi;
}

// Export for CommonJS compatibility
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = { SmartTestApiService, smartTestApi };
}
