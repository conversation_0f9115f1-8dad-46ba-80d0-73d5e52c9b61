/* Reports Page Styles */

/* Global styles */
:root {
  --primary-color: #0078d4;
  --secondary-color: #106ebe;
  --success-color: #107c10;
  --danger-color: #d83b01;
  --warning-color: #ffb900;
  --light-color: #f3f2f1;
  --dark-color: #323130;
  --border-color: #edebe9;
  --card-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  color: var(--dark-color);
}

/* Header and navigation styles */
.header {
  background-color: var(--primary-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-weight: bold;
  color: white !important;
}

.nav-link {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: white !important;
}

.nav-link.active {
  color: white !important;
  font-weight: 600;
  border-bottom: 2px solid white;
}

/* Sidebar styles */
.sidebar {
  background-color: white;
  border-right: 1px solid var(--border-color);
  height: calc(100vh - 56px);
}

.sidebar .nav-link {
  color: var(--dark-color) !important;
  border-radius: 0;
  padding: 10px 15px;
  margin: 2px 0;
}

.sidebar .nav-link:hover {
  background-color: #f5f5f5;
  color: var(--primary-color) !important;
}

.sidebar .nav-link.active {
  background-color: #f0f0f0;
  color: var(--primary-color) !important;
  font-weight: bold;
  border-left: 3px solid var(--primary-color);
  border-bottom: none;
}

/* Main content styles */
.main-content {
  padding: 20px;
}

.card {
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

.card-header {
  background-color: #f9f9f9;
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
}

/* Dashboard stats styles */
.stats-card {
  text-align: center;
  transition: transform 0.2s;
}

.stats-card:hover {
  transform: translateY(-5px);
}

.stats-value {
  font-size: 2rem;
  font-weight: bold;
}

.stats-label {
  font-size: 0.9rem;
  color: #666;
}

/* Filter and control styles */
.filter-bar {
  background-color: white;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: var(--card-shadow);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

/* Table styles */
.table {
  background-color: white;
}

.table th {
  border-top: none;
  background-color: #f9f9f9;
  font-weight: 600;
}

.table tbody tr {
  transition: background-color 0.2s;
}

.table tbody tr:hover {
  background-color: #f5f5f5;
}

/* FixedHeader styles */
.fixedHeader-floating {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000 !important;
}

.fixedHeader-floating th {
  background-color: #f9f9f9;
  padding: 10px !important;
}

/* SearchPanes styles - customized to match our original filter controls */
.dtsp-panes {
  padding: 0 !important;
  margin: 0 !important;
}

/* Make SearchPanes look like our original filter controls */
.dtsp-searchPane {
  margin-bottom: 0 !important;
}

/* Style the SearchPane headers like our original filter labels */
.dtsp-searchPane .dtsp-titleRow {
  background-color: transparent !important;
  border: none !important;
  padding: 0 0 5px 0 !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  color: #212529 !important;
}

/* Style the SearchPane containers like our original select boxes */
.dtsp-searchPane .dtsp-subRow2 {
  background-color: white !important;
  border: 1px solid #ced4da !important;
  border-radius: 0.25rem !important;
  padding: 0.25rem !important;
}

/* Style the SearchPane count badges */
.dtsp-pill {
  background-color: #f8f9fa !important;
  color: #6c757d !important;
  border: 1px solid #dee2e6 !important;
  font-weight: normal !important;
}

/* Style the selected items */
.dtsp-selected .dtsp-pill {
  background-color: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
}

/* Make the SearchPanes container fit our layout */
#searchPanes-container .dtsp-panesContainer {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Adjust the columns layout to match our original 3-column layout */
.dtsp-columns-3 {
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 1rem !important;
}

/* Animations for table row updates */
.report-row {
    transition: opacity 0.3s ease-in-out, background-color 0.5s ease-in-out;
}

.report-row.new-row {
    opacity: 0;
    animation: highlightFade 3s forwards;
}

.report-row.new-row.visible {
    opacity: 1;
}

@keyframes highlightFade {
    0% { background-color: rgba(255, 247, 204, 0.7); }
    100% { background-color: transparent; }
}

/* Ensure table rows have a bit of spacing for better readability */
.table-striped tbody tr {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Status colors */
.text-success {
  color: var(--success-color) !important;
}

.text-danger {
  color: var(--danger-color) !important;
}

.text-warning {
  color: var(--warning-color) !important;
}

.bg-success {
  background-color: var(--success-color) !important;
}

.bg-danger {
  background-color: var(--danger-color) !important;
}

.bg-warning {
  background-color: var(--warning-color) !important;
}

/* Progress bar styles */
.progress {
  height: 8px;
  margin-bottom: 5px;
}

.progress-bar {
  background-color: var(--success-color);
}

/* Test details section */
.test-details-section {
  background-color: white;
  border-radius: 4px;
  box-shadow: var(--card-shadow);
  margin-top: 30px;
  padding: 20px;
}

.detail-label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #666;
}

.detail-value {
  font-size: 1rem;
  margin-bottom: 15px;
}

/* Chart containers */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Loading indicator */
.loading-spinner {
  width: 3rem;
  height: 3rem;
}

/* Custom button styles */
.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    position: static;
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .chart-container {
    height: 200px;
  }

  .stats-value {
    font-size: 1.5rem;
  }
}

/* Animation for refreshing data */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s;
}

/* Custom dropdown styles */
.dropdown-menu {
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

.dropdown-item {
  padding: 8px 15px;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
  color: var(--primary-color);
}

.dropdown-item.active {
  background-color: var(--primary-color);
  color: white;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

/* Refresh notification */
.refresh-success-alert {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    opacity: 0;
    animation: fadeInOut 3s forwards;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-20px); }
    10% { opacity: 1; transform: translateY(0); }
    90% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-20px); }
}

/* Loading indicator animation */
.loading-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.loading-indicator .dots {
    display: flex;
    gap: 4px;
}

.loading-indicator .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #0d6efd;
    animation: bounce 1.4s infinite ease-in-out both;
}

.loading-indicator .dot:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-indicator .dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}