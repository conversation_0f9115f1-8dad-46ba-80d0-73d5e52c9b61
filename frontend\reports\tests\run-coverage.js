/**
 * <PERSON><PERSON>t to run tests and generate coverage report
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Define paths
const rootDir = path.resolve(__dirname, '../../');
const coverageDir = path.resolve(__dirname, 'coverage');

// Create coverage directory if it doesn't exist
if (!fs.existsSync(coverageDir)) {
  fs.mkdirSync(coverageDir, { recursive: true });
}

// Run Jest with coverage
try {
  console.log('Running tests with coverage...');
  execSync('npx jest --config=reports/tests/jest.config.js --coverage', {
    cwd: rootDir,
    stdio: 'inherit'
  });
  
  console.log('\nTests completed successfully!');
  console.log(`Coverage report is available at: ${coverageDir}`);
  
  // Generate summary
  const coverageSummaryPath = path.join(coverageDir, 'coverage-summary.json');
  if (fs.existsSync(coverageSummaryPath)) {
    const summary = JSON.parse(fs.readFileSync(coverageSummaryPath, 'utf8'));
    const total = summary.total;
    
    console.log('\nCoverage Summary:');
    console.log(`Statements : ${total.statements.pct}%`);
    console.log(`Branches   : ${total.branches.pct}%`);
    console.log(`Functions  : ${total.functions.pct}%`);
    console.log(`Lines      : ${total.lines.pct}%`);
  }
} catch (error) {
  console.error('Error running tests:', error.message);
  process.exit(1);
}
