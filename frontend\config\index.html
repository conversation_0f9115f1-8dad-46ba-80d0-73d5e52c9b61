<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Automation Configuration</title>
    <!-- Replace Bootstrap with Fluent UI -->
    <link rel="stylesheet" href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css">
    <link rel="stylesheet" href="styles.css">
    <!-- Add Mock API Service for testing -->
    <script src="./mock-api.js"></script>
    <!-- Add API Service as ES6 module -->
    <script type="module" src="./services/api-service.js"></script>
    <!-- Mock API service worker for development and testing -->
    <script src="./register-mock-service-worker.js"></script>
</head>
<body class="ms-Fabric">
    <header class="ms-CommandBar teams-header">
        <div class="ms-CommandBar-mainArea">
            <div class="ms-CommandBar-primaryCommand">
                <div class="teams-app-title">Test Automation Framework</div>
            </div>
            <div class="ms-CommandBar-sideCommands">
                <div class="teams-environment-display" id="environment-display">Current Environment: Development</div>
            </div>
        </div>
    </header>

    <div class="teams-container">
        <div class="teams-layout">
            <nav class="ms-Nav teams-nav">
                <div class="ms-Nav-group">
                    <ul class="ms-Nav-groupContent">
                        <li class="ms-Nav-item">
                            <a class="ms-Nav-link" href="../dashboard/index.html">
                                <i class="ms-Icon ms-Icon--ViewDashboard" aria-hidden="true"></i> Dashboard
                            </a>
                        </li>
                        <li class="ms-Nav-item">
                            <a class="ms-Nav-link is-selected" href="#">
                                <i class="ms-Icon ms-Icon--Settings" aria-hidden="true"></i> Configuration
                            </a>
                        </li>
                        <li class="ms-Nav-item">
                            <a class="ms-Nav-link" href="../reports/index.html">
                                <i class="ms-Icon ms-Icon--ReportDocument" aria-hidden="true"></i> Reports
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="teams-content">
                <div class="teams-content-header">
                    <h1 class="ms-font-xxl">Configuration</h1>
                    <div class="teams-action-bar">
                        <button type="button" class="ms-Button ms-Button--primary" id="save-config-btn">
                            <span class="ms-Button-label">Save Configuration</span>
                        </button>
                    </div>
                </div>

                <div class="teams-content-body">
                    <div class="teams-grid">
                        <div class="teams-grid-col-8">
                            <div class="ms-DocumentCard teams-card">
                                <div class="ms-DocumentCard-title">
                                    <h4 class="ms-font-l">Environment Settings</h4>
                                </div>
                                <div class="ms-DocumentCard-details">
                                    <form id="environment-form">
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Default Environment</label>
                                            <div class="ms-Dropdown">
                                                <select class="ms-Dropdown-select" id="environment-select">
                                                    <option value="development">Development</option>
                                                    <option value="staging">Staging</option>
                                                    <option value="production">Production</option>
                                                </select>
                                            </div>
                                            <div class="ms-TextField-description">Select the default environment for test execution.</div>
                                        </div>

                                        <h5 class="ms-font-m teams-section-title">Environment Configuration</h5>
                                        <div class="environment-config" id="development-config">
                                            <div class="ms-TextField teams-form-group">
                                                <label class="ms-Label">Database Host</label>
                                                <input type="text" class="ms-TextField-field" id="dev-db-host" value="localhost">
                                            </div>
                                            <div class="ms-TextField teams-form-group">
                                                <label class="ms-Label">Database Name</label>
                                                <input type="text" class="ms-TextField-field" id="dev-db-name" value="testautomation">
                                            </div>
                                        </div>

                                        <div class="environment-config teams-hidden" id="staging-config">
                                            <div class="ms-TextField teams-form-group">
                                                <label class="ms-Label">Database Host</label>
                                                <input type="text" class="ms-TextField-field" id="staging-db-host" value="staging-db.example.com">
                                            </div>
                                            <div class="ms-TextField teams-form-group">
                                                <label class="ms-Label">Database Name</label>
                                                <input type="text" class="ms-TextField-field" id="staging-db-name" value="testautomation_staging">
                                            </div>
                                        </div>

                                        <div class="environment-config teams-hidden" id="production-config">
                                            <div class="ms-TextField teams-form-group">
                                                <label class="ms-Label">Database Host</label>
                                                <input type="text" class="ms-TextField-field" id="prod-db-host" value="db.example.com">
                                            </div>
                                            <div class="ms-TextField teams-form-group">
                                                <label class="ms-Label">Database Name</label>
                                                <input type="text" class="ms-TextField-field" id="prod-db-name" value="testautomation_prod">
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="ms-DocumentCard teams-card">
                                <div class="ms-DocumentCard-title">
                                    <h4 class="ms-font-l">Test Configurations</h4>
                                </div>
                                <div class="ms-DocumentCard-details">
                                    <form id="test-config-form">
                                        <h5 class="ms-font-m teams-section-title">Smoke Test Configuration</h5>
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Timeout (seconds)</label>
                                            <input type="number" class="ms-TextField-field" id="smoke-test-timeout" value="120">
                                        </div>
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Retry Attempts</label>
                                            <input type="number" class="ms-TextField-field" id="smoke-test-retries" value="2">
                                        </div>

                                        <h5 class="ms-font-m teams-section-title">Regression Test Configuration</h5>
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Timeout (seconds)</label>
                                            <input type="number" class="ms-TextField-field" id="regression-test-timeout" value="600">
                                        </div>
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Retry Attempts</label>
                                            <input type="number" class="ms-TextField-field" id="regression-test-retries" value="1">
                                        </div>

                                        <h5 class="ms-font-m teams-section-title">Heartbeat Test Configuration</h5>
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Timeout (seconds)</label>
                                            <input type="number" class="ms-TextField-field" id="heartbeat-test-timeout" value="60">
                                        </div>
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Frequency (minutes)</label>
                                            <input type="number" class="ms-TextField-field" id="heartbeat-test-frequency" value="60">
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="teams-grid-col-4">
                            <div class="ms-DocumentCard teams-card">
                                <div class="ms-DocumentCard-title">
                                    <h4 class="ms-font-l">Interface Settings</h4>
                                </div>
                                <div class="ms-DocumentCard-details">
                                    <form id="interface-form">
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Dashboard Refresh Interval (seconds)</label>
                                            <input type="number" class="ms-TextField-field" id="refresh-interval" value="30">
                                        </div>
                                        <div class="ms-CheckBox teams-form-group">
                                            <input class="ms-CheckBox-input" type="checkbox" id="enable-notifications" checked>
                                            <label class="ms-CheckBox-field" for="enable-notifications">
                                                <span class="ms-Label">Enable Notifications</span>
                                            </label>
                                        </div>
                                        <div class="ms-CheckBox teams-form-group">
                                            <input class="ms-CheckBox-input" type="checkbox" id="auto-expand-details" checked>
                                            <label class="ms-CheckBox-field" for="auto-expand-details">
                                                <span class="ms-Label">Auto-expand Test Details</span>
                                            </label>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="ms-DocumentCard teams-card">
                                <div class="ms-DocumentCard-title">
                                    <h4 class="ms-font-l">API Endpoints</h4>
                                </div>
                                <div class="ms-DocumentCard-details">
                                    <form id="api-form">
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">n8n Base URL</label>
                                            <input type="text" class="ms-TextField-field" id="n8n-base-url" value="http://localhost:5678">
                                        </div>
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Test Execution Webhook</label>
                                            <input type="text" class="ms-TextField-field" id="test-execution-webhook" value="/webhook/test-execution">
                                        </div>
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">NLP Processing Webhook</label>
                                            <input type="text" class="ms-TextField-field" id="nlp-webhook" value="/webhook/nlp-processor">
                                        </div>
                                        <div class="ms-TextField teams-form-group">
                                            <label class="ms-Label">Test Results Webhook</label>
                                            <input type="text" class="ms-TextField-field" id="test-results-webhook" value="/webhook/test-results">
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Replace Bootstrap JS with Fluent UI JS -->
    <script src="https://unpkg.com/@fluentui/react@8/dist/fluentui-react.js"></script>
    <script src="config.js"></script>

    <!-- Initialize Mock API System -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the mock API system
            if (typeof initMockApiSystem === 'function') {
                initMockApiSystem();
            } else {
                console.warn('Mock API system initialization function not found');
            }
        });
    </script>
    <script>
        // Add service worker registration script
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('./service-worker.js').then(function(registration) {
                    console.log('Service worker registered:', registration);
                }).catch(function(registrationError) {
                    console.error('Service worker registration failed:', registrationError);
                });
            });
        } else {
            console.error('Service workers are not supported.');
        }
    </script>
</body>
</html>