



<html>
<head>
<title>Home Page</title>
<link rel="stylesheet" type="text/css" href="css/table.css">
<script type="text/javascript" src="jquery-ui-1.11.0.custom/external/jquery/jquery.js"></script>
<script src="js/lib.js"></script>
<script src="js/menu.js"></script>
<script type="text/javascript">
$(function() {
	var icon = 0;
	$(".icon10").each(function(i){
		$(this).attr('data-icon', icon++);
		$(this).click(function() {
			var offset = [];
			$('tr[data-offset]').each(function(){
				offset.push($(this).attr('data-offset'));
				});
			var parameters = [
			                  ['offset', offset[0]],
			                  ['offset', offset[1]],
			                  ['offset', offset[2]],
			                  ['offset', offset[3]],
			                  ['offset', offset[4]],
			                  ['offset', offset[5]],
			                  ['icon', $(this).attr('data-icon')]];
			request('home', 'get', parameters);
			});
		});
})
</script>
<style>
td.inline {
    visibility: inline;
}
td.collapse {
    visibility: collapse;
}
</style>
</head>
<body>
	<a href="home" class="clickable">Home</a> &nbsp;
<span id="case" class="clickable">Case#</span> &nbsp;
<span id="suite" class="clickable">Suite#</span> &nbsp;
<span id="project" class="clickable">Project#</span> &nbsp;
<a href="ReportList?tp_id=201&tp_id=202" class="clickable">Report</a>
<p/><p/>
	<span style="color: red;"></span>
	
	<p/>
	<table class="spacing">
 		<tr>
			<td colspan="7">
				Recent Case &nbsp; <button onclick="request('CaseCreator', 'post', []);">New Case</button>
			</td>
		</tr>
		<tr class="center clickable" data-offset="0">
			<td class="collapse">
				<img src="img/prev.jpeg" class="icon10" /><img src="img/prev.jpeg" class="icon10" />
			</td>

			<td onclick="goCase($(this).text())"></td>

			<td onclick="goCase($(this).text())"></td>

			<td onclick="goCase($(this).text())"></td>

			<td onclick="goCase($(this).text())"></td>

			<td onclick="goCase($(this).text())"></td>

			<td class="collapse">
				<img src="img/next.jpeg" class="icon10" /><img src="img/next.jpeg" class="icon10" />
			</td>
		</tr>
		<tr class="center clickable" data-offset="0">
			<td class="collapse">
				<img src="img/prev.jpeg" class="icon10" /><img src="img/prev.jpeg" class="icon10" />
			</td>

			<td onclick="goCase($(this).text())">2669</td>

			<td onclick="goCase($(this).text())">2651</td>

			<td onclick="goCase($(this).text())">3243</td>

			<td onclick="goCase($(this).text())">3300</td>

			<td onclick="goCase($(this).text())">3299</td>

			<td class="inline">
				<img src="img/next.jpeg" class="icon10" /><img src="img/next.jpeg" class="icon10" />
			</td>
		</tr>
		<tr>
			<td colspan="7">
				Recent Suite &nbsp; <button onclick="request('SuiteCreator', 'post', []);">New Suite</button>
			</td>
		</tr>
		<tr class="center clickable blue" data-offset="0">
			<td class="collapse">
				<img src="img/prev.jpeg" class="icon10" /><img src="img/prev.jpeg" class="icon10" />
			</td>

			<td onclick="goSuite($(this).text())"></td>

			<td onclick="goSuite($(this).text())"></td>

			<td onclick="goSuite($(this).text())"></td>

			<td onclick="goSuite($(this).text())"></td>

			<td onclick="goSuite($(this).text())"></td>

			<td class="collapse">
				<img src="img/next.jpeg" class="icon10" /><img src="img/next.jpeg" class="icon10" />
			</td>
		</tr>
		<tr class="center clickable blue" data-offset="0">
			<td class="collapse">
				<img src="img/prev.jpeg" class="icon10" /><img src="img/prev.jpeg" class="icon10" />
			</td>

			<td onclick="goSuite($(this).text())">183</td>

			<td onclick="goSuite($(this).text())">208</td>

			<td onclick="goSuite($(this).text())">229</td>

			<td onclick="goSuite($(this).text())">189</td>

			<td onclick="goSuite($(this).text())">209</td>

			<td class="inline">
				<img src="img/next.jpeg" class="icon10" /><img src="img/next.jpeg" class="icon10" />
			</td>
		</tr>
		<tr>
			<td colspan="7">
				Recent Project &nbsp; <button onclick="request('ProjectCreator', 'post', []);">New Project</button>
			</td>
		</tr>
		<tr class="center clickable brown" data-offset="0">
			<td class="collapse">
				<img src="img/prev.jpeg" class="icon10" /><img src="img/prev.jpeg" class="icon10" />
			</td>

			<td onclick="goProject($(this).text())"></td>

			<td onclick="goProject($(this).text())"></td>

			<td onclick="goProject($(this).text())"></td>

			<td onclick="goProject($(this).text())"></td>

			<td onclick="goProject($(this).text())"></td>

			<td class="collapse">
				<img src="img/next.jpeg" class="icon10" /><img src="img/next.jpeg" class="icon10" />
			</td>
		</tr>
		<tr class="center clickable brown" data-offset="0">
			<td class="collapse">
				<img src="img/prev.jpeg" class="icon10" /><img src="img/prev.jpeg" class="icon10" />
			</td>

			<td onclick="goProject($(this).text())">30</td>

			<td onclick="goProject($(this).text())">31</td>

			<td onclick="goProject($(this).text())">32</td>

			<td onclick="goProject($(this).text())">33</td>

			<td onclick="goProject($(this).text())">34</td>

			<td class="inline">
				<img src="img/next.jpeg" class="icon10" /><img src="img/next.jpeg" class="icon10" />
			</td>
		</tr>
	</table>
	<p/>
	<a href="Tutorial/tutorials.html" class="clickable">Tutorial</a>&nbsp; 
	<a href="import.jsp" class="clickable">Import</a>&nbsp;
	<a href="ProfileLoader?tp_id=99" class="clickable">Bookmarks</a>
	<br/><br/>
ID: <a href="ProfileLoader" class="clickable"><EMAIL></a><p/>

</body>
</html>
