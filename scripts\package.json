{"name": "smarttest-scripts", "version": "1.0.0", "description": "Debugging and testing scripts for SmartTest application", "main": "index.js", "scripts": {"verify:connection": "node verify_connection.js", "verify:schema": "node verify_schema.js", "api:test": "node api_test.js", "test:run": "node run_test.js", "test:monitor": "node monitor_test.js", "connect:qa01": "node verify_connection.js qa01", "connect:qa02": "node verify_connection.js qa02", "connect:qa03": "node verify_connection.js qa03", "schema:qa01": "node verify_schema.js qa01", "schema:qa02": "node verify_schema.js qa02", "schema:qa03": "node verify_schema.js qa03"}, "author": "", "license": "ISC", "dependencies": {"axios": "^0.24.0", "mysql2": "^2.3.3", "ssh2": "^1.10.0"}}