/**
 * Mock API Service for SmartTest Frontend Testing
 * This file intercepts API requests and returns mock data that matches the actual API format
 */

// Create realistic mock data based on documentation
const mockData = {
  // Test cases based on the test_case_structure.md schema
  testCases: [
    { tc_id: 3180, uid: "Iak<PERSON>.<PERSON><PERSON><PERSON>@igtplayer.com", status: "A", case_driver: "com.igt.pa.core.DefaultCaseDriver", tp_id: 33, comments: "Regression test for payment system", tickets: "JIRA-1234", name: "Payment Gateway Validation" },
    { tc_id: 3181, uid: "<EMAIL>", status: "A", case_driver: "com.igt.pa.core.DefaultCaseDriver", tp_id: 33, comments: "Verify authentication flow", tickets: "JIRA-1235", name: "Authentication Flow Test" },
    { tc_id: 3182, uid: "Iakov.<PERSON><PERSON><PERSON><PERSON>@igtplayer.com", status: "A", case_driver: "com.igt.pa.core.DefaultCaseDriver", tp_id: 33, comments: "Check user profile update", tickets: "JIRA-2345", name: "User Profile Update Test" },
    { tc_id: 3183, uid: "<EMAIL>", status: "A", case_driver: "com.igt.pa.core.DefaultCaseDriver", tp_id: 33, comments: "Validate search functionality", tickets: "JIRA-3456", name: "Search Function Test" },
    { tc_id: 3184, uid: "<EMAIL>", status: "A", case_driver: "com.igt.pa.core.DefaultCaseDriver", tp_id: 33, comments: "Test error handling", tickets: "JIRA-4567", name: "Error Handling Test" },
    { tc_id: 3185, uid: "<EMAIL>", status: "M", case_driver: "com.igt.pa.core.DefaultCaseDriver", tp_id: 33, comments: "Database connection test", tickets: "JIRA-5678", name: "Database Connection Test" },
    { tc_id: 3186, uid: "<EMAIL>", status: "I", case_driver: "com.igt.pa.core.DefaultCaseDriver", tp_id: 33, comments: "Legacy system check", tickets: "JIRA-6789", name: "Legacy System Integration Test" },
    { tc_id: 3187, uid: "<EMAIL>", status: "A", case_driver: "com.igt.pa.core.ExtendedCaseDriver", tp_id: 33, comments: "Performance verification", tickets: "JIRA-7890", name: "Performance Benchmark Test" },
    { tc_id: 3188, uid: "<EMAIL>", status: "A", case_driver: "com.igt.pa.core.DefaultCaseDriver", tp_id: 33, comments: "Security vulnerability scan", tickets: "JIRA-8901", name: "Security Vulnerability Test" },
    { tc_id: 3189, uid: "<EMAIL>", status: "A", case_driver: "com.igt.pa.core.DefaultCaseDriver", tp_id: 33, comments: "User interface testing", tickets: "JIRA-9012", name: "UI Validation Test" }
  ],
  
  // Test suites based on the test_suite_structure.md schema
  testSuites: [
    { ts_id: 101, uid: "<EMAIL>", name: "Smoke Test Suite", description: "Basic functionality tests to verify system health", status: "A" },
    { ts_id: 102, uid: "<EMAIL>", name: "Regression Test Suite", description: "Full regression tests for all core features", status: "A" },
    { ts_id: 103, uid: "<EMAIL>", name: "Performance Test Suite", description: "Test system performance under various conditions", status: "A" },
    { ts_id: 104, uid: "<EMAIL>", name: "Security Test Suite", description: "Security verification and testing", status: "A" }
  ],
  
  // Mock active tests with different initiators
  activeTests: [
    {
      tsn_id: "50001",
      tc_id: "3180",
      status: "running",
      initiator_username: "<EMAIL>",
      step_count: 15,
      latest_activity: new Date(Date.now() - 60000).toISOString(), // 1 minute ago
      name: "Payment Gateway Validation"
    },
    {
      tsn_id: "50002",
      tc_id: "3181",
      status: "running",
      initiator_username: "<EMAIL>",
      step_count: 8,
      latest_activity: new Date(Date.now() - 120000).toISOString(), // 2 minutes ago
      name: "Authentication Flow Test"
    },
    {
      tsn_id: "50003",
      tc_id: "3182",
      status: "running",
      initiator_username: "<EMAIL>",
      step_count: 22,
      latest_activity: new Date(Date.now() - 180000).toISOString(), // 3 minutes ago
      name: "User Profile Update Test"
    },
    {
      tsn_id: "50004",
      tc_id: "3183",
      status: "failed",
      initiator_username: "<EMAIL>",
      step_count: 5,
      latest_activity: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
      name: "Search Function Test"
    },
    {
      tsn_id: "50005",
      tc_id: "3184",
      status: "completed",
      initiator_username: "<EMAIL>",
      step_count: 12,
      latest_activity: new Date(Date.now() - 420000).toISOString(), // 7 minutes ago
      name: "Error Handling Test"
    }
  ],
  
  // Mock reports data for the reports page
  reports: [
    {
      id: "TSN-123456",
      name: "Smoke Test Run",
      type: "Smoke Test",
      environment: "Development",
      startTime: "2025-04-01T10:00:00Z",
      endTime: "2025-04-01T10:05:30Z",
      duration: 330,
      status: "completed",
      totalCases: 5,
      passedCases: 5,
      failedCases: 0,
      skippedCases: 0,
      user: "<EMAIL>",
      trigger: "Manual"
    },
    {
      id: "TSN-123457",
      name: "Regression Test Run",
      type: "Regression Test",
      environment: "Development",
      startTime: "2025-04-01T11:00:00Z",
      endTime: "2025-04-01T11:15:45Z",
      duration: 945,
      status: "failed",
      totalCases: 10,
      passedCases: 7,
      failedCases: 2,
      skippedCases: 1,
      user: "<EMAIL>",
      trigger: "Scheduled"
    },
    {
      id: "TSN-123458",
      name: "Performance Test Run",
      type: "Performance Test",
      environment: "Staging",
      startTime: "2025-04-01T12:00:00Z",
      endTime: "2025-04-01T12:10:15Z",
      duration: 615,
      status: "completed",
      totalCases: 3,
      passedCases: 3,
      failedCases: 0,
      skippedCases: 0,
      user: "<EMAIL>",
      trigger: "Manual"
    },
    {
      id: "TSN-123459",
      name: "Security Test Run",
      type: "Security Test",
      environment: "Production",
      startTime: "2025-04-01T13:00:00Z",
      endTime: "2025-04-01T13:08:30Z",
      duration: 510,
      status: "failed",
      totalCases: 4,
      passedCases: 2,
      failedCases: 2,
      skippedCases: 0,
      user: "<EMAIL>",
      trigger: "Manual"
    },
    {
      id: "TSN-123460",
      name: "Custom Test Run",
      type: "Custom Test",
      environment: "Development",
      startTime: "2025-04-02T09:00:00Z",
      endTime: "2025-04-02T09:07:45Z",
      duration: 465,
      status: "completed",
      totalCases: 6,
      passedCases: 6,
      failedCases: 0,
      skippedCases: 0,
      user: "<EMAIL>",
      trigger: "Manual"
    }
  ]
};

// Start with a null active test run
let activeTestRun = null;

// Add direct mocking capability as fallback when service worker fails
const mockApi = {
  // Original fetch function to restore later
  originalFetch: null,
  
  // Track if direct mocking is enabled
  directMockingEnabled: false,
  
  // Enable direct fetch API mocking (without service worker)
  enableDirectMocking: function() {
    if (this.directMockingEnabled) {
      console.log('Direct API mocking already enabled');
      return;
    }
    
    try {
      console.log('Enabling direct fetch API mocking as fallback');
      
      // Store original fetch
      this.originalFetch = window.fetch;
      
      // Override fetch with our custom implementation
      window.fetch = this.mockFetch.bind(this);
      
      this.directMockingEnabled = true;
      console.log('Direct API mocking successfully enabled');
    } catch (error) {
      console.error('Failed to enable direct API mocking:', error);
    }
  },
  
  // Disable direct fetch mocking
  disableDirectMocking: function() {
    if (!this.directMockingEnabled) {
      return;
    }
    
    // Restore original fetch
    if (this.originalFetch) {
      window.fetch = this.originalFetch;
    }
    
    this.directMockingEnabled = false;
    console.log('Direct API mocking disabled');
  },
  
  // Custom fetch implementation for direct mocking
  mockFetch: async function(url, options = {}) {
    // Convert URL object to string if needed
    const urlString = url.toString();
    
    // Only intercept requests to our target API
    if (urlString.includes('/AutoRun/') || urlString.includes('/api/')) {
      console.log(`[Direct Mock] Intercepting request to: ${urlString}`, options);
      
      // Determine what type of request we're handling
      let response = null;
      
      try {
        // Process route and create mock response
        response = await processMockRequest(urlString, options);
      } catch (error) {
        console.error('[Direct Mock] Error processing request:', error);
        // Return error response
        return new Response(JSON.stringify({ 
          error: true, 
          message: `Mock API error: ${error.message || 'Unknown error'}`
        }), {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }
      
      // If we got a mock response, return it
      if (response) {
        return response;
      }
    }
    
    // For any requests we don't want to mock, pass through to original fetch
    if (this.originalFetch) {
      return this.originalFetch(url, options);
    } else {
      // Fallback to current fetch (should not happen)
      console.warn('[Direct Mock] Original fetch not available, using current fetch');
      return fetch(url, options);
    }
  }
};

// Make the mockApi object available globally
window.mockApi = mockApi;

// Helper functions for request processing
function handleCaseRunner(params) {
  const newTsnId = 12770 + Math.floor(Math.random() * 100);
  
  // Start a new test run
  if (params.tc_id) {
    // Single test case execution
    activeTestRun = {
      tsn_id: newTsnId,
      tc_id: parseInt(params.tc_id),
      start_time: new Date().toISOString(),
      status: "running",
      progress: 0,
      expected_duration: "10s"
    };
    
    // Simulate test progress
    simulateTestProgress(activeTestRun);
    
    return {
      success: true,
      message: `Test case ${params.tc_id} execution initiated`,
      data: { 
        tsn_id: newTsnId,
        status: "started"
      }
    };
  } else if (params.ts_id) {
    // Test suite execution
    activeTestRun = {
      tsn_id: newTsnId,
      ts_id: parseInt(params.ts_id),
      start_time: new Date().toISOString(),
      status: "running",
      progress: 0,
      expected_duration: "15m"
    };
    
    // Simulate test suite progress
    simulateTestSuiteProgress(activeTestRun);
    
    return {
      success: true,
      message: `Test suite ${params.ts_id} execution initiated`,
      data: { 
        tsn_id: newTsnId,
        status: "started"
      }
    };
  }
  
  return {
    success: false,
    message: "Missing test case or test suite ID",
    data: {}
  };
}

function handleDynamicSuiteRunner(params) {
  const newTsnId = 12770 + Math.floor(Math.random() * 100);
  
  try {
    // Extract test cases and other parameters
    let testCases = [];
    let suiteName = 'Dynamic Test Suite';
    let description = 'Dynamically created test suite';
    
    // Parse parameters
    if (params.test_cases) {
      // Handle different formats of test_cases
      if (typeof params.test_cases === 'string') {
        try {
          // Try parsing as JSON
          testCases = JSON.parse(params.test_cases);
        } catch (e) {
          // If not JSON, try parsing as comma-separated values
          testCases = params.test_cases.split(',').map(id => parseInt(id.trim(), 10));
        }
      } else if (Array.isArray(params.test_cases)) {
        // If it's already an array, use it directly
        testCases = params.test_cases.map(tc => {
          // Handle both simple ID arrays and object arrays with id property
          return typeof tc === 'object' && tc.id ? tc.id : tc;
        });
      }
    }
    
    if (params.name) {
      suiteName = params.name;
    }
    
    if (params.description) {
      description = params.description;
    }
    
    console.log('Creating dynamic test suite with test cases:', testCases);
    
    // Create a temporary dynamic test suite
    activeTestRun = {
      tsn_id: newTsnId,
      name: suiteName,
      description: description,
      testCases: testCases,
      start_time: new Date().toISOString(),
      status: "running",
      progress: 0,
      expected_duration: "5m"
    };
    
    // Simulate test suite progress
    simulateTestSuiteProgress(activeTestRun);
    
    return {
      success: true,
      message: `Dynamic test suite "${suiteName}" created and execution initiated`,
      data: { 
        tsn_id: newTsnId,
        status: "started"
      }
    };
  } catch (error) {
    console.error('Error creating dynamic test suite:', error);
    return {
      success: false,
      message: `Failed to create dynamic test suite: ${error.message}`,
      data: {}
    };
  }
}

function handleReportSummary(params) {
  const { tsn_id, testId } = params;
  
  // If there's an active test run with this ID, return its status
  if (activeTestRun && activeTestRun.tsn_id === parseInt(tsn_id)) {
    // Generate mock test cases for the active test run
    const testCases = [];
    const totalCases = activeTestRun.testCases ? activeTestRun.testCases.length : 5;
    const passedCases = activeTestRun.status === 'completed' ? totalCases : Math.floor(totalCases * 0.7);
    const failedCases = activeTestRun.status === 'failed' ? Math.floor(totalCases * 0.2) : 0;
    const skippedCases = totalCases - passedCases - failedCases;
    
    for (let i = 0; i < totalCases; i++) {
      let status = 'passed';
      let errorMessage = null;
      
      if (i < failedCases) {
        status = 'failed';
        errorMessage = 'Test assertion failed: Expected true but got false';
      } else if (i < failedCases + skippedCases) {
        status = 'skipped';
        errorMessage = 'Test skipped due to dependency failure';
      }
      
      testCases.push({
        id: `TC-${i+1}`,
        name: `Test Case ${i+1}`,
        description: `Test case ${i+1} for ${activeTestRun.name}`,
        status: status,
        duration: Math.floor(Math.random() * 10) + 1,
        errorMessage: errorMessage
      });
    }
    
    return {
      id: activeTestRun.tsn_id.toString(),
      name: activeTestRun.name,
      type: activeTestRun.type || 'Custom Test',
      environment: 'Development',
      startTime: activeTestRun.start_time,
      endTime: activeTestRun.status !== 'running' ? new Date().toISOString() : null,
      duration: Math.floor(Math.random() * 300) + 60,
      status: activeTestRun.status,
      totalCases: totalCases,
      passedCases: passedCases,
      failedCases: failedCases,
      skippedCases: skippedCases,
      user: '<EMAIL>',
      trigger: 'Manual',
      testCases: testCases
    };
  }
  
  // Look for a matching report in the mock data
  const report = mockData.reports.find(r => r.id === tsn_id);
  if (report) {
    // Generate mock test cases for this report
    const testCases = [];
    for (let i = 0; i < report.totalCases; i++) {
      let status = 'passed';
      let errorMessage = null;
      
      if (i < report.failedCases) {
        status = 'failed';
        errorMessage = 'Test assertion failed: Expected true but got false';
      } else if (i < report.failedCases + report.skippedCases) {
        status = 'skipped';
        errorMessage = 'Test skipped due to dependency failure';
      }
      
      testCases.push({
        id: `TC-${report.id}-${i+1}`,
        name: `Test Case ${i+1}`,
        description: `Test case ${i+1} for ${report.type}`,
        status: status,
        duration: Math.floor(Math.random() * 10) + 1,
        errorMessage: errorMessage
      });
    }
    
    // Add test cases to the report
    return {
      ...report,
      testCases: testCases
    };
  }
  
  return {
    success: false,
    message: `No report found for tsn_id: ${tsn_id}`
  };
}

function handleGetReports(params) {
  const { timeRange, testId } = params;
  
  // If testId is provided, return details for a specific test
  if (testId) {
    const report = mockData.reports.find(r => r.id === testId);
    if (report) {
      // Generate mock test cases for this report
      const testCases = [];
      for (let i = 0; i < report.totalCases; i++) {
        let status = 'passed';
        let errorMessage = null;
        
        if (i < report.failedCases) {
          status = 'failed';
          errorMessage = 'Test assertion failed: Expected true but got false';
        } else if (i < report.failedCases + report.skippedCases) {
          status = 'skipped';
          errorMessage = 'Test skipped due to dependency failure';
        }
        
        testCases.push({
          id: `TC-${report.id}-${i+1}`,
          name: `Test Case ${i+1}`,
          description: `Test case ${i+1} for ${report.type}`,
          status: status,
          duration: Math.floor(Math.random() * 10) + 1,
          errorMessage: errorMessage
        });
      }
      
      // Return test details in the format expected by the UI
      return {
        test: {
          ...report,
          testCases: testCases
        }
      };
    }
    return { success: false, message: "Test not found" };
  }
  
  // Return all mock reports
  return {
    reports: mockData.reports
  };
}

function handleTestStatus(params) {
  const tsn_id = params.tsn_id;
  
  if (activeTestRun && activeTestRun.tsn_id == tsn_id) {
    return {
      success: true,
      message: "Status retrieved successfully",
      data: {
        tsn_id: activeTestRun.tsn_id,
        status: activeTestRun.status,
        progress: activeTestRun.progress,
        message: `Test execution ${activeTestRun.status}. Progress: ${activeTestRun.progress}%`
      }
    };
  }
  
  return {
    success: false,
    message: "Test run not found",
    data: {}
  };
}

function handleExecutionLog(params) {
  const tsn_id = params.tsn_id;
  
  if (activeTestRun && activeTestRun.tsn_id == tsn_id) {
    return {
      success: true,
      message: "Execution log retrieved successfully",
      data: {
        tsn_id: activeTestRun.tsn_id,
        logs: [
          { timestamp: new Date(Date.now() - 5000).toISOString(), level: "INFO", message: "Test execution started" },
          { timestamp: new Date(Date.now() - 4000).toISOString(), level: "INFO", message: "Initializing test environment" },
          { timestamp: new Date(Date.now() - 3000).toISOString(), level: "INFO", message: "Running test steps" },
          { timestamp: new Date(Date.now() - 2000).toISOString(), level: "INFO", message: `Current progress: ${activeTestRun.progress}%` },
          { timestamp: new Date().toISOString(), level: "INFO", message: "Test execution in progress" }
        ]
      }
    };
  }
  
  return {
    success: false,
    message: "Test run not found",
    data: {}
  };
}

function handleStopTest(params) {
  const { tsn_id } = params;
  
  if (activeTestRun && activeTestRun.tsn_id === parseInt(tsn_id)) {
    activeTestRun.status = "stopped";
    return { success: true, message: "Test stopped successfully" };
  }
  
  return { success: false, message: "Test not found or already completed" };
}

function handleRerunTests(params) {
  const { failed_tsn_id, rerun_type } = params;
  
  // Generate a new test run ID
  const newTsnId = Math.floor(Math.random() * 1000000) + 1000000;
  
  // Get the original test run report to identify failed tests
  const originalReport = handleReportSummary({ tsn_id: failed_tsn_id });
  
  if (!originalReport || !originalReport.testCases) {
    return { success: false, message: "Could not retrieve test cases from the original run" };
  }
  
  // Filter out failed test cases
  const failedTests = originalReport.testCases.filter(tc => 
    tc.status === 'failed' || tc.status === 'error');
  
  if (failedTests.length === 0) {
    return { success: false, message: "No failed tests found in the original run" };
  }
  
  // Create a new test run with only the failed test cases
  activeTestRun = {
    tsn_id: newTsnId,
    name: `Rerun of Failed Tests (${failed_tsn_id})`,
    description: `Rerunning ${failedTests.length} failed tests from test run ${failed_tsn_id}`,
    testCases: failedTests.map(tc => tc.id),
    start_time: new Date().toISOString(),
    status: "running",
    progress: 0,
    expected_duration: "3m",
    original_run_id: failed_tsn_id
  };
  
  // Simulate test suite progress
  simulateTestSuiteProgress(activeTestRun);
  
  return {
    success: true,
    message: `Rerunning ${failedTests.length} failed tests`,
    tsn_id: newTsnId
  };
}

// Function to process a mock request and generate a response
// This is used by both the service worker and direct mocking
async function processMockRequest(url, options = {}) {
  const urlObj = new URL(url, 'http://localhost');
  const endpoint = urlObj.pathname.split('/').pop();
  
  console.log('Mock API: Processing request for endpoint', endpoint);
  
  // Default response
  let responseData = { 
    success: true, 
    message: 'Operation completed successfully',
    data: {}
  };
  
  let status = 200;
  let contentType = 'application/json';
  
  // Extract request parameters
  let requestParams = {};
  
  // Parse URL parameters
  urlObj.searchParams.forEach((value, key) => {
    requestParams[key] = value;
  });
  
  // Copy params to requestParams
  const params = { ...requestParams };
  
  // Parse request body if it exists
  if (options.body) {
    try {
      // Handle URL-encoded form data
      if (typeof options.body === 'string') {
        const searchParams = new URLSearchParams(options.body);
        searchParams.forEach((value, key) => {
          requestParams[key] = value;
        });
      } else if (typeof options.body === 'object') {
        // Handle JSON data
        if (options.body instanceof FormData) {
          options.body.forEach((value, key) => {
            requestParams[key] = value;
          });
        } else if (options.headers && options.headers['Content-Type'] === 'application/json') {
          Object.assign(requestParams, JSON.parse(options.body));
        } else {
          Object.assign(requestParams, options.body);
        }
      }
    } catch (error) {
      console.error('Error parsing request body:', error);
    }
  }
  
  console.log('Mock API: Request parameters', requestParams);
  
  // Handle different endpoints
  switch (endpoint) {
    case 'CaseRunner':
      responseData = handleCaseRunner(requestParams);
      break;
    case 'DynamicSuiteRunner':
      responseData = handleDynamicSuiteRunner(requestParams);
      break;
    case 'ReportSummary':
      responseData = handleReportSummary(requestParams);
      break;
    case 'TestStatus':
      responseData = handleTestStatus(requestParams);
      break;
    case 'ExecutionLog':
      responseData = handleExecutionLog(requestParams);
      break;
    case 'StopTest':
      responseData = handleStopTest(requestParams);
      break;
    case 'RerunTests':
      responseData = handleRerunTests(requestParams);
      break;
    case 'GetTestSuites':
      responseData = { testSuites: mockData.testSuites };
      break;
    case 'GetTestCases':
      responseData = { testCases: mockData.testCases };
      break;
    case 'GetReports':
      responseData = handleGetReports(requestParams);
      break;
    case 'ActiveTests':
    case 'active-tests':
      // Return mock active tests data
      return {
        success: true,
        data: mockData.activeTests,
        message: 'Active tests retrieved successfully'
      };
    case 'ActiveSessions':
      // Return mock active tests as sessions
      return {
        success: true,
        data: mockData.activeTests,
        message: 'Active sessions retrieved successfully'
      };
    default:
      // Check if this is a request for reports data
      if (url.includes('webhook/test-results')) {
        responseData = handleGetReports(requestParams);
        break;
      }
      // Check if this is a request for test cases
      if (url.includes('GetTestCases') || url.includes('test-cases')) {
        responseData = { testCases: mockData.testCases };
        break;
      }
      // Unknown endpoint
      status = 404;
      responseData = {
        success: false,
        message: `Unknown endpoint: ${endpoint}`,
        data: {}
      };
  }
  
  // Create and return mock response
  return new Response(
    typeof responseData === 'string' ? responseData : JSON.stringify(responseData), 
    {
      status: status,
      headers: {
        'Content-Type': contentType
      }
    }
  );
}

// Function to simulate test progress
function simulateTestProgress(testRun) {
  let interval = setInterval(() => {
    // Increment progress
    testRun.progress += 20;
    
    // Check if test is complete
    if (testRun.progress >= 100) {
      testRun.progress = 100;
      testRun.status = 'completed';
      clearInterval(interval);
    }
    
    console.log(`Mock API: Test ${testRun.tsn_id} progress: ${testRun.progress}%`);
  }, 1000);
}

// Function to simulate test suite progress
function simulateTestSuiteProgress(testRun) {
  let interval = setInterval(() => {
    // Increment progress
    testRun.progress += 10;
    
    // Check if test is complete
    if (testRun.progress >= 100) {
      testRun.progress = 100;
      testRun.status = 'completed';
      clearInterval(interval);
    }
    
    console.log(`Mock API: Test suite ${testRun.tsn_id} progress: ${testRun.progress}%`);
  }, 1500);
}

// Override fetch API to intercept network requests
const originalFetch = window.fetch;
window.fetch = function(url, options) {
  console.log('Mock API: Intercepted fetch request to', url);
  
  // Get request body parameters if they exist
  let params = {};
  if (options && options.body) {
    try {
      // Handle URL-encoded form data
      if (typeof options.body === 'string') {
        const searchParams = new URLSearchParams(options.body);
        searchParams.forEach((value, key) => {
          params[key] = value;
        });
      } else if (typeof options.body === 'object') {
        // Handle JSON data
        if (options.body instanceof FormData) {
          options.body.forEach((value, key) => {
            params[key] = value;
          });
        } else if (options.headers && options.headers['Content-Type'] === 'application/json') {
          params = JSON.parse(options.body);
        } else {
          params = options.body;
        }
      }
    } catch (error) {
      console.error('Error parsing request body:', error);
    }
  }
  
  // Also parse URL parameters
  if (typeof url === 'string' && url.includes('?')) {
    const urlParts = url.split('?');
    const searchParams = new URLSearchParams(urlParts[1]);
    searchParams.forEach((value, key) => {
      params[key] = value;
    });
  }
  
  // Check if this is an API request we want to mock
  if (typeof url === 'string' && (url.includes('AutoRun') || url.includes('/api/'))) {
    return new Promise(resolve => {
      setTimeout(() => {
        // Create a mock response based on the URL and parameters
        let responseData = { 
          success: true, 
          message: 'Operation completed successfully',
          data: {}
        };
        
        // Handle different endpoints
        if (url.includes('CaseRunner')) {
          // Test execution initiation
          const newTsnId = 12770 + Math.floor(Math.random() * 100);
          
          // Start a new test run
          if (params.tc_id) {
            // Single test case execution
            activeTestRun = {
              tsn_id: newTsnId,
              tc_id: parseInt(params.tc_id),
              start_time: new Date().toISOString(),
              status: "running",
              progress: 0,
              expected_duration: "10s"
            };
            
            responseData.data = { 
              tsn_id: newTsnId,
              status: "started",
              message: `Test case ${params.tc_id} execution initiated`
            };
            
            // Simulate test progress
            simulateTestProgress(activeTestRun);
          } else if (params.ts_id) {
            // Test suite execution
            activeTestRun = {
              tsn_id: newTsnId,
              ts_id: parseInt(params.ts_id),
              start_time: new Date().toISOString(),
              status: "running",
              progress: 0,
              expected_duration: "15m"
            };
            
            responseData.data = { 
              tsn_id: newTsnId,
              status: "started",
              message: `Test suite ${params.ts_id} execution initiated`
            };
            
            // Simulate test suite progress
            simulateTestSuiteProgress(activeTestRun);
          }
        } else if (url.includes('StopTest')) {
          // Stop test execution
          if (activeTestRun) {
            activeTestRun.status = "stopped";
            responseData.data = {
              tsn_id: params.tsn_id || activeTestRun.tsn_id,
              status: "stopped",
              message: "Test execution stopped successfully"
            };
          } else {
            responseData.success = false;
            responseData.message = "No active test to stop";
          }
        } else if (url.includes('ReportSummary')) {
          // Report summary endpoint
          const tsn_id = params.tsn_id;
          if (tsn_id == 12762) {
            responseData.data = mockData.testRuns.singleTestRun;
          } else if (tsn_id == 12767) {
            responseData.data = mockData.testRuns.suiteTestRun;
          } else if (activeTestRun && activeTestRun.tsn_id == tsn_id) {
            // Generate a report for the active test run
            if (activeTestRun.tc_id) {
              // Single test case report
              responseData.data = {
                tsn_id: activeTestRun.tsn_id,
                creation_time: activeTestRun.start_time,
                completion_time: activeTestRun.status === 'completed' ? new Date().toISOString() : null,
                duration: activeTestRun.status === 'completed' ? '10s' : 'In progress',
                tests: [
                  { tc_id: activeTestRun.tc_id, outcome: activeTestRun.status === 'completed' ? 'P' : 'I', name: "Test Case Execution" }
                ],
                summary: {
                  totalTests: 1,
                  passed: activeTestRun.status === 'completed' ? 1 : 0,
                  failed: 0,
                  skipped: 0
                },
                details: [
                  { seq_index: 1, txt: "Test execution in progress", outcome: "I" }
                ]
              };
            } else {
              // Test suite report
              responseData.data = {
                tsn_id: activeTestRun.tsn_id,
                creation_time: activeTestRun.start_time,
                completion_time: activeTestRun.status === 'completed' ? new Date().toISOString() : null,
                duration: activeTestRun.status === 'completed' ? '15m' : 'In progress',
                tests: [
                  { tc_id: 101, outcome: activeTestRun.progress > 30 ? 'P' : 'I', name: "Test Case 101" },
                  { tc_id: 102, outcome: activeTestRun.progress > 60 ? 'P' : 'I', name: "Test Case 102" },
                  { tc_id: 103, outcome: activeTestRun.progress > 90 ? 'P' : 'I', name: "Test Case 103" }
                ],
                summary: {
                  totalTests: 3,
                  passed: Math.floor(activeTestRun.progress / 30),
                  failed: 0,
                  skipped: 0
                },
                details: [
                  { seq_index: 1, txt: "Test suite execution in progress", outcome: "I" }
                ]
              };
            }
          } else {
            responseData.success = false;
            responseData.message = "Test run not found";
          }
        } else if (url.includes('TestStatus')) {
          // Test status check endpoint
          const tsn_id = params.tsn_id;
          
          if (activeTestRun && activeTestRun.tsn_id == tsn_id) {
            responseData.data = {
              tsn_id: activeTestRun.tsn_id,
              status: activeTestRun.status,
              progress: activeTestRun.progress,
              message: `Test execution ${activeTestRun.status}. Progress: ${activeTestRun.progress}%`
            };
          } else {
            responseData.success = false;
            responseData.message = "Test run not found";
          }
        } else if (url.includes('DynamicTestSuite') || url.includes('CreateDynamicSuite')) {
          // Handle dynamic test suite creation and execution
          const newTsnId = 12770 + Math.floor(Math.random() * 100);
          
          try {
            // Extract test cases and other parameters
            let testCases = [];
            let suiteName = 'Dynamic Test Suite';
            let description = 'Dynamically created test suite';
            
            // Parse parameters
            if (params.test_cases) {
              testCases = Array.isArray(params.test_cases) ? params.test_cases : JSON.parse(params.test_cases);
            }
            
            if (params.name) {
              suiteName = params.name;
            }
            
            if (params.description) {
              description = params.description;
            }
            
            // Create a temporary dynamic test suite
            activeTestRun = {
              tsn_id: newTsnId,
              name: suiteName,
              description: description,
              testCases: testCases,
              start_time: new Date().toISOString(),
              status: "running",
              progress: 0,
              expected_duration: "5m"
            };
            
            // Ensure the response data structure matches what the frontend expects
            responseData = {
              success: true,
              message: `Dynamic test suite "${suiteName}" created and execution initiated`,
              data: { 
                tsn_id: newTsnId,
                status: "started"
              }
            };
            
            // Simulate test suite progress
            simulateTestSuiteProgress(activeTestRun);
            
          } catch (error) {
            console.error('Error creating dynamic test suite:', error);
            responseData.success = false;
            responseData.message = `Failed to create dynamic test suite: ${error.message}`;
            responseData.data = {};
          }
        } else if (url.includes('GetTestCases')) {
          // Return all test cases
          responseData.data = mockData.testCases;
        } else if (url.includes('GetTestSuites')) {
          // Return all test suites
          responseData.data = mockData.testSuites;
        } else {
          // Default response for unknown endpoints
          responseData.success = false;
          responseData.message = `Endpoint not implemented in mock API: ${url}`;
        }
        
        // Create and return a Response object
        const response = new Response(JSON.stringify(responseData), {
          status: 200,
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        resolve(response);
      }, 500); // Add a slight delay to simulate network latency
    });
  }
  
  // For all other requests, use the original fetch
  return originalFetch.apply(this, arguments);
};

// Wait for DOM content to ensure API service is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('Enhanced Mock API Service initialized');
  
  // Check if we need to inject the API service
  if (!window.apiService) {
    console.log('Creating API service');
    // Create a basic API service
    window.apiService = {
      credentials: {
        uid: "<EMAIL>",
        password: "password123"
      },
      
      loadCredentials() {
        return true;
      },
      
      setCredentials(uid, password) {
        this.credentials.uid = uid;
        this.credentials.password = password;
      },
      
      async getRequest(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/api/${endpoint}?${queryString}`;
        const response = await fetch(url);
        const data = await response.json();
        return data.data;
      },
      
      async postRequest(endpoint, params = {}) {
        const url = `/api/${endpoint}`;
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(params)
        });
        const data = await response.json();
        return data.data;
      },
      
      async getTestSuites() {
        return this.getRequest('GetTestSuites');
      },
      
      async getTestCases() {
        return this.getRequest('GetTestCases');
      },
      
      async runTestCase(tcId, params = {}) {
        const requestParams = {
          ...this.credentials,
          tc_id: tcId,
          ...params
        };
        const response = await fetch('/AutoRun/CaseRunner', {
          method: 'POST',
          body: new URLSearchParams(requestParams)
        });
        const data = await response.json();
        return data.data.tsn_id;
      },
      
      async runTestSuite(tsId, params = {}) {
        const requestParams = {
          ...this.credentials,
          ts_id: tsId,
          ...params
        };
        const response = await fetch('/AutoRun/CaseRunner', {
          method: 'POST',
          body: new URLSearchParams(requestParams)
        });
        const data = await response.json();
        return data.data.tsn_id;
      },
      
      async runDynamicTestSuite(params = {}) {
        const requestParams = {
          ...this.credentials,
          ...params
        };
        const response = await fetch('/AutoRun/DynamicTestSuite', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestParams)
        });
        const data = await response.json();
        if (!data.success) {
          throw new Error(data.message || 'Failed to run dynamic test suite');
        }
        // Ensure tsn_id is available in the response
        if (!data.data || !data.data.tsn_id) {
          console.error('Missing tsn_id in dynamic test suite response', data);
          throw new Error('Invalid response: Missing test run ID');
        }
        return data.data.tsn_id;
      },
      
      async stopTest(tsnId) {
        const response = await fetch('/AutoRun/StopTest', {
          method: 'POST',
          body: new URLSearchParams({
            ...this.credentials,
            tsn_id: tsnId
          })
        });
        const data = await response.json();
        return data.success;
      },
      
      async getTestStatus(tsnId) {
        const response = await fetch(`/AutoRun/TestStatus?tsn_id=${tsnId}`);
        const data = await response.json();
        return data.data;
      },
      
      async getReportSummary(tsnId) {
        const response = await fetch(`/AutoRun/ReportSummary?tsn_id=${tsnId}`);
        const data = await response.json();
        return data.data;
      },
      
      async getActiveTests() {
        const response = await fetch('/AutoRun/ActiveTests');
        const data = await response.json();
        return data.data;
      }
    };
  }
});

document.addEventListener('DOMContentLoaded', () => {
  console.log('Enhanced Mock API Service initialized');
  
  // Check if we need to inject the API service
  if (!window.apiService) {
    console.log('Creating API service');
    // Create a basic API service
    window.apiService = {
      credentials: {
        uid: "<EMAIL>",
        password: "password123"
      },
      
      loadCredentials() {
        return true;
      },
      
      setCredentials(uid, password) {
        this.credentials.uid = uid;
        this.credentials.password = password;
      },
      
      async getRequest(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/api/${endpoint}?${queryString}`;
        const response = await fetch(url);
        const data = await response.json();
        return data.data;
      },
      
      async postRequest(endpoint, params = {}) {
        const url = `/api/${endpoint}`;
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(params)
        });
        const data = await response.json();
        return data.data;
      },
      
      async getTestSuites() {
        return this.getRequest('GetTestSuites');
      },
      
      async getTestCases() {
        return this.getRequest('GetTestCases');
      },
      
      async runTestCase(tcId, params = {}) {
        const requestParams = {
          ...this.credentials,
          tc_id: tcId,
          ...params
        };
        const response = await fetch('/AutoRun/CaseRunner', {
          method: 'POST',
          body: new URLSearchParams(requestParams)
        });
        const data = await response.json();
        return data.data.tsn_id;
      },
      
      async runTestSuite(tsId, params = {}) {
        const requestParams = {
          ...this.credentials,
          ts_id: tsId,
          ...params
        };
        const response = await fetch('/AutoRun/CaseRunner', {
          method: 'POST',
          body: new URLSearchParams(requestParams)
        });
        const data = await response.json();
        return data.data.tsn_id;
      },
      
      async runDynamicTestSuite(params = {}) {
        const requestParams = {
          ...this.credentials,
          ...params
        };
        const response = await fetch('/AutoRun/DynamicTestSuite', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestParams)
        });
        const data = await response.json();
        if (!data.success) {
          throw new Error(data.message || 'Failed to run dynamic test suite');
        }
        // Ensure tsn_id is available in the response
        if (!data.data || !data.data.tsn_id) {
          console.error('Missing tsn_id in dynamic test suite response', data);
          throw new Error('Invalid response: Missing test run ID');
        }
        return data.data.tsn_id;
      },
      
      async stopTest(tsnId) {
        const response = await fetch('/AutoRun/StopTest', {
          method: 'POST',
          body: new URLSearchParams({
            ...this.credentials,
            tsn_id: tsnId
          })
        });
        const data = await response.json();
        return data.success;
      },
      
      async getTestStatus(tsnId) {
        const response = await fetch(`/AutoRun/TestStatus?tsn_id=${tsnId}`);
        const data = await response.json();
        return data.data;
      },
      
      async getReportSummary(tsnId) {
        const response = await fetch(`/AutoRun/ReportSummary?tsn_id=${tsnId}`);
        const data = await response.json();
        return data.data;
      },
      
      async getActiveTests() {
        const response = await fetch('/AutoRun/ActiveTests');
        const data = await response.json();
        return data.data;
      }
    };
  }
});
