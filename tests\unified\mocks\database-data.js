/**
 * Database Mock Data for Unified Testing Architecture
 * 
 * This file provides comprehensive mock data that mirrors actual database structures:
 * - Test cases with real field names and data types
 * - Test suites with hierarchical relationships
 * - Test sessions with execution history
 * - Test results with detailed outcomes
 * 
 * All mock data uses actual database schema and realistic values
 */

// Test Cases Mock Data (from test_case table)
const testCases = [
  {
    tc_id: 1001,
    tc_name: 'User Login Validation',
    tc_description: 'Validates user login functionality with valid credentials',
    tc_type: 'Functional',
    tc_priority: 'High',
    tc_status: 'Active',
    tc_created_date: '2023-01-15 10:30:00',
    tc_modified_date: '2023-01-20 14:45:00',
    tc_created_by: 'test_user',
    tc_project_id: 1,
    tc_module_id: 101,
    tc_estimated_time: 300, // seconds
    tc_complexity: 'Medium'
  },
  {
    tc_id: 1002,
    tc_name: 'Payment Processing Flow',
    tc_description: 'Tests end-to-end payment processing with various payment methods',
    tc_type: 'Integration',
    tc_priority: 'Critical',
    tc_status: 'Active',
    tc_created_date: '2023-01-16 09:15:00',
    tc_modified_date: '2023-01-22 11:30:00',
    tc_created_by: 'test_user',
    tc_project_id: 1,
    tc_module_id: 102,
    tc_estimated_time: 600,
    tc_complexity: 'High'
  },
  {
    tc_id: 1003,
    tc_name: 'Data Validation Rules',
    tc_description: 'Validates input data validation rules and error handling',
    tc_type: 'Validation',
    tc_priority: 'Medium',
    tc_status: 'Active',
    tc_created_date: '2023-01-17 13:20:00',
    tc_modified_date: '2023-01-23 16:10:00',
    tc_created_by: 'test_user',
    tc_project_id: 1,
    tc_module_id: 103,
    tc_estimated_time: 450,
    tc_complexity: 'Medium'
  }
];

// Test Suites Mock Data (from test_suite table)
const testSuites = [
  {
    ts_id: 2001,
    ts_name: 'Regression Test Suite',
    ts_description: 'Comprehensive regression testing for core functionality',
    ts_type: 'Regression',
    ts_status: 'Active',
    ts_created_date: '2023-01-10 08:00:00',
    ts_modified_date: '2023-01-25 12:00:00',
    ts_created_by: 'test_manager',
    ts_project_id: 1,
    ts_estimated_time: 7200, // 2 hours
    ts_test_case_count: 25
  },
  {
    ts_id: 2002,
    ts_name: 'Smoke Test Suite',
    ts_description: 'Quick smoke tests for basic functionality verification',
    ts_type: 'Smoke',
    ts_status: 'Active',
    ts_created_date: '2023-01-12 10:30:00',
    ts_modified_date: '2023-01-26 14:15:00',
    ts_created_by: 'test_manager',
    ts_project_id: 1,
    ts_estimated_time: 1800, // 30 minutes
    ts_test_case_count: 8
  },
  {
    ts_id: 2003,
    ts_name: 'Performance Test Suite',
    ts_description: 'Performance and load testing scenarios',
    ts_type: 'Performance',
    ts_status: 'Active',
    ts_created_date: '2023-01-14 15:45:00',
    ts_modified_date: '2023-01-27 09:30:00',
    ts_created_by: 'perf_tester',
    ts_project_id: 1,
    ts_estimated_time: 10800, // 3 hours
    ts_test_case_count: 15
  }
];

// Test Sessions Mock Data (from test_session table)
const testSessions = [
  {
    tsn_id: 13782,
    ts_id: 2001,
    tc_id: null, // Suite execution
    tsn_name: 'Regression Suite Run #1',
    tsn_status: 'Completed',
    tsn_start_time: '2023-01-28 09:00:00',
    tsn_end_time: '2023-01-28 11:15:00',
    tsn_duration: 8100, // seconds
    tsn_environment: 'QA02',
    tsn_executed_by: 'automation_user',
    tsn_total_tests: 25,
    tsn_passed_tests: 23,
    tsn_failed_tests: 2,
    tsn_skipped_tests: 0,
    tsn_pass_rate: 92.0,
    tsn_created_date: '2023-01-28 09:00:00',
    tsn_notes: 'Automated regression run - 2 failures in payment module'
  },
  {
    tsn_id: 13783,
    ts_id: 2002,
    tc_id: null,
    tsn_name: 'Smoke Test Run #5',
    tsn_status: 'Completed',
    tsn_start_time: '2023-01-28 14:30:00',
    tsn_end_time: '2023-01-28 14:55:00',
    tsn_duration: 1500,
    tsn_environment: 'QA01',
    tsn_executed_by: 'automation_user',
    tsn_total_tests: 8,
    tsn_passed_tests: 8,
    tsn_failed_tests: 0,
    tsn_skipped_tests: 0,
    tsn_pass_rate: 100.0,
    tsn_created_date: '2023-01-28 14:30:00',
    tsn_notes: 'All smoke tests passed successfully'
  },
  {
    tsn_id: 13784,
    ts_id: null,
    tc_id: 1001,
    tsn_name: 'Single Test Case Run',
    tsn_status: 'Running',
    tsn_start_time: '2023-01-28 16:00:00',
    tsn_end_time: null,
    tsn_duration: null,
    tsn_environment: 'QA02',
    tsn_executed_by: 'manual_tester',
    tsn_total_tests: 1,
    tsn_passed_tests: 0,
    tsn_failed_tests: 0,
    tsn_skipped_tests: 0,
    tsn_pass_rate: null,
    tsn_created_date: '2023-01-28 16:00:00',
    tsn_notes: 'Manual execution in progress'
  }
];

// Test Results Mock Data (from test_result table)
const testResults = [
  {
    tr_id: 5001,
    tsn_id: 13782,
    tc_id: 1001,
    tr_status: 'Passed',
    tr_start_time: '2023-01-28 09:05:00',
    tr_end_time: '2023-01-28 09:10:00',
    tr_duration: 300,
    tr_error_message: null,
    tr_stack_trace: null,
    tr_screenshot_path: '/screenshots/test_1001_pass.png',
    tr_log_file_path: '/logs/test_1001.log',
    tr_created_date: '2023-01-28 09:10:00'
  },
  {
    tr_id: 5002,
    tsn_id: 13782,
    tc_id: 1002,
    tr_status: 'Failed',
    tr_start_time: '2023-01-28 09:15:00',
    tr_end_time: '2023-01-28 09:25:00',
    tr_duration: 600,
    tr_error_message: 'Payment gateway timeout - connection failed after 30 seconds',
    tr_stack_trace: 'PaymentException: Gateway timeout\n    at PaymentProcessor.process(PaymentProcessor.java:45)\n    at TestCase1002.execute(TestCase1002.java:23)',
    tr_screenshot_path: '/screenshots/test_1002_fail.png',
    tr_log_file_path: '/logs/test_1002.log',
    tr_created_date: '2023-01-28 09:25:00'
  },
  {
    tr_id: 5003,
    tsn_id: 13783,
    tc_id: 1003,
    tr_status: 'Passed',
    tr_start_time: '2023-01-28 14:35:00',
    tr_end_time: '2023-01-28 14:42:00',
    tr_duration: 420,
    tr_error_message: null,
    tr_stack_trace: null,
    tr_screenshot_path: '/screenshots/test_1003_pass.png',
    tr_log_file_path: '/logs/test_1003.log',
    tr_created_date: '2023-01-28 14:42:00'
  }
];

// Active Tests (derived from test_session table with status = 'Running')
const activeTests = testSessions.filter(session => session.tsn_status === 'Running');

// Recent Runs (last 10 test sessions ordered by start time)
const recentRuns = testSessions
  .sort((a, b) => new Date(b.tsn_start_time) - new Date(a.tsn_start_time))
  .slice(0, 10);

// Database Query Result Formatters
const formatters = {
  /**
   * Format test cases for API response
   */
  formatTestCases: (cases = testCases) => {
    return cases.map(tc => ({
      tc_id: tc.tc_id,
      tc_name: tc.tc_name,
      tc_description: tc.tc_description,
      tc_type: tc.tc_type,
      tc_priority: tc.tc_priority,
      tc_status: tc.tc_status,
      tc_created_date: tc.tc_created_date,
      tc_estimated_time: tc.tc_estimated_time,
      tc_complexity: tc.tc_complexity
    }));
  },

  /**
   * Format test suites for API response
   */
  formatTestSuites: (suites = testSuites) => {
    return suites.map(ts => ({
      ts_id: ts.ts_id,
      ts_name: ts.ts_name,
      ts_description: ts.ts_description,
      ts_type: ts.ts_type,
      ts_status: ts.ts_status,
      ts_created_date: ts.ts_created_date,
      ts_estimated_time: ts.ts_estimated_time,
      ts_test_case_count: ts.ts_test_case_count
    }));
  },

  /**
   * Format test sessions for API response
   */
  formatTestSessions: (sessions = testSessions) => {
    return sessions.map(tsn => ({
      tsn_id: tsn.tsn_id,
      tsn_name: tsn.tsn_name,
      tsn_status: tsn.tsn_status,
      tsn_start_time: tsn.tsn_start_time,
      tsn_end_time: tsn.tsn_end_time,
      tsn_duration: tsn.tsn_duration,
      tsn_environment: tsn.tsn_environment,
      tsn_total_tests: tsn.tsn_total_tests,
      tsn_passed_tests: tsn.tsn_passed_tests,
      tsn_failed_tests: tsn.tsn_failed_tests,
      tsn_pass_rate: tsn.tsn_pass_rate
    }));
  },

  /**
   * Format test results for API response
   */
  formatTestResults: (results = testResults) => {
    return results.map(tr => ({
      tr_id: tr.tr_id,
      tsn_id: tr.tsn_id,
      tc_id: tr.tc_id,
      tr_status: tr.tr_status,
      tr_start_time: tr.tr_start_time,
      tr_end_time: tr.tr_end_time,
      tr_duration: tr.tr_duration,
      tr_error_message: tr.tr_error_message,
      tr_screenshot_path: tr.tr_screenshot_path
    }));
  }
};

// Data Generators for Dynamic Testing
const generators = {
  /**
   * Generate a test case with optional overrides
   */
  generateTestCase: (overrides = {}) => {
    const baseCase = testCases[0];
    return {
      ...baseCase,
      tc_id: Math.floor(Math.random() * 10000) + 1000,
      tc_created_date: new Date().toISOString().slice(0, 19).replace('T', ' '),
      tc_modified_date: new Date().toISOString().slice(0, 19).replace('T', ' '),
      ...overrides
    };
  },

  /**
   * Generate a test session with optional overrides
   */
  generateTestSession: (overrides = {}) => {
    const baseSession = testSessions[0];
    return {
      ...baseSession,
      tsn_id: Math.floor(Math.random() * 10000) + 10000,
      tsn_start_time: new Date().toISOString().slice(0, 19).replace('T', ' '),
      tsn_created_date: new Date().toISOString().slice(0, 19).replace('T', ' '),
      ...overrides
    };
  },

  /**
   * Generate multiple test cases
   */
  generateTestCases: (count = 5, overrides = {}) => {
    return Array.from({ length: count }, (_, index) => 
      generators.generateTestCase({ 
        tc_id: 1000 + index,
        tc_name: `Generated Test Case ${index + 1}`,
        ...overrides 
      })
    );
  }
};

// Export all mock data and utilities
module.exports = {
  // Raw data
  testCases,
  testSuites,
  testSessions,
  testResults,
  activeTests,
  recentRuns,
  
  // Formatters
  formatters,
  
  // Generators
  generators,
  
  // Quick access methods
  getTestCases: (filters = {}) => {
    let cases = [...testCases];
    if (filters.limit) cases = cases.slice(0, filters.limit);
    return formatters.formatTestCases(cases);
  },
  
  getTestSuites: (filters = {}) => {
    let suites = [...testSuites];
    if (filters.limit) suites = suites.slice(0, filters.limit);
    return formatters.formatTestSuites(suites);
  },
  
  getRecentRuns: (filters = {}) => {
    let runs = [...recentRuns];
    if (filters.limit) runs = runs.slice(0, filters.limit);
    return formatters.formatTestSessions(runs);
  },
  
  getActiveTests: (filters = {}) => {
    let active = [...activeTests];
    if (filters.limit) active = active.slice(0, filters.limit);
    return formatters.formatTestSessions(active);
  }
};
