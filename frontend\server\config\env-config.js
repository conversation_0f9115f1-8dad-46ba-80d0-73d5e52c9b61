/**
 * Environment configuration
 */
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Ensure the URL has a trailing slash
const getBaseUrl = () => {
  const baseUrl = process.env.MPRTS_BASE_URL || 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/';
  return baseUrl.endsWith('/') ? baseUrl : baseUrl + '/';
};

const MPTSR_BASE_URL = getBaseUrl();

module.exports = {
  MPTSR_BASE_URL
};
