/**
 * Root Jest configuration for SmartTest
 * 
 * This configuration allows running all tests in the project,
 * including both server tests and reports tests.
 */

module.exports = {
  // The test environment that will be used for testing
  testEnvironment: 'jsdom',
  
  // The glob patterns <PERSON><PERSON> uses to detect test files
  testMatch: [
    '**/frontend/server/**/*.test.js',
    '**/frontend/reports/tests/**/*.test.js'
  ],
  
  // An array of regexp pattern strings that are matched against all test paths
  testPathIgnorePatterns: [
    '/node_modules/'
  ],
  
  // Indicates whether each individual test should be reported during the run
  verbose: true,
  
  // Automatically clear mock calls and instances between every test
  clearMocks: true,
  
  // Indicates whether the coverage information should be collected while executing the test
  collectCoverage: true,
  
  // The directory where Jest should output its coverage files
  coverageDirectory: 'coverage',
  
  // An array of regexp pattern strings used to skip coverage collection
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/tests/mocks/'
  ],
  
  // A list of reporter names that <PERSON><PERSON> uses when writing coverage reports
  coverageReporters: [
    'json',
    'text',
    'lcov',
    'clover',
    'html'
  ],
  
  // An array of file extensions your modules use
  moduleFileExtensions: [
    'js',
    'json',
    'jsx',
    'ts',
    'tsx',
    'node'
  ],
  
  // A map from regular expressions to module names that allow to stub out resources
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': '<rootDir>/frontend/reports/tests/mocks/styleMock.js',
    '\\.(gif|ttf|eot|svg|png)$': '<rootDir>/frontend/reports/tests/mocks/fileMock.js'
  },
  
  // The paths to modules that run some code to configure or set up the testing environment
  setupFiles: [
    '<rootDir>/jest.setup.js'
  ],
  
  // Projects configuration for multi-project setup
  projects: [
    {
      displayName: 'server',
      testMatch: ['<rootDir>/frontend/server/**/*.test.js'],
      testEnvironment: 'node'
    },
    {
      displayName: 'reports',
      testMatch: ['<rootDir>/frontend/reports/tests/**/*.test.js'],
      testEnvironment: 'jsdom',
      setupFiles: ['<rootDir>/frontend/reports/tests/setup.js'],
      setupFilesAfterEnv: ['<rootDir>/frontend/reports/tests/setupAfterEnv.js']
    }
  ]
};
