/**
 * Unit tests for the Session ID Service
 *
 * These tests verify that the Session ID Service correctly:
 * - Retrieves session IDs from multiple sources
 * - Caches session IDs in local storage
 * - Falls back to alternative sources when needed
 */

describe('SessionIdService', () => {
  let service;

  // Mock credentials
  const mockCredentials = {
    uid: '<EMAIL>',
    password: 'test123'
  };

  // Mock session IDs
  const mockSessionIds = ['13782', '13781', '13780', '13779', '13778'];

  // Setup before each test
  beforeEach(() => {
    // Use the global service instance
    service = window.sessionIdService;

    // Mock localStorage
    global.localStorage = {
      getItem: jest.fn(),
      setItem: jest.fn(),
      removeItem: jest.fn()
    };

    // Mock fetch
    global.fetch = jest.fn();
  });

  // Cleanup after each test
  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('getRecentSessionIds', () => {
    test('should return cached session IDs if available', async () => {
      // Mock getCachedSessionIds to return session IDs
      service.getCachedSessionIds = jest.fn().mockReturnValue(mockSessionIds);

      // Call getRecentSessionIds
      const result = await service.getRecentSessionIds(mockCredentials, 3);

      // Verify getCachedSessionIds was called
      expect(service.getCachedSessionIds).toHaveBeenCalled();

      // Verify getSessionIdsFromApi was not called
      expect(global.fetch).not.toHaveBeenCalled();

      // Verify the result is the cached session IDs (limited to 3)
      expect(result).toEqual(mockSessionIds.slice(0, 3));
    });

    test('should fetch session IDs from API if cache is empty', async () => {
      // Mock getCachedSessionIds to return null
      service.getCachedSessionIds = jest.fn().mockReturnValue(null);

      // Mock getSessionIdsFromApi to return session IDs
      service.getSessionIdsFromApi = jest.fn().mockResolvedValue(mockSessionIds);

      // Mock cacheSessionIds
      service.cacheSessionIds = jest.fn();

      // Call getRecentSessionIds
      const result = await service.getRecentSessionIds(mockCredentials, 3);

      // Verify getCachedSessionIds was called
      expect(service.getCachedSessionIds).toHaveBeenCalled();

      // Verify getSessionIdsFromApi was called with correct parameters
      expect(service.getSessionIdsFromApi).toHaveBeenCalledWith(mockCredentials, 3);

      // Verify cacheSessionIds was called with the session IDs
      expect(service.cacheSessionIds).toHaveBeenCalledWith(mockSessionIds);

      // Verify the result is the API session IDs (limited to 3)
      expect(result).toEqual(mockSessionIds.slice(0, 3));
    });

    test('should fall back to hardcoded IDs if API fails', async () => {
      // Mock getCachedSessionIds to return null
      service.getCachedSessionIds = jest.fn().mockReturnValue(null);

      // Mock getSessionIdsFromApi to throw an error
      service.getSessionIdsFromApi = jest.fn().mockRejectedValue(
        new Error('API error')
      );

      // Call getRecentSessionIds
      const result = await service.getRecentSessionIds(mockCredentials, 3);

      // Verify getCachedSessionIds was called
      expect(service.getCachedSessionIds).toHaveBeenCalled();

      // Verify getSessionIdsFromApi was called
      expect(service.getSessionIdsFromApi).toHaveBeenCalled();

      // Verify the result is the fallback IDs (limited to 3)
      expect(result).toEqual(service.fallbackIds.slice(0, 3));
    });

    test('should fall back to hardcoded IDs if API returns empty array', async () => {
      // Mock getCachedSessionIds to return null
      service.getCachedSessionIds = jest.fn().mockReturnValue(null);

      // Mock getSessionIdsFromApi to return empty array
      service.getSessionIdsFromApi = jest.fn().mockResolvedValue([]);

      // Call getRecentSessionIds
      const result = await service.getRecentSessionIds(mockCredentials, 3);

      // Verify getCachedSessionIds was called
      expect(service.getCachedSessionIds).toHaveBeenCalled();

      // Verify getSessionIdsFromApi was called
      expect(service.getSessionIdsFromApi).toHaveBeenCalled();

      // Verify the result is the fallback IDs (limited to 3)
      expect(result).toEqual(service.fallbackIds.slice(0, 3));
    });

    test('should handle errors and return fallback IDs', async () => {
      // Mock getCachedSessionIds to throw an error
      service.getCachedSessionIds = jest.fn().mockImplementation(() => {
        throw new Error('Cache error');
      });

      // Call getRecentSessionIds
      const result = await service.getRecentSessionIds(mockCredentials, 3);

      // Verify the result is the fallback IDs (limited to 3)
      expect(result).toEqual(service.fallbackIds.slice(0, 3));
    });
  });

  describe('getCachedSessionIds', () => {
    test('should return cached session IDs if valid', () => {
      // Mock localStorage.getItem to return a valid cache
      const mockCache = {
        ids: mockSessionIds,
        expiry: Date.now() + 1000000 // Far in the future
      };
      global.localStorage.getItem.mockReturnValue(JSON.stringify(mockCache));

      // Call getCachedSessionIds
      const result = service.getCachedSessionIds();

      // Verify localStorage.getItem was called with the correct key
      expect(global.localStorage.getItem).toHaveBeenCalledWith(service.cacheKey);

      // Verify the result is the cached session IDs
      expect(result).toEqual(mockSessionIds);
    });

    test('should return null if cache is expired', () => {
      // Mock localStorage.getItem to return an expired cache
      const mockCache = {
        ids: mockSessionIds,
        expiry: Date.now() - 1000 // In the past
      };
      global.localStorage.getItem.mockReturnValue(JSON.stringify(mockCache));

      // Call getCachedSessionIds
      const result = service.getCachedSessionIds();

      // Verify localStorage.getItem was called
      expect(global.localStorage.getItem).toHaveBeenCalled();

      // Verify localStorage.removeItem was called to remove the expired cache
      expect(global.localStorage.removeItem).toHaveBeenCalledWith(service.cacheKey);

      // Verify the result is null
      expect(result).toBeNull();
    });

    test('should return null if cache is not found', () => {
      // Mock localStorage.getItem to return null
      global.localStorage.getItem.mockReturnValue(null);

      // Call getCachedSessionIds
      const result = service.getCachedSessionIds();

      // Verify localStorage.getItem was called
      expect(global.localStorage.getItem).toHaveBeenCalled();

      // Verify the result is null
      expect(result).toBeNull();
    });

    test('should handle invalid JSON in cache', () => {
      // Mock localStorage.getItem to return invalid JSON
      global.localStorage.getItem.mockReturnValue('invalid json');

      // Call getCachedSessionIds
      const result = service.getCachedSessionIds();

      // Verify the result is null
      expect(result).toBeNull();
    });
  });

  describe('cacheSessionIds', () => {
    test('should cache session IDs in localStorage', () => {
      // Call cacheSessionIds
      service.cacheSessionIds(mockSessionIds);

      // Verify localStorage.setItem was called with the correct key
      expect(global.localStorage.setItem).toHaveBeenCalledWith(
        service.cacheKey,
        expect.any(String)
      );

      // Verify the cache contains the session IDs and an expiry time
      const cacheArg = JSON.parse(global.localStorage.setItem.mock.calls[0][1]);
      expect(cacheArg.ids).toEqual(mockSessionIds);
      expect(cacheArg.expiry).toBeGreaterThan(Date.now());
    });

    test('should handle localStorage errors', () => {
      // Mock localStorage.setItem to throw an error
      global.localStorage.setItem.mockImplementation(() => {
        throw new Error('Storage error');
      });

      // Call cacheSessionIds
      service.cacheSessionIds(mockSessionIds);

      // Verify the error was caught (no exception thrown)
      expect(global.localStorage.setItem).toHaveBeenCalled();
    });
  });

  describe('getSessionIdsFromApi', () => {
    test('should fetch session IDs from API', async () => {
      // Mock fetch to return a successful response
      global.fetch.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({
          success: true,
          data: [
            { tsn_id: 13782 },
            { tsn_id: 13781 },
            { tsn_id: 13780 }
          ]
        })
      });

      // Call getSessionIdsFromApi
      const result = await service.getSessionIdsFromApi(mockCredentials, 3);

      // Verify fetch was called with the correct URL and parameters
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/local/recent-runs')
      );
      const url = new URL(global.fetch.mock.calls[0][0]);
      expect(url.searchParams.get('uid')).toBe(mockCredentials.uid);
      expect(url.searchParams.get('password')).toBe(mockCredentials.password);
      expect(url.searchParams.get('limit')).toBe('3');

      // Verify the result is the session IDs
      expect(result).toEqual(['13782', '13781', '13780']);
    });

    test('should throw an error if API request fails', async () => {
      // Mock fetch to return an error response
      global.fetch.mockResolvedValue({
        ok: false,
        status: 500
      });

      // Expect getSessionIdsFromApi to throw an error
      await expect(service.getSessionIdsFromApi(mockCredentials, 3))
        .rejects.toThrow('API request failed with status 500');
    });

    test('should throw an error if API returns an error', async () => {
      // Mock fetch to return a success response with an error message
      global.fetch.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({
          success: false,
          message: 'API error'
        })
      });

      // Expect getSessionIdsFromApi to throw an error
      await expect(service.getSessionIdsFromApi(mockCredentials, 3))
        .rejects.toThrow('API error');
    });

    test('should handle missing or invalid data in API response', async () => {
      // Mock fetch to return a success response with missing data
      global.fetch.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({
          success: true
          // No data field
        })
      });

      // Call getSessionIdsFromApi
      const result = await service.getSessionIdsFromApi(mockCredentials, 3);

      // Verify the result is an empty array
      expect(result).toEqual([]);
    });
  });
});
