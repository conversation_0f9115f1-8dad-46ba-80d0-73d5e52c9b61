/**
 * Global Test Setup for Unified SmartTest Architecture
 * 
 * This file configures the testing environment for all test types:
 * - Unit tests
 * - Integration tests  
 * - End-to-end tests
 * 
 * Consolidates setup from:
 * - frontend/reports/tests/
 * - frontend/server/tests/
 * - Root level tests
 */

// Import required modules
const path = require('path');
const fs = require('fs');

// Global test configuration
global.TEST_CONFIG = {
  // Test environment
  environment: 'test',
  
  // Timeouts
  defaultTimeout: 30000,
  integrationTimeout: 60000,
  e2eTimeout: 120000,
  
  // Mock data paths
  mockDataPath: path.join(__dirname, 'mocks'),
  
  // Test utilities
  testUtilsPath: path.join(__dirname, 'utils'),
  
  // Coverage thresholds
  coverageThresholds: {
    statements: 85,
    branches: 80,
    functions: 85,
    lines: 85
  }
};

// Mock external dependencies that should not be called during tests
jest.mock('ssh2', () => ({
  Client: jest.fn().mockImplementation(() => ({
    connect: jest.fn(),
    exec: jest.fn(),
    end: jest.fn(),
    on: jest.fn()
  }))
}));

// Mock MySQL connections
jest.mock('mysql2/promise', () => ({
  createConnection: jest.fn().mockResolvedValue({
    execute: jest.fn(),
    query: jest.fn(),
    end: jest.fn(),
    destroy: jest.fn()
  }),
  createPool: jest.fn().mockReturnValue({
    getConnection: jest.fn().mockResolvedValue({
      execute: jest.fn(),
      query: jest.fn(),
      release: jest.fn()
    }),
    end: jest.fn()
  })
}));

// Mock external HTTP requests
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    request: jest.fn()
  })),
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  request: jest.fn()
}));

// Mock file system operations for tests
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  readFileSync: jest.fn(),
  writeFileSync: jest.fn(),
  existsSync: jest.fn(),
  mkdirSync: jest.fn()
}));

// Global test utilities
global.testUtils = {
  // Load mock data
  loadMockData: (filename) => {
    const mockPath = path.join(global.TEST_CONFIG.mockDataPath, filename);
    if (fs.existsSync(mockPath)) {
      return require(mockPath);
    }
    throw new Error(`Mock data file not found: ${filename}`);
  },
  
  // Create mock API response
  createMockApiResponse: (data, status = 200) => ({
    data,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    headers: { 'content-type': 'application/json' },
    config: {}
  }),
  
  // Create mock database result
  createMockDbResult: (rows, fields = []) => ({
    rows,
    fields,
    rowCount: rows.length,
    command: 'SELECT'
  }),
  
  // Wait for async operations
  waitFor: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Generate test data
  generateTestData: {
    testCase: (overrides = {}) => ({
      id: 'TEST_001',
      name: 'Sample Test Case',
      status: 'PASSED',
      duration: 1500,
      timestamp: new Date().toISOString(),
      ...overrides
    }),
    
    testRun: (overrides = {}) => ({
      id: 'RUN_001',
      sessionId: 'SESSION_001',
      startTime: new Date().toISOString(),
      endTime: new Date(Date.now() + 60000).toISOString(),
      status: 'COMPLETED',
      totalTests: 10,
      passedTests: 8,
      failedTests: 2,
      ...overrides
    }),
    
    apiResponse: (overrides = {}) => ({
      success: true,
      data: [],
      message: 'Success',
      timestamp: new Date().toISOString(),
      ...overrides
    })
  }
};

// Console override for cleaner test output
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
};

// Restore console for specific tests if needed
global.restoreConsole = () => {
  global.console = originalConsole;
};

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global setup for each test file
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
  
  // Reset console mocks
  global.console.log.mockClear();
  global.console.warn.mockClear();
  global.console.error.mockClear();
  global.console.info.mockClear();
  global.console.debug.mockClear();
});

// Global cleanup after each test
afterEach(() => {
  // Clean up any test artifacts
  jest.restoreAllMocks();
});

// Global setup for test suites
beforeAll(() => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.TEST_MODE = 'true';
  
  // Disable real network requests
  process.env.DISABLE_NETWORK = 'true';
});

// Global cleanup for test suites
afterAll(() => {
  // Clean up environment
  delete process.env.TEST_MODE;
  delete process.env.DISABLE_NETWORK;
});

// Export test configuration for use in tests
module.exports = {
  TEST_CONFIG: global.TEST_CONFIG,
  testUtils: global.testUtils
};
