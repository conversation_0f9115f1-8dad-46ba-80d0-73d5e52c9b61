/* Dashboard Styles - Microsoft Teams UI */

:root {
    /* Teams color palette */
    --teams-base: #f3f2f1;
    --teams-primary: #6264a7;
    --teams-primary-hover: #7174b4;
    --teams-success: #92c353;
    --teams-danger: #d13438;
    --teams-warning: #ffaa44;
    --teams-info: #2d8cff;
    --teams-dark: #252423;
    --teams-light: #ffffff;
    --teams-border: #e1dfdd;
    --teams-text: #252423;
    --teams-text-light: #f3f2f1;
}

body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', sans-serif;
    background-color: var(--teams-base);
    color: var(--teams-text);
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow-x: hidden;
}

/* Header */
.ms-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--teams-primary);
    color: var(--teams-light);
    padding: 0.75rem 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.ms-header-brand {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--teams-light);
    text-decoration: none;
}

.ms-header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ms-environment-display {
    font-size: 0.875rem;
    color: var(--teams-light);
}

/* Layout */
.ms-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 56px);
}

.ms-layout {
    display: flex;
    flex: 1;
}

/* Navigation */
.ms-nav {
    width: 240px;
    background-color: var(--teams-light);
    border-right: 1px solid var(--teams-border);
    overflow-y: auto;
}

.ms-nav-content {
    padding: 1rem 0;
}

.ms-nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ms-nav-item {
    margin: 0;
}

.ms-nav-link {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--teams-text);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 400;
    transition: background-color 0.2s;
}

.ms-nav-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.ms-nav-link-active {
    background-color: rgba(98, 100, 167, 0.1);
    border-left: 3px solid var(--teams-primary);
    font-weight: 600;
    color: var(--teams-primary);
}

/* Content */
.ms-content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

.ms-content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--teams-border);
}

.ms-section-title {
    margin: 1.5rem 0 1rem;
    font-weight: 600;
}

/* Grid */
.ms-grid {
    display: flex;
    flex-direction: column;
    margin: 0 -0.75rem;
    width: 100%;
}

.ms-grid-row {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 1.5rem;
    align-items: stretch;
    justify-content: space-between;
}

.ms-grid-col {
    padding: 0 0.75rem;
    margin-bottom: 1.5rem;
    box-sizing: border-box;
    display: flex;
}

.ms-sm3 {
    width: 25%;
    flex: 0 0 calc(25% - 1rem);
    max-width: calc(25% - 1rem);
}

.ms-sm6 {
    width: 50%;
    flex: 0 0 calc(50% - 1rem);
    max-width: calc(50% - 1rem);
}

/* Status Cards */
.ms-stat-card {
    border-radius: 4px;
    padding: 1.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    transition: transform 0.2s, box-shadow 0.2s;
    margin-bottom: 0;
    width: 100%;
    height: 100%;
    justify-content: center;
}

.ms-stat-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    /* Teams-specific styling for status titles */
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ms-stat-value {
    font-size: 2rem;
    font-weight: 700;
}

/* Status colors - Teams specific */
.ms-bgColor-themePrimary {
    background-color: var(--teams-primary);
}

.ms-bgColor-green {
    background-color: var(--teams-success);
}

.ms-bgColor-red {
    background-color: var(--teams-danger);
}

.ms-bgColor-orange {
    background-color: var(--teams-warning);
}

/* Table */
.ms-table-container {
    margin-bottom: 1.5rem;
    overflow-x: auto;
}

.ms-Table {
    width: 100%;
    border-collapse: collapse;
}

.ms-Table th {
    background-color: #f3f2f1;
    font-weight: 600;
    text-align: left;
    padding: 0.75rem;
    border-bottom: 1px solid var(--teams-border);
}

.ms-Table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--teams-border);
}

.ms-Table--fixed {
    table-layout: fixed;
}

.ms-Table tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Status text in table */
.ms-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ms-status-passed {
    background-color: rgba(146, 195, 83, 0.2);
    color: var(--teams-success);
}

.ms-status-failed {
    background-color: rgba(209, 52, 56, 0.2);
    color: var(--teams-danger);
}

.ms-status-skipped {
    background-color: rgba(255, 170, 68, 0.2);
    color: var(--teams-warning);
}

/* Buttons */
.ms-Button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    height: 32px;
    padding: 0 16px;
    border-radius: 2px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    outline: none;
}

.ms-Button--default {
    background-color: var(--teams-light);
    color: var(--teams-text);
    border: 1px solid var(--teams-border);
}

.ms-Button--default:hover {
    background-color: var(--teams-base);
}

.ms-Button--primary {
    background-color: var(--teams-primary);
    color: var(--teams-light);
}

.ms-Button--primary:hover {
    background-color: var(--teams-primary-hover);
}

/* Utilities */
.ms-hidden {
    display: none !important;
}

.ms-fontColor-white {
    color: var(--teams-light);
}

/* Media queries */
@media (max-width: 768px) {
    .ms-layout {
        flex-direction: column;
    }
    
    .ms-nav {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--teams-border);
    }
    
    .ms-sm3, .ms-sm6 {
        width: 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .ms-content-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .ms-content-actions {
        margin-top: 1rem;
    }
}

/* Fix for medium screens */
@media (min-width: 769px) and (max-width: 1200px) {
    .ms-sm3 {
        width: 50%;
        flex: 0 0 50%;
        max-width: 50%;
    }
}

/* API Integration UI Components */

/* Modal */
.ms-modal {
    display: none;
    position: fixed;
    z-index: 9999; /* Increased z-index */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.6); /* Darkened background */
}

.ms-modal.active {
    display: block !important;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.ms-modal-content {
    background-color: var(--teams-light);
    margin: 10% auto;
    padding: 20px;
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    width: 80%;
    max-width: 600px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
}

.ms-modal-close {
    position: absolute;
    top: 10px;
    right: 15px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.ms-modal-close:hover,
.ms-modal-close:focus {
    color: var(--teams-text);
    text-decoration: none;
}

/* Form Group */
.ms-form-group {
    margin-bottom: 1rem;
}

.ms-form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.ms-form-group input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--teams-border);
    border-radius: 2px;
    font-size: 14px;
}

.ms-form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

/* Card Container */
.ms-card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.ms-card {
    background-color: var(--teams-light);
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
    margin-bottom: 0;
    width: 100%;
    height: 100%;
    justify-content: center;
}

.ms-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ms-card h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
}

.ms-card p {
    margin: 0.5rem 0;
    font-size: 0.875rem;
}

/* Active Test Card */
.active-test-card {
    background-color: var(--teams-light);
    border-left: 4px solid var(--teams-info);
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.active-test-card.running {
    border-left-color: var(--teams-info);
}

.active-test-card.completed {
    border-left-color: var(--teams-success);
}

.active-test-card.failed {
    border-left-color: var(--teams-danger);
}

.active-test-card.stopped {
    border-left-color: var(--teams-warning);
}

.active-test-card h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
}

.active-test-card p {
    margin: 0.25rem 0;
    font-size: 0.875rem;
}

.button-container {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* Test Suite Card */
.test-suite-card {
    background-color: var(--teams-light);
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
}

.test-suite-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.test-suite-card h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
}

.test-suite-card p {
    margin: 0.5rem 0;
    font-size: 0.875rem;
    color: #666;
}

/* Recent Run Card */
.recent-run-card {
    background-color: var(--teams-light);
    border-left: 4px solid var(--teams-info);
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.recent-run-card.completed {
    border-left-color: var(--teams-success);
}

.recent-run-card.failed {
    border-left-color: var(--teams-danger);
}

.recent-run-card.running {
    border-left-color: var(--teams-info);
}

/* Loading Indicator */
.ms-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.ms-loading-message {
    margin-top: 1rem;
    font-weight: 600;
    color: var(--teams-primary);
}

/* Spinner */
.ms-Spinner {
    position: relative;
    width: 28px;
    height: 28px;
}

.ms-Spinner--large {
    width: 42px;
    height: 42px;
}

.ms-Spinner::before {
    content: '';
    box-sizing: border-box;
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 3px solid #f3f2f1;
    border-top-color: var(--teams-primary);
    animation: spinner 0.8s linear infinite;
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}

/* Notification Container */
.ms-notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1500;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
    max-width: 350px;
}

.ms-notification {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 12px 16px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    animation: slideIn 0.3s ease-out;
    min-width: 250px;
    max-width: 100%;
}

.ms-notification.success {
    border-left: 4px solid var(--teams-success);
}

.ms-notification.error {
    border-left: 4px solid var(--teams-danger);
}

.ms-notification.info {
    border-left: 4px solid var(--teams-info);
}

.ms-notification.fade-out {
    animation: fadeOut 0.5s ease-out forwards;
}

.ms-notification-message {
    flex: 1;
    font-size: 14px;
    margin-right: 8px;
}

.ms-notification-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 18px;
    padding: 0;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Empty Message */
.ms-empty-message {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-style: italic;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px dashed var(--teams-border);
}

/* User Info in Header */
.ms-user-info {
    font-size: 0.875rem;
    color: var(--teams-light);
    margin-right: 1rem;
}

/* Report Table */
.report-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.report-table th,
.report-table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid var(--teams-border);
}

.report-table th {
    background-color: #f3f2f1;
    font-weight: 600;
}

.report-summary {
    margin-top: 1rem;
}

/* API Explorer Link */
.api-explorer-link {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--teams-primary);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: background-color 0.2s;
}

.api-explorer-link:hover {
    background-color: var(--teams-primary-hover);
}

/* Predefined Test Suites */
.ms-predefined-grid {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    gap: 20px;
    margin-bottom: 24px;
}

.ms-predefined-card {
    background-color: #f9f9f9;
    border: 1px solid #edebe9;
    border-radius: 4px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    flex: 0 0 auto;
    width: 250px;
}

.ms-predefined-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ms-predefined-card h3 {
    margin-top: 0;
    margin-bottom: 8px;
    color: #323130;
}

.ms-predefined-card p {
    margin-bottom: 16px;
    color: #605e5c;
    min-height: 40px;
}

.ms-predefined-card .ms-Button {
    width: 100%;
}

/* Custom Test Suite Builder */
.ms-checkbox-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #edebe9;
    border-radius: 2px;
    padding: 8px;
    margin-bottom: 16px;
    background-color: #fff;
    position: relative;
}

.ms-checkbox-item {
    display: flex;
    flex-direction: column;
    padding: 16px 50px 16px 16px; /* Equal padding on all sides with extra space for checkbox */
    border-bottom: 1px solid #f3f2f1;
    position: relative;
    min-height: 24px; /* Ensure minimum height for proper spacing */
}

.ms-checkbox-item:last-child {
    border-bottom: none;
}

.ms-checkbox-item label {
    font-size: 14px;
    color: #323130;
    cursor: pointer;
    flex: 1;
    display: flex;
    align-items: center;
}

.checkbox-container {
    position: absolute;
    right: 16px; /* Equal distance from right edge */
    top: 50%; /* Center vertically */
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Standard checkbox styling */
.ms-checkbox-item input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
    width: 20px;
    height: 20px;
}

/* Ensure consistent spacing for all checkbox items */
.ms-checkbox-item:hover {
    background-color: #f3f2f1;
}

.ms-checkbox-item .ms-test-description {
    font-size: 12px;
    color: #605e5c;
    margin-top: 8px;
}

.ms-form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
}

/* Query History Styles */
.query-history {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.query-history h4 {
    margin: 0 0 0.5rem 0;
    color: #495057;
    font-size: 0.9rem;
}

.query-history ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.query-history li {
    padding: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.query-history li:last-child {
    border-bottom: none;
}

.query-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-right: 0.5rem;
}

.query-status.success {
    background-color: #d4edda;
    color: #155724;
}

.query-status.error {
    background-color: #f8d7da;
    color: #721c24;
}

.query-status.warning {
    background-color: #fff3cd;
    color: #856404;
}

.query-time {
    color: #6c757d;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.query-text {
    margin-top: 0.25rem;
    font-family: monospace;
    font-size: 0.85rem;
    color: #212529;
    word-break: break-word;
}

/* Active Test Card Styles */
.test-card {
    position: relative;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.test-card.running {
    border-left: 4px solid #007bff;
}

.test-card.completed {
    border-left: 4px solid #28a745;
}

.test-card.failed {
    border-left: 4px solid #dc3545;
}

.test-card.error {
    border-left: 4px solid #ffc107;
}

.test-card.stopped {
    border-left: 4px solid #6c757d;
}

.status-text {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.progress-bar {
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 0.5rem 0;
    position: relative;
}

.progress-bar::after {
    content: attr(data-progress);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 0.8rem;
    font-weight: 500;
    text-shadow: 0 0 2px rgba(0,0,0,0.5);
}

.messages {
    margin-top: 0.5rem;
}

.message {
    padding: 0.5rem;
    margin-bottom: 0.25rem;
    border-radius: 3px;
    font-size: 0.85rem;
}

.message.info {
    background-color: #cce5ff;
    color: #004085;
}

.message.warning {
    background-color: #fff3cd;
    color: #856404;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
}

/* Loading Indicator */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255,255,255,0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notifications */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    padding: 1rem;
    border-radius: 4px;
    color: #fff;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    background-color: #28a745;
}

.notification.error {
    background-color: #dc3545;
}

.notification.info {
    background-color: #17a2b8;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Report Styles */
.report-summary {
    margin-bottom: 20px;
}

.report-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.report-stat {
    background-color: #f3f2f1;
    padding: 10px;
    border-radius: 4px;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #605e5c;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
}

.text-success {
    color: var(--teams-success);
}

.text-danger {
    color: var(--teams-danger);
}

.text-warning {
    color: var(--teams-warning);
}

/* Table styles for reports */
#report-content table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

#report-content th {
    background-color: #f3f2f1;
    padding: 8px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #e1dfdd;
}

#report-content td {
    padding: 8px;
    border-bottom: 1px solid #e1dfdd;
}

#report-content tr:hover {
    background-color: #f9f9f9;
}

.error-message {
    color: var(--teams-danger);
    padding: 8px;
    font-size: 13px;
}

.ms-bgColor-sharedRedLight10 {
    background-color: #fde7e9;
}

.ms-bgColor-sharedYellowLight10 {
    background-color: #fff4ce;
}

/* Active Tests Panel Styles */
.ms-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.active-tests-filter {
  display: flex;
  gap: 8px;
}

.active-tests-filter-btn {
  background-color: #f1f3f5;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.active-tests-filter-btn:hover {
  background-color: #e9ecef;
}

.active-tests-filter-btn.active {
  background-color: #4285f4;
  color: white;
  border-color: #4285f4;
}

.active-test-card {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 16px;
  margin: 0 0 16px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
}

.active-test-card.current-user {
  border-left: 4px solid #4285f4;
}

.active-test-card.other-user {
  border-left: 4px solid #fbbc05;
}

.active-test-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.active-test-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.test-id {
  font-weight: bold;
  color: #333;
}

.initiator-badge {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.initiator-badge.self {
  background-color: #e8f0fe;
  color: #4285f4;
}

.initiator-badge.other {
  background-color: #fff8e1;
  color: #f57c00;
}

.test-info {
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.5;
}

.test-status {
  margin-top: 8px;
  font-weight: 500;
}

.active-test-card .button-container {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.test-activity {
  font-size: 12px;
  color: #666;
  font-style: italic;
  margin-top: 8px;
  margin-bottom: 8px;
}

/* Status indicators */
.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-indicator.running {
  background-color: #34a853;
  animation: pulse 1.5s infinite;
}

.status-indicator.completed {
  background-color: #107c10;
}

.status-indicator.failed {
  background-color: #d13438;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ms-section-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .active-tests-filter {
    margin-top: 12px;
    width: 100%;
    justify-content: space-between;
  }
}

/* Test Card Styles - Enhanced version */
.test-card {
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.test-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Status-based styling */
.test-card.running {
  border-left: 4px solid #34a853;
}

.test-card.passed {
  border-left: 4px solid #107c10;
}

.test-card.failed {
  border-left: 4px solid #d13438;
}

.test-card.error {
  border-left: 4px solid #ff8c00;
}

.test-card.pending {
  border-left: 4px solid #6264a7;
}

.test-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eaeaea;
}

.test-card .test-info {
  flex: 1;
}

.test-card .test-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 5px 0;
  color: #323130;
}

.test-card .test-id-session {
  font-size: 12px;
  color: #605e5c;
  margin-bottom: 5px;
}

.test-card .test-id-label, 
.test-card .session-label {
  font-weight: 500;
  margin-right: 3px;
}

.test-card .test-user-time {
  font-size: 12px;
  color: #605e5c;
  display: flex;
  justify-content: space-between;
}

.test-card .status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 6px;
}

.test-card .status-indicator.running {
  background-color: #34a853;
  animation: pulse 1.5s infinite;
}

.test-card .status-indicator.passed {
  background-color: #107c10;
}

.test-card .status-indicator.failed {
  background-color: #d13438;
}

.test-card .status-indicator.pending {
  background-color: #6264a7;
}

.test-card .status-text {
  font-weight: 600;
  font-size: 14px;
}

.test-card.running .status-text {
  color: #34a853;
}

.test-card.passed .status-text {
  color: #107c10;
}

.test-card.failed .status-text {
  color: #d13438;
}

.test-card-body {
  padding: 15px;
}

.test-card .progress-container {
  background-color: #f0f0f0;
  border-radius: 4px;
  height: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}

.test-card .progress-bar {
  height: 100%;
  text-align: center;
  font-size: 9px;
  line-height: 10px;
  color: white;
  font-weight: 600;
  transition: width 0.5s ease;
}

.test-card .progress-bar.running {
  background-color: #34a853;
}

.test-card .progress-bar.passed {
  background-color: #107c10;
}

.test-card .progress-bar.failed {
  background-color: #d13438;
}

.test-card .progress-bar.pending {
  background-color: #6264a7;
}

.test-card .test-progress {
  font-size: 13px;
  color: #605e5c;
  margin-top: 5px;
}

.test-card-footer {
  padding: 10px 15px;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.view-report-btn {
  background-color: #6264a7;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.view-report-btn:hover {
  background-color: #7174b4;
}

.stop-button {
  background-color: #d13438;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.stop-button:hover {
  background-color: #e74856;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.ms-empty-message {
  text-align: center;
  padding: 20px;
  color: #605e5c;
  font-size: 14px;
}

.ms-empty-message.error {
  color: #d13438;
}

/* Test Results Table */
#reports-table {
    width: 100%;
    border-collapse: collapse;
}

#reports-table th, 
#reports-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid var(--teams-border);
}

#reports-table th {
    background-color: #f5f5f5;
    font-weight: 600;
}

#reports-table .text-success {
    color: var(--teams-success);
    font-weight: 600;
}

#reports-table .text-danger {
    color: var(--teams-danger);
    font-weight: 600;
}

#reports-table .table-success {
    background-color: rgba(146, 195, 83, 0.1);
}

#reports-table .table-danger {
    background-color: rgba(209, 52, 56, 0.1);
}

.test-details-btn {
    min-width: auto;
    padding: 4px 12px;
}

/* Test Report Modal */
.ms-report-modal {
    max-width: 80%;
    max-height: 80vh;
    width: 800px;
    overflow: auto;
}

.ms-report-header {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--teams-border);
}

.ms-report-header h3 {
    margin-top: 0;
    margin-bottom: 12px;
    color: var(--teams-primary);
    font-size: 1.5rem;
}

.ms-report-meta {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px 16px;
    margin-bottom: 16px;
}

.ms-report-meta div {
    padding: 4px 0;
}

.ms-report-content {
    max-height: 50vh;
    overflow: auto;
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
}

.ms-report-content ul {
    padding-left: 20px;
}

.ms-modal-footer {
    margin-top: 20px;
    border-top: 1px solid var(--teams-border);
    padding-top: 12px;
    text-align: right;
}

.status-passed, .status-pass, .status-PASSED, .status-PASS {
    color: var(--teams-success);
    font-weight: 600;
}

.status-failed, .status-fail, .status-FAILED, .status-FAIL {
    color: var(--teams-danger);
    font-weight: 600;
}

.status-running, .status-RUNNING {
    color: var(--teams-info);
    font-weight: 600;
}

/* Add a tooltip for email display */
td[title] {
    position: relative;
    cursor: help;
}

td[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 0;
    background-color: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
}