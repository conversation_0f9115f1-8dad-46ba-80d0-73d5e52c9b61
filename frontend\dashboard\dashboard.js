// Dashboard JavaScript

// Make the notification function globally available
window.showNotification = function(title, message, type = 'info', duration = 3000) {
  console.log(`NOTIFICATION [${type}]: ${title} - ${message}`);
  // Implementation will be added by the UI framework
};

// Function to show login modal
function showLoginModal() {
  const loginModal = document.getElementById('login-modal');
  const loginButton = document.getElementById('login-button');
  const logoutButton = document.getElementById('logout-button');
  const userDisplay = document.getElementById('user-display');
  
  if (loginModal) {
    loginModal.style.display = 'block';
    loginModal.classList.add('active');
    
    // Focus on the username field
    const usernameField = document.getElementById('username');
    if (usernameField) {
      usernameField.focus();
    }
  }
  
  // Update button visibility
  if (loginButton) {
    loginButton.style.display = 'none';
  }
  if (logoutButton) {
    logoutButton.style.display = 'none';
  }
  if (userDisplay) {
    userDisplay.textContent = 'Not logged in';
  }
}

// Function to handle successful login
function handleSuccessfulLogin(username) {
  const loginButton = document.getElementById('login-button');
  const logoutButton = document.getElementById('logout-button');
  const userDisplay = document.getElementById('user-display');
  const loginModal = document.getElementById('login-modal');
  
  // Update button visibility
  if (loginButton) {
    loginButton.style.display = 'none';
  }
  if (logoutButton) {
    logoutButton.style.display = 'inline-block';
  }
  if (userDisplay) {
    userDisplay.textContent = `Logged in as: ${username}`;
  }
  if (loginModal) {
    loginModal.style.display = 'none';
    loginModal.classList.remove('active');
  }
}

// Function to handle logout
function handleLogout() {
  const loginButton = document.getElementById('login-button');
  const logoutButton = document.getElementById('logout-button');
  const userDisplay = document.getElementById('user-display');
  
  // Update button visibility
  if (loginButton) {
    loginButton.style.display = 'inline-block';
  }
  if (logoutButton) {
    logoutButton.style.display = 'none';
  }
  if (userDisplay) {
    userDisplay.textContent = 'Not logged in';
  }
  
  // Clear dashboard data from localStorage on logout
  localStorage.removeItem(config.storageKey);
  console.log('Dashboard data cleared from localStorage');
  
  // Show login modal
  showLoginModal();
}

// Configuration
const config = {
    n8nReportingEndpoint: 'http://localhost:5678/webhook/test-results',
    refreshInterval: 30000, // 30 seconds
    storageKey: 'smarttest_dashboard_data' // Storage key for dashboard data
};

// DOM Elements
const elements = {
    totalTests: document.getElementById('total-tests'),
    successfulTests: document.getElementById('successful-tests'),
    failedTests: document.getElementById('failed-tests'),
    runningTests: document.getElementById('running-tests'),
    recentTestsTable: document.getElementById('recent-tests-table'),
    refreshBtn: document.getElementById('refresh-btn'),
    environmentDisplay: document.getElementById('environment-display'),
    testResultsChart: document.getElementById('test-results-chart'),
    testDurationChart: document.getElementById('test-duration-chart'),
    predefinedSuitesContainer: document.getElementById('predefined-suites-container'),
    customSuiteBuilder: document.getElementById('available-testcases'),
    loginButton: document.getElementById('login-button') // Corrected ID to match HTML
};

// Charts
let resultsChart;
let durationChart;

// Data
let testData = {
    recent: [],
    summary: {
        total: 0,
        successful: 0,
        failed: 0,
        running: 0
    }
};

// Process test data from API response
function processTestData(data) {
    // Update test summary
    testData.summary = data.summary || {
        total: 0,
        successful: 0,
        failed: 0,
        running: 0
    };
    
    // Update recent tests
    testData.recent = data.recent || [];
    
    // Update environment display
    if (data.environment) {
        elements.environmentDisplay.textContent = `Environment: ${data.environment}`;
    }
    
    // Save to localStorage
    saveDashboardData();
    
    // Update dashboard UI
    updateDashboard();
}

// Save dashboard data to localStorage
function saveDashboardData() {
    try {
        const dataToSave = {
            summary: testData.summary,
            recent: testData.recent.slice(0, 10), // Limit recent data for storage efficiency
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem(config.storageKey, JSON.stringify(dataToSave));
        console.log('Dashboard data saved to localStorage');
    } catch (error) {
        console.error('Error saving to localStorage:', error);
    }
}

// Load dashboard data from localStorage
function loadDashboardData() {
    try {
        const savedData = localStorage.getItem(config.storageKey);
        if (savedData) {
            const data = JSON.parse(savedData);
            
            // Check if data is fresh (less than 24 hours old)
            const timestamp = new Date(data.timestamp);
            const now = new Date();
            const isDataFresh = (now - timestamp) < (24 * 60 * 60 * 1000); // 24 hours
            
            if (isDataFresh) {
                console.log('Loading dashboard data from localStorage');
                
                if (data.summary) {
                    testData.summary = data.summary;
                }
                
                if (data.recent && data.recent.length > 0) {
                    testData.recent = data.recent;
                }
                
                return true;
            } else {
                console.log('Stored dashboard data is outdated, not using it');
                return false;
            }
        }
    } catch (error) {
        console.error('Error loading from localStorage:', error);
    }
    return false;
}

// Initialize dashboard
function initDashboard() {
    console.log('Initializing dashboard...');
    
    // 1. Set up event listeners
    elements.refreshBtn.addEventListener('click', refreshDashboard);
    
    // Add login button event listener
    if (elements.loginButton) {
        elements.loginButton.addEventListener('click', showLoginModal);
    } else {
        console.error('Login button not found in DOM');
    }
    
    // Add logout button event listener
    const logoutButton = document.getElementById('logout-button');
    if (logoutButton) {
        logoutButton.addEventListener('click', handleLogout);
    }
    
    // 2. Create charts
    initCharts();
    
    // 3. Initialize API integration to get fresh data
    if (!window.dashboardApiIntegration && typeof DashboardApiIntegration !== 'undefined') {
        console.log('Creating dashboardApiIntegration instance manually');
        // Create the integration object if the class exists but wasn't instantiated
        window.dashboardApiIntegration = new DashboardApiIntegration();
    }
    
    if (window.dashboardApiIntegration) {
        // If API service is available, use it for real data
        window.dashboardApiIntegration.initialize().then(success => {
            if (success) {
                console.info('API Integration initialized successfully');
                
                // Check if user is logged in
                if (window.apiService && window.apiService.credentials && window.apiService.credentials.uid) {
                    handleSuccessfulLogin(window.apiService.credentials.uid);
                }
                
                // Check URL parameters after API is initialized
                handleUrlParameters();
            } else {
                console.warn('API Integration could not be initialized');
                // Don't load any mock data - just wait for user login
            }
        }).catch(error => {
            console.error('Error initializing API integration:', error);
            // Don't load any mock data - just wait for user login
        });
    } else {
        // API Integration not available
        console.warn('API Integration not available, waiting for user login');
        
        // Set up manual refresh interval, but don't show any mock data
        setInterval(refreshDashboard, config.refreshInterval);
    }
}

// Update dashboard with current data
function updateDashboard() {
    // Update summary statistics
    elements.totalTests.textContent = testData.summary.total;
    elements.successfulTests.textContent = testData.summary.successful;
    elements.failedTests.textContent = testData.summary.failed;
    elements.runningTests.textContent = testData.summary.running;
    
    // Update recent tests table
    updateRecentTestsTable();
    
    // Update charts if they exist
    if (resultsChart && durationChart) {
        updateCharts();
    }
    
    // Save current data to localStorage
    saveDashboardData();
}

// Handle URL parameters for specific actions
function handleUrlParameters() {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const testId = urlParams.get('testId');
    const rerunFailed = urlParams.get('rerunFailed');
    
    // If specific test ID is provided, focus on that test
    if (testId) {
        fetchSpecificTestData(testId);
    }
    
    // If rerunFailed parameter is provided, rerun failed tests from that test run
    if (rerunFailed && window.dashboardApiIntegration) {
        window.dashboardApiIntegration.rerunFailedTests(rerunFailed)
            .then(newTestId => {
                if (newTestId) {
                    // Update URL without reloading the page
                    const newUrl = new URL(window.location.href);
                    newUrl.searchParams.delete('rerunFailed');
                    newUrl.searchParams.set('testId', newTestId);
                    window.history.pushState({}, '', newUrl);
                    
                    // Focus on the new test
                    fetchSpecificTestData(newTestId);
                }
            })
            .catch(error => {
                console.error('Error handling rerunFailed parameter:', error);
                showNotification('Error', `Failed to rerun tests: ${error.message}`, 'error');
            });
    }
}

// Refresh dashboard data
function refreshDashboard() {
    // If API integration is available, use it
    if (window.dashboardApiIntegration && window.dashboardApiIntegration.loadDashboardData) {
        // Show loading indicator
        showLoading('Refreshing dashboard data...');
        
        // Refresh data through API integration
        window.dashboardApiIntegration.loadDashboardData().then(() => {
            hideLoading();
        }).catch(error => {
            console.error('Error refreshing dashboard data:', error);
            hideLoading();
        });
    } else {
        // Otherwise refresh from n8n endpoint (or use mock data)
        fetchDashboardData();
    }
}

// Show loading indicator
function showLoading(message = 'Loading...') {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        const loadingMessage = loadingIndicator.querySelector('.ms-loading-message');
        if (loadingMessage) {
            loadingMessage.textContent = message;
        }
        loadingIndicator.style.display = 'flex';
    }
}

// Hide loading indicator
function hideLoading() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }
}

// Initialize charts
function initCharts() {
    // Teams UI color palette for charts
    const chartColors = {
        passed: '#92c353',    // Teams success color
        failed: '#d13438',    // Teams danger color
        skipped: '#ffaa44'    // Teams warning color
    };
    
    // Test Results Pie Chart
    resultsChart = new Chart(elements.testResultsChart, {
        type: 'pie',
        data: {
            labels: ['Passed', 'Failed', 'Skipped'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: [chartColors.passed, chartColors.failed, chartColors.skipped]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Test Status Distribution',
                    font: {
                        family: "'Segoe UI', sans-serif",
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    labels: {
                        font: {
                            family: "'Segoe UI', sans-serif"
                        }
                    }
                }
            }
        }
    });
    
    // Test Duration Bar Chart
    durationChart = new Chart(elements.testDurationChart, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: 'Test Duration (seconds)',
                data: [],
                backgroundColor: '#6264a7' // Teams primary color
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Test Duration by Type',
                    font: {
                        family: "'Segoe UI', sans-serif",
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    labels: {
                        font: {
                            family: "'Segoe UI', sans-serif"
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: "'Segoe UI', sans-serif"
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: "'Segoe UI', sans-serif"
                        }
                    }
                }
            }
        }
    });
}

// Fetch dashboard data from n8n
async function fetchDashboardData() {
    try {
        const response = await fetch(config.n8nReportingEndpoint);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        processTestData(data);
        updateDashboard();
    } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Optionally show an error message on the dashboard
    }
}

// Fetch data for a specific test
async function fetchSpecificTestData(testId) {
    try {
        const response = await fetch(`${config.n8nReportingEndpoint}?testId=${testId}`);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        // Process and display the specific test data
        if (data.test) {
            // Add to recent tests if not already there
            if (!testData.recent.some(test => test.id === data.test.id)) {
                testData.recent.unshift(data.test);
                // Keep only the most recent 10 tests
                testData.recent = testData.recent.slice(0, 10);
            }
            updateDashboard();
            
            // Scroll to the test in the table
            highlightTest(testId);
        }
    } catch (error) {
        console.error('Error fetching specific test data:', error);
    }
}

// Update the recent tests table
function updateRecentTestsTable() {
    // Clear existing table content
    elements.recentTestsTable.innerHTML = '';
    
    // Add tests to table
    testData.recent.forEach(test => {
        const row = document.createElement('tr');
        row.id = `test-${test.id}`;
        
        // Define status class for styling using Teams UI classes
        let statusClass, statusText;
        
        switch(test.status) {
            case 'Success':
                statusClass = 'ms-status ms-status-passed';
                statusText = 'Passed';
                break;
            case 'Failed':
                statusClass = 'ms-status ms-status-failed';
                statusText = 'Failed';
                break;
            case 'Running':
                statusClass = 'ms-status ms-status-skipped';
                statusText = 'Skipped';
                break;
            default:
                statusClass = '';
                statusText = test.status;
        }
        
        // Format duration
        const duration = test.duration ? `${test.duration.toFixed(2)}s` : '-';
        
        // Create row content
        row.innerHTML = `
            <td>${test.id}</td>
            <td>${test.name || test.type}</td>
            <td>${test.environment}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
            <td>${new Date(test.startTime).toLocaleString()}</td>
            <td>${duration}</td>
            <td>
                <button class="ms-Button ms-Button--default view-details-btn" data-test-id="${test.id}">
                    <span class="ms-Button-label">View</span>
                </button>
            </td>
        `;
        
        // Add to table
        elements.recentTestsTable.appendChild(row);
        
        // Add event listener to view details button
        const viewBtn = row.querySelector('.view-details-btn');
        if (viewBtn) {
            viewBtn.addEventListener('click', () => {
                window.location.href = `../reports/index.html?testId=${test.id}`;
            });
        }
    });
    
    // Show message if no tests
    if (testData.recent.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="7" class="text-center">No test data available</td>`;
        elements.recentTestsTable.appendChild(row);
    }
}

// Update the charts with current data
function updateCharts() {
    // Update results chart
    resultsChart.data.datasets[0].data = [
        testData.summary.successful,
        testData.summary.failed,
        testData.summary.running
    ];
    resultsChart.update();
    
    // Process data for duration chart
    const testTypes = {};
    testData.recent.forEach(test => {
        if (test.duration && test.type) {
            if (!testTypes[test.type]) {
                testTypes[test.type] = [];
            }
            testTypes[test.type].push(test.duration);
        }
    });
    
    // Calculate average duration by type
    const labels = [];
    const durations = [];
    
    Object.entries(testTypes).forEach(([type, durationList]) => {
        const avgDuration = durationList.reduce((sum, duration) => sum + duration, 0) / durationList.length;
        labels.push(type);
        durations.push(avgDuration.toFixed(2));
    });
    
    // Update duration chart
    durationChart.data.labels = labels;
    durationChart.data.datasets[0].data = durations;
    durationChart.update();
}

// Highlight a specific test in the table
function highlightTest(testId) {
    const testRow = document.getElementById(`test-${testId}`);
    if (testRow) {
        // Scroll to the row
        testRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Highlight the row
        testRow.classList.add('ms-row-highlight');
        
        // Remove highlight after a few seconds
        setTimeout(() => {
            testRow.classList.remove('ms-row-highlight');
        }, 3000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initDashboard);