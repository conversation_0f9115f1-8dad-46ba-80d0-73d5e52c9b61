/**
 * Basic usage example for the database module
 */
const db = require('../index');

async function main() {
  try {
    // Initialize the database connection
    console.log('Initializing database connection...');
    await db.init();
    
    // Get connection information
    const info = db.getConnectionInfo();
    console.log(`Connected to ${info.environment} environment`);
    
    // Get test cases
    console.log('\nGetting test cases...');
    const testCases = await db.getTestCases({ limit: 5 });
    console.log(`Retrieved ${testCases.length} test cases:`);
    console.log(testCases);
    
    // Get test suites
    console.log('\nGetting test suites...');
    const testSuites = await db.getTestSuites({ limit: 5 });
    console.log(`Retrieved ${testSuites.length} test suites:`);
    console.log(testSuites);
    
    // Get active tests
    console.log('\nGetting active tests...');
    const activeTests = await db.getActiveTests();
    console.log(`Retrieved ${activeTests.length} active tests:`);
    console.log(activeTests);
    
    // Get recent runs
    console.log('\nGetting recent runs...');
    const recentRuns = await db.getRecentRuns({ limit: 5 });
    console.log(`Retrieved ${recentRuns.length} recent runs:`);
    console.log(recentRuns);
    
    // If there are any recent runs, get test results for the first one
    if (recentRuns.length > 0) {
      const tsnId = recentRuns[0].tsn_id;
      console.log(`\nGetting test results for session ${tsnId}...`);
      const testResults = await db.getTestResults(tsnId);
      console.log('Test results:');
      console.log(testResults);
    }
    
    // Use the query builder for a custom query
    console.log('\nUsing query builder for a custom query...');
    const queryBuilder = new db.QueryBuilder();
    queryBuilder.select('test_case', ['tc_id', 'name', 'status']);
    queryBuilder.where('status', '=', 'active');
    queryBuilder.limit(5);
    
    const { sql, params } = queryBuilder.build();
    console.log(`Executing query: ${sql}`);
    const results = await db.query(sql, params);
    console.log(`Retrieved ${results.length} results:`);
    console.log(results);
    
    // Close the connection
    console.log('\nClosing database connection...');
    await db.close();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the example
main();
