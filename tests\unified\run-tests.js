#!/usr/bin/env node

/**
 * Unified Test Runner for SmartTest Application
 * 
 * This script runs the unified test suite and provides:
 * - Test execution with proper configuration
 * - Coverage reporting
 * - Test result summary
 * - Error handling and reporting
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const CONFIG = {
  jestConfig: path.join(__dirname, 'jest.config.js'),
  rootDir: path.join(__dirname, '../../'),
  coverageDir: path.join(__dirname, 'coverage'),
  timeout: 120000 // 2 minutes
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * Print colored output
 */
function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Print test header
 */
function printHeader(title) {
  const border = '='.repeat(60);
  colorLog(border, 'cyan');
  colorLog(`  ${title}`, 'bright');
  colorLog(border, 'cyan');
}

/**
 * Print test summary
 */
function printSummary(results) {
  colorLog('\n📊 Test Summary:', 'bright');
  colorLog(`✅ Passed: ${results.passed || 0}`, 'green');
  colorLog(`❌ Failed: ${results.failed || 0}`, 'red');
  colorLog(`⏭️  Skipped: ${results.skipped || 0}`, 'yellow');
  colorLog(`📁 Total: ${results.total || 0}`, 'blue');
  
  if (results.coverage) {
    colorLog('\n📈 Coverage:', 'bright');
    colorLog(`Lines: ${results.coverage.lines || 0}%`, 'cyan');
    colorLog(`Functions: ${results.coverage.functions || 0}%`, 'cyan');
    colorLog(`Branches: ${results.coverage.branches || 0}%`, 'cyan');
    colorLog(`Statements: ${results.coverage.statements || 0}%`, 'cyan');
  }
}

/**
 * Run Jest with specified configuration
 */
function runJest(args = []) {
  return new Promise((resolve, reject) => {
    const jestArgs = [
      '--config', CONFIG.jestConfig,
      '--rootDir', CONFIG.rootDir,
      '--verbose',
      ...args
    ];

    colorLog(`🚀 Running: jest ${jestArgs.join(' ')}`, 'blue');

    const jest = spawn('npx', ['jest', ...jestArgs], {
      stdio: 'inherit',
      shell: true,
      cwd: CONFIG.rootDir
    });

    jest.on('close', (code) => {
      if (code === 0) {
        colorLog('✅ Tests completed successfully!', 'green');
        resolve({ success: true, code });
      } else {
        colorLog(`❌ Tests failed with exit code: ${code}`, 'red');
        resolve({ success: false, code });
      }
    });

    jest.on('error', (error) => {
      colorLog(`❌ Error running tests: ${error.message}`, 'red');
      reject(error);
    });

    // Set timeout
    setTimeout(() => {
      jest.kill('SIGTERM');
      reject(new Error('Test execution timed out'));
    }, CONFIG.timeout);
  });
}

/**
 * Validate test environment
 */
function validateEnvironment() {
  colorLog('🔍 Validating test environment...', 'yellow');

  // Check if Jest config exists
  if (!fs.existsSync(CONFIG.jestConfig)) {
    throw new Error(`Jest config not found: ${CONFIG.jestConfig}`);
  }

  // Check if test directories exist
  const testDirs = ['unit', 'integration', 'mocks'];
  for (const dir of testDirs) {
    const dirPath = path.join(__dirname, dir);
    if (!fs.existsSync(dirPath)) {
      colorLog(`⚠️  Warning: Test directory not found: ${dir}`, 'yellow');
    }
  }

  // Ensure coverage directory exists
  if (!fs.existsSync(CONFIG.coverageDir)) {
    fs.mkdirSync(CONFIG.coverageDir, { recursive: true });
    colorLog(`📁 Created coverage directory: ${CONFIG.coverageDir}`, 'green');
  }

  colorLog('✅ Environment validation completed', 'green');
}

/**
 * Main test runner function
 */
async function runTests() {
  try {
    printHeader('SmartTest Unified Test Suite');
    
    // Parse command line arguments
    const args = process.argv.slice(2);
    const testType = args[0] || 'all';
    
    // Validate environment
    validateEnvironment();
    
    let jestArgs = [];
    
    // Configure test type
    switch (testType) {
      case 'unit':
        colorLog('🧪 Running Unit Tests...', 'blue');
        jestArgs = ['--selectProjects', 'Unit Tests'];
        break;
        
      case 'integration':
        colorLog('🔗 Running Integration Tests...', 'blue');
        jestArgs = ['--selectProjects', 'Integration Tests'];
        break;
        
      case 'e2e':
        colorLog('🌐 Running E2E Tests...', 'blue');
        jestArgs = ['--selectProjects', 'E2E Tests'];
        break;
        
      case 'coverage':
        colorLog('📊 Running Tests with Coverage...', 'blue');
        jestArgs = ['--coverage'];
        break;
        
      case 'watch':
        colorLog('👀 Running Tests in Watch Mode...', 'blue');
        jestArgs = ['--watch'];
        break;
        
      case 'ci':
        colorLog('🤖 Running Tests in CI Mode...', 'blue');
        jestArgs = ['--ci', '--coverage', '--watchAll=false'];
        break;
        
      default:
        colorLog('🎯 Running All Tests...', 'blue');
        break;
    }
    
    // Add additional arguments
    if (args.length > 1) {
      jestArgs.push(...args.slice(1));
    }
    
    // Run tests
    const result = await runJest(jestArgs);
    
    // Print results
    if (result.success) {
      colorLog('\n🎉 All tests passed!', 'green');
      
      // Check for coverage report
      const coverageFile = path.join(CONFIG.coverageDir, 'coverage-final.json');
      if (fs.existsSync(coverageFile)) {
        colorLog(`📈 Coverage report available at: ${CONFIG.coverageDir}/lcov-report/index.html`, 'cyan');
      }
      
      process.exit(0);
    } else {
      colorLog('\n💥 Some tests failed!', 'red');
      process.exit(result.code || 1);
    }
    
  } catch (error) {
    colorLog(`\n💥 Test execution failed: ${error.message}`, 'red');
    console.error(error);
    process.exit(1);
  }
}

/**
 * Print usage information
 */
function printUsage() {
  colorLog('\n📖 Usage:', 'bright');
  colorLog('  node run-tests.js [test-type] [additional-args]', 'cyan');
  colorLog('\n🎯 Test Types:', 'bright');
  colorLog('  unit        - Run unit tests only', 'cyan');
  colorLog('  integration - Run integration tests only', 'cyan');
  colorLog('  e2e         - Run end-to-end tests only', 'cyan');
  colorLog('  coverage    - Run all tests with coverage', 'cyan');
  colorLog('  watch       - Run tests in watch mode', 'cyan');
  colorLog('  ci          - Run tests in CI mode', 'cyan');
  colorLog('  all         - Run all tests (default)', 'cyan');
  colorLog('\n📝 Examples:', 'bright');
  colorLog('  node run-tests.js unit', 'yellow');
  colorLog('  node run-tests.js coverage', 'yellow');
  colorLog('  node run-tests.js unit --verbose', 'yellow');
}

// Handle help flag
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  printUsage();
  process.exit(0);
}

// Run tests
runTests();
