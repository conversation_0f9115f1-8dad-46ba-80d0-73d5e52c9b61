/**
 * Build script for SmartTest
 * 
 * This script copies the frontend files to the public directory
 */

const fs = require('fs');
const path = require('path');

// Directories to copy
const directories = [
  { src: 'frontend/dashboard', dest: 'frontend/server/public/dashboard' },
  { src: 'frontend/config', dest: 'frontend/server/public/config' },
  { src: 'frontend/reports', dest: 'frontend/server/public/reports' }
];

// Function to copy a file
function copyFile(src, dest) {
  fs.mkdirSync(path.dirname(dest), { recursive: true });
  fs.copyFileSync(src, dest);
  console.log(`Copied ${src} to ${dest}`);
}

// Function to copy a directory recursively
function copyDir(src, dest) {
  // Create destination directory if it doesn't exist
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  // Get all files and directories in the source directory
  const entries = fs.readdirSync(src, { withFileTypes: true });

  // Copy each file and directory
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      // Recursively copy directory
      copyDir(srcPath, destPath);
    } else {
      // Copy file
      copyFile(srcPath, destPath);
    }
  }
}

// Clean the public directory
console.log('Cleaning public directory...');
for (const dir of directories) {
  if (fs.existsSync(dir.dest)) {
    fs.rmSync(dir.dest, { recursive: true, force: true });
    console.log(`Removed ${dir.dest}`);
  }
}

// Copy the frontend files
console.log('Copying frontend files...');
for (const dir of directories) {
  copyDir(dir.src, dir.dest);
  console.log(`Copied ${dir.src} to ${dir.dest}`);
}

console.log('Build complete!');
