/**
 * Test script for SmartTest server
 */

const http = require('http');

// Make a request to the server
http.get('http://localhost:3000', (res) => {
  console.log(`Status code: ${res.statusCode}`);
  console.log(`Headers: ${JSON.stringify(res.headers)}`);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log(`Response length: ${data.length} bytes`);
    console.log('Response received successfully');
  });
}).on('error', (err) => {
  console.error(`Error: ${err.message}`);
});
