/**
 * Database Module
 * Provides a unified interface for database operations with AI support
 */
const config = require('./config');
const ConnectionFactory = require('./connections');
const queries = require('./queries');
const QueryBuilder = require('./utils/query-builder');
const { generateQuery } = require('./ai/query-generator');
const { getDatabaseSchema } = require('./ai/context-provider');

// Module state
let connection = null;
let isInitialized = false;
let currentEnvironment = null;
let debugMode = process.env.DB_DEBUG === 'true';

/**
 * Log debug information if debug mode is enabled
 * @param {string} message - Message to log
 */
function log(message) {
  if (debugMode) {
    console.log(`[DB] ${message}`);
  }
}

/**
 * Initialize the database connection
 * @param {string} environment - Environment name (qa01, qa02, qa03)
 * @param {Object} options - Connection options
 * @returns {Promise<boolean>} - True if initialization was successful
 */
async function init(environment = null, options = {}) {
  // Check if DB_DISABLED is set to true
  if (process.env.DB_DISABLED === 'true') {
    console.log('Database connections are disabled by configuration. Running in proxy-only mode.');
    isInitialized = true;
    currentEnvironment = 'disabled';
    return true;
  }

  // If already initialized, return
  if (isInitialized && connection) {
    log(`Database already initialized for environment: ${currentEnvironment}`);
    return true;
  }

  try {
    log(`Initializing database connection for environment: ${environment || 'auto-detect'}`);

    // Create connection
    connection = ConnectionFactory.createConnection(environment, options);

    // Initialize connection
    await connection.init();

    // Set initialized flag
    isInitialized = true;
    currentEnvironment = environment || config.detectEnvironment() || 'unknown';

    log(`Database connection initialized successfully for environment: ${currentEnvironment}`);
    return true;
  } catch (error) {
    console.error(`Failed to initialize database connection: ${error.message}`);

    // Clean up any partial connections
    if (connection) {
      try {
        await connection.close();
      } catch (closeError) {
        // Ignore errors during cleanup
      }

      connection = null;
    }

    isInitialized = false;
    throw error;
  }
}

/**
 * Execute a raw SQL query
 * @param {string} sql - SQL query
 * @param {Array} params - Query parameters
 * @returns {Promise<Array>} - Query results
 */
async function query(sql, params = []) {
  if (!isInitialized || !connection) {
    log('Database not initialized, attempting to initialize...');
    await init();
  }

  try {
    log(`Executing query: ${sql}`);
    return await connection.query(sql, params);
  } catch (error) {
    log(`Query error: ${error.message}`);
    throw error;
  }
}

/**
 * Execute a query generated from natural language
 * @param {string} description - Natural language query description
 * @returns {Promise<Array>} - Query results
 */
async function executeNaturalLanguageQuery(description) {
  if (!isInitialized) {
    await init();
  }

  try {
    // Get database schema for context
    const dbSchema = getDatabaseSchema();

    // Generate query from natural language
    const { sql, params } = await generateQuery(description, dbSchema);

    // Log the generated query
    log(`[AI] Generated SQL: ${sql}`);
    log(`[AI] Parameters: ${JSON.stringify(params)}`);

    // Execute the query
    return await query(sql, params);
  } catch (error) {
    console.error(`[AI] Error executing natural language query: ${error.message}`);
    throw error;
  }
}

/**
 * Get test cases with optional filtering
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Test cases
 */
async function getTestCases(filters = {}) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testCases.getTestCases(connection, filters);
}

/**
 * Get a test case by ID
 * @param {number|string} tc_id - Test case ID
 * @returns {Promise<Object>} - Test case
 */
async function getTestCaseById(tc_id) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testCases.getTestCaseById(connection, tc_id);
}

/**
 * Search test cases
 * @param {Object} criteria - Search criteria
 * @returns {Promise<Array>} - Test cases
 */
async function searchTestCases(criteria = {}) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testCases.searchTestCases(connection, criteria);
}

/**
 * Get test suites with optional filtering
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Test suites
 */
async function getTestSuites(filters = {}) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testSuites.getTestSuites(connection, filters);
}

/**
 * Get a test suite by ID
 * @param {number|string} ts_id - Test suite ID
 * @returns {Promise<Object>} - Test suite
 */
async function getTestSuiteById(ts_id) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testSuites.getTestSuiteById(connection, ts_id);
}

/**
 * Get test cases in a test suite
 * @param {number|string} ts_id - Test suite ID
 * @returns {Promise<Object>} - Test suite with test cases
 */
async function getTestSuiteInfo(ts_id) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testSuites.getTestSuiteInfo(connection, ts_id);
}

/**
 * Get active test sessions
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Active test sessions
 */
async function getActiveTests(filters = {}) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testSessions.getActiveTests(connection, filters);
}

/**
 * Get recent test sessions
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Recent test sessions
 */
async function getRecentRuns(filters = {}) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testSessions.getRecentRuns(connection, filters);
}

/**
 * Get test session details
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Object>} - Test session details
 */
async function getTestSessionDetails(tsn_id) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testSessions.getTestSessionDetails(connection, tsn_id);
}

/**
 * Get test results for a specific test session
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Object>} - Test results
 */
async function getTestResults(tsn_id) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testResults.getTestResults(connection, tsn_id);
}

/**
 * Get test result summary for a specific test session
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Object>} - Test result summary
 */
async function getTestResultSummary(tsn_id) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testResults.getTestResultSummary(connection, tsn_id);
}

/**
 * Get test case results for a specific test session
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Array>} - Test case results
 */
async function getTestCaseResults(tsn_id) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testResults.getTestCaseResults(connection, tsn_id);
}

/**
 * Get connection information
 * @returns {Object} - Connection information
 */
function getConnectionInfo() {
  return {
    initialized: isInitialized,
    environment: currentEnvironment,
    config: config.getCurrentConfig()
  };
}

/**
 * Close the database connection
 * @returns {Promise<void>}
 */
async function close() {
  if (!isInitialized || !connection) {
    log('Database not initialized, nothing to close');
    return;
  }

  log(`Closing database connection for environment: ${currentEnvironment}...`);

  try {
    await connection.close();
  } catch (error) {
    console.error(`Error closing database connection: ${error.message}`);
  } finally {
    connection = null;
    isInitialized = false;
    log('Database connection closed');
  }
}

module.exports = {
  // Core functions
  init,
  query,
  close,
  getConnectionInfo,

  // Test case functions
  getTestCases,
  getTestCaseById,
  searchTestCases,

  // Test suite functions
  getTestSuites,
  getTestSuiteById,
  getTestSuiteInfo,

  // Test session functions
  getActiveTests,
  getRecentRuns,
  getTestSessionDetails,

  // Test result functions
  getTestResults,
  getTestResultSummary,
  getTestCaseResults,

  // AI-related functions
  executeNaturalLanguageQuery,

  // Export QueryBuilder for advanced usage
  QueryBuilder,

  // Export environment utilities
  setEnvironment: config.setEnvironment,
  detectEnvironment: config.detectEnvironment,
  environments: config.environments
};
