/**
 * SmartTest API Server
 *
 * This server implements the backend API for the SmartTest automation framework,
 * aligning with the existing code architecture while adding test session and
 * input query endpoints with enhanced error handling.
 */

const express = require('express');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const path = require('path');
const db = require('./database'); // Updated to use the refactored database layer

// Load environment variables
require('dotenv').config();

// Get environment-specific config based on NODE_ENV
const envFile = process.env.NODE_ENV ? `.env.${process.env.NODE_ENV}` : '.env';
try {
  require('dotenv').config({ path: envFile });
  console.log(`Loaded environment variables from ${envFile}`);
} catch (err) {
  console.warn(`Warning: Failed to load ${envFile}, using default .env file`);
}

// Import configuration
const { PORT } = require('./config/app-config');

// Import middleware
const requestLogger = require('./middleware/logging');
const errorHandler = require('./middleware/error-handler');

// Import routes
const routes = require('./routes');

// Create Express app
const app = express();

// Middleware to parse URL-encoded bodies (needed for CaseRunner proxy)
app.use(express.urlencoded({ extended: true }));

// Middleware to parse JSON bodies (good practice to have)
app.use(express.json());

// Apply security middleware
app.use(helmet({
  // Relax COEP for development to allow loading external CDN resources
  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: false,
  contentSecurityPolicy: false // Already disabled
}));

// Serve static assets from frontend directory (main project files)
app.use('/frontend', express.static(path.join(__dirname, '..')));

// Also serve from public directory for backward compatibility
app.use(express.static(path.join(__dirname, 'public')));

// Serve application routes with HTML5 history mode support
app.use('/dashboard', express.static(path.join(__dirname, '..', 'dashboard')));
app.use('/config', express.static(path.join(__dirname, '..', 'config')));
app.use('/reports', express.static(path.join(__dirname, '..', 'reports')));

// Serve shared services for direct access
app.use('/shared', express.static(path.join(__dirname, '..', 'shared')));

// Optionally: Redirect root to dashboard for user convenience
app.get('/', (req, res) => {
  res.redirect('/dashboard');
});

// Rate limiting middleware
const apiLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes by default
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs by default
  message: { success: false, message: 'Too many requests from this IP, please try again later' },
  standardHeaders: true,
  legacyHeaders: false
});

// Apply rate limiting to all routes
app.use('/AutoRun/', apiLimiter);

// Enable CORS
app.use(cors());

// Apply logging middleware
app.use(requestLogger);

// Apply routes
app.use('/', routes);

// Homepage with documentation
app.get('/api-docs', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>SmartTest API Server</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
          }
          .container {
            max-width: 1000px;
            margin: 0 auto;
          }
          h1 {
            border-bottom: 2px solid #0078d7;
            padding-bottom: 10px;
            color: #0078d7;
          }
          h2 {
            margin-top: 30px;
            color: #0078d7;
          }
          ul {
            list-style-type: none;
            padding-left: 20px;
          }
          li {
            margin-bottom: 10px;
          }
          code {
            background-color: #f4f4f4;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
          }
          .card {
            background-color: #f9f9f9;
            border-left: 4px solid #0078d7;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
          }
          a {
            color: #0078d7;
            text-decoration: none;
          }
          a:hover {
            text-decoration: underline;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>SmartTest API Server</h1>
          <p>Server is running successfully. You can access the following applications:</p>

          <div class="card">
            <h2>Applications</h2>
            <ul>
              <li><a href="/dashboard"><strong>Dashboard</strong></a> - View test execution status and metrics</li>
              <li><a href="/config/api-explorer.html"><strong>API Explorer</strong></a> - Explore and test API endpoints</li>
              <li><a href="/reports"><strong>Reports</strong></a> - View test reports and analytics</li>
              <li><a href="/bot"><strong>Test Bot</strong></a> - Automated test assistant</li>
            </ul>
          </div>

          <div class="card">
            <h2>API Endpoints</h2>
            <ul>
              <li><code>POST /AutoRun/TestSession</code> - Create a new test session</li>
              <li><code>GET /AutoRun/TestSession</code> - Get all test sessions</li>
              <li><code>GET /AutoRun/TestSession/:id</code> - Get a specific test session</li>
              <li><code>POST /AutoRun/TestSession/:id/status</code> - Update a test session status</li>
              <li><code>GET /AutoRun/TestSession/:id/Report</code> - Get a test session report</li>
              <li><code>POST /AutoRun/InputQuery</code> - Log a new input query</li>
              <li><code>GET /AutoRun/InputQuery/:sessionId</code> - Get input queries for a session</li>
              <li><code>GET /AutoRun/InputQuery/:sessionId/Stats</code> - Get query stats for a session</li>
              <li><code>POST /AutoRun/setup</code> - Set up database schema (admin only)</li>
              <li><code>POST /AutoRun/case-runner</code> - Run a test case or suite</li>
            </ul>
          </div>

          <p>See the <a href="/config/api-explorer.html">API Explorer</a> for interactive documentation and testing.</p>
        </div>
      </body>
    </html>
  `);
});

// Register error handler
app.use(errorHandler);

// Helper function to calculate duration between start and end times
function calculateDuration(startTime, endTime) {
  if (!startTime || !endTime) return 'N/A';

  const start = new Date(startTime);
  const end = new Date(endTime);

  if (isNaN(start.getTime()) || isNaN(end.getTime())) return 'N/A';

  const durationMs = end - start;
  const seconds = Math.floor(durationMs / 1000);

  if (seconds < 60) return `${seconds} seconds`;

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  return `${minutes} min ${remainingSeconds} sec`;
}

// Helper function to calculate pass rate
function calculatePassRate(passed, failed) {
  if (!passed && !failed) return 0;

  const total = (passed || 0) + (failed || 0);
  if (total === 0) return 0;

  return Math.round((passed / total) * 100);
}

// Add authentication middleware
const authenticateSessionApi = (req, res, next) => {
  // Simple authentication for API calls
  // For demo purposes, we're just checking if the uid is provided
  // In a real app, we would validate credentials against a database
  if (req.query.uid) {
    next();
  } else {
    res.status(401).json({ error: 'Authentication required' });
  }
};

// Initialize the server and connect to the database
async function initialize() {
  try {
    console.log('Initializing API server...');

    // Try to initialize the database connection, but continue even if it fails
    try {
      await db.init();
      console.log('Database connection initialized');
    } catch (dbError) {
      console.error('Failed to initialize database connection:', dbError);
      console.warn('Continuing without database connection for testing purposes');
    }

    // Start listening for requests only if not in test mode
    if (process.env.NODE_ENV !== 'test') {
      const port = process.env.PORT || 3000;
      app.listen(port, () => {
        console.log(`Server running on port ${port}`);
      });
    }

    return app;
  } catch (error) {
    console.error('Failed to start server:', error);
    // Only exit the process if not in test environment
    if (process.env.NODE_ENV !== 'test') {
      process.exit(1);
    }
    throw error;
  }
}

// Start the server if not in test mode
if (process.env.NODE_ENV !== 'test') {
  initialize();
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('Received SIGINT signal, shutting down server...');
  await db.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM signal, shutting down server...');
  await db.close();
  process.exit(0);
});

// Note: The recent-runs endpoint has been moved to routes/recent-runs.js
// This improves separation of concerns and makes the codebase more maintainable

// Note: The test-details endpoint has been moved to routes/test-details.js
// This improves separation of concerns and makes the codebase more maintainable

module.exports = app;
