/**
 * Tests for API parameter passing
 *
 * These tests verify that parameters are correctly passed between
 * the frontend, API layer, and database layer using the standardized
 * snake_case naming convention.
 */

const request = require('supertest');

// Mock the case-runner service
jest.mock('../services/case-runner', () => ({
  runTest: jest.fn().mockImplementation(async (params) => {
    return {
      tsn_id: '1001',
      message: params.tc_id ?
        `Test case ${params.tc_id} started successfully with session ID 1001` :
        `Test suite ${params.ts_id} started successfully with session ID 1001`
    };
  })
}));

// Mock the database module
jest.mock('../database', () => ({
  init: jest.fn().mockResolvedValue(),
  close: jest.fn().mockResolvedValue(),
  getTestCases: jest.fn().mockImplementation(async (filters) => {
    // Return mock data based on filters
    return [
      { tc_id: 1, name: 'Test Case 1', status: filters.status || 'active' },
      { tc_id: 2, name: 'Test Case 2', status: filters.status || 'inactive' }
    ];
  }),
  getTestSuites: jest.fn().mockImplementation(async (filters) => {
    // Return mock data based on filters
    return [
      { ts_id: 101, name: 'Test Suite 1', uid: filters.uid || 'user1' },
      { ts_id: 102, name: 'Test Suite 2', uid: filters.uid || 'user2' }
    ];
  }),
  getActiveTests: jest.fn().mockImplementation(async (filters) => {
    // Return mock data based on filters
    return [
      { tsn_id: 1001, tc_id: 1, uid: filters.uid || 'user1', start_ts: '2023-01-01' },
      { tsn_id: 1002, tc_id: 2, uid: filters.uid || 'user2', start_ts: '2023-01-02' }
    ];
  }),
  getTestResults: jest.fn().mockImplementation(async (tsn_id) => {
    // Return mock data based on tsn_id
    return [
      { tsn_id, tc_id: 1, outcome: 'pass' },
      { tsn_id, tc_id: 2, outcome: 'fail' }
    ];
  }),
  getTestSessionDetails: jest.fn().mockImplementation(async (tsn_id) => {
    // Return mock data based on tsn_id
    return {
      tsn_id,
      tc_id: 1,
      uid: 'user1',
      start_ts: '2023-01-01',
      end_ts: '2023-01-02'
    };
  }),
  searchTestCases: jest.fn().mockImplementation(async (criteria) => {
    // Return mock data based on criteria
    return [
      { tc_id: 1, name: criteria.name || 'Test Case 1', status: criteria.status || 'active' },
      { tc_id: 2, name: criteria.name || 'Test Case 2', status: criteria.status || 'inactive' }
    ];
  })
}));

// Mock the authentication middleware
jest.mock('../middleware/auth', () => ({
  validateCredentials: (req, res, next) => {
    // Add user object to request
    req.user = { uid: req.body.uid || req.query.uid || 'test_user' };
    next();
  }
}));

// Load the API after the mocks are set up
const app = require('../api');

describe('API Parameter Passing', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Test test-cases endpoint
  describe('GET /local/test-cases', () => {
    test('should pass ts_id parameter correctly', async () => {
      // Make request with ts_id parameter
      const response = await request(app)
        .get('/local/test-cases')
        .query({ ts_id: 101 });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);

      // Verify database function was called with correct parameter
      const db = require('../database');
      expect(db.getTestCases).toHaveBeenCalledWith(expect.objectContaining({ ts_id: '101' }));
    });

    test('should pass status parameter correctly', async () => {
      // Make request with status parameter
      const response = await request(app)
        .get('/local/test-cases')
        .query({ status: 'active' });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);

      // Verify database function was called with correct parameter
      const db = require('../database');
      expect(db.getTestCases).toHaveBeenCalledWith(expect.objectContaining({ status: 'active' }));
    });

    test('should pass multiple parameters correctly', async () => {
      // Make request with multiple parameters
      const response = await request(app)
        .get('/local/test-cases')
        .query({ ts_id: 101, status: 'active', limit: 10 });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);

      // Verify database function was called with correct parameters
      const db = require('../database');
      expect(db.getTestCases).toHaveBeenCalledWith(expect.objectContaining({
        ts_id: '101',
        status: 'active',
        limit: '10'
      }));
    });
  });

  // Test test-suites endpoint
  describe('GET /local/test-suites', () => {
    test('should pass uid parameter correctly', async () => {
      // Make request with uid parameter
      const response = await request(app)
        .get('/local/test-suites')
        .query({ uid: 'user1' });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);

      // Verify database function was called with correct parameter
      const db = require('../database');
      expect(db.getTestSuites).toHaveBeenCalledWith(expect.objectContaining({ uid: 'user1' }));
    });

    test('should pass name parameter correctly', async () => {
      // Make request with name parameter
      const response = await request(app)
        .get('/local/test-suites')
        .query({ name: 'Test Suite' });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);

      // Verify database function was called with correct parameter
      const db = require('../database');
      expect(db.getTestSuites).toHaveBeenCalledWith(expect.objectContaining({ name: 'Test Suite' }));
    });

    test('should pass multiple parameters correctly', async () => {
      // Make request with multiple parameters
      const response = await request(app)
        .get('/local/test-suites')
        .query({ uid: 'user1', name: 'Test Suite', limit: 10 });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);

      // Verify database function was called with correct parameters
      const db = require('../database');
      expect(db.getTestSuites).toHaveBeenCalledWith(expect.objectContaining({
        uid: 'user1',
        name: 'Test Suite',
        limit: '10'
      }));
    });
  });

  // Test active-tests endpoint
  describe('GET /local/active-tests', () => {
    test('should pass uid parameter correctly', async () => {
      // Make request with uid parameter
      const response = await request(app)
        .get('/local/active-tests')
        .query({ uid: 'user1' });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);

      // Verify database function was called with correct parameter
      const db = require('../database');
      expect(db.getActiveTests).toHaveBeenCalledWith(expect.objectContaining({ uid: 'user1' }));
    });

    test('should pass limit parameter correctly', async () => {
      // Make request with limit parameter
      const response = await request(app)
        .get('/local/active-tests')
        .query({ limit: 5 });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);

      // Verify database function was called with correct parameter
      const db = require('../database');
      expect(db.getActiveTests).toHaveBeenCalledWith(expect.objectContaining({ limit: '5' }));
    });
  });

  // Test test-results endpoint
  describe('GET /api/test-reports/:tsn_id', () => {
    test('should pass tsn_id parameter correctly', async () => {
      // Make request with tsn_id parameter
      const response = await request(app)
        .get('/api/test-reports/1001');

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('reports');

      // Verify database function was called with correct parameter
      const db = require('../database');
      expect(db.getTestResults).toHaveBeenCalledWith('1001');
    });
  });

  // Test case-runner endpoint
  describe('POST /api/case-runner', () => {
    test('should pass tc_id parameter correctly', async () => {
      // Mock the case-runner service
      jest.mock('../services/case-runner', () => ({
        runTest: jest.fn().mockResolvedValue({
          tsn_id: '1001',
          message: 'Test started successfully'
        })
      }));

      // Make request with tc_id parameter
      const response = await request(app)
        .post('/api/case-runner')
        .send({
          uid: 'user1',
          password: 'password',
          tc_id: 1,
          envir: 'qa02'
        });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('tsn_id');

      // Verify service function was called with correct parameters
      const caseRunnerService = require('../services/case-runner');
      expect(caseRunnerService.runTest).toHaveBeenCalledWith(expect.objectContaining({
        tc_id: 1,
        uid: 'user1',
        envir: 'qa02'
      }));
    });

    test('should pass ts_id parameter correctly', async () => {
      // Mock the case-runner service
      jest.mock('../services/case-runner', () => ({
        runTest: jest.fn().mockResolvedValue({
          tsn_id: '1001',
          message: 'Test started successfully'
        })
      }));

      // Make request with ts_id parameter
      const response = await request(app)
        .post('/api/case-runner')
        .send({
          uid: 'user1',
          password: 'password',
          ts_id: 101,
          envir: 'qa02'
        });

      // Verify response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('tsn_id');

      // Verify service function was called with correct parameters
      const caseRunnerService = require('../services/case-runner');
      expect(caseRunnerService.runTest).toHaveBeenCalledWith(expect.objectContaining({
        ts_id: 101,
        uid: 'user1',
        envir: 'qa02'
      }));
    });
  });
});
