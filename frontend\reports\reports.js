// Reports Page JavaScript

// Configuration
const config = {
    // Hybrid approach: Use both database API and direct external API
    reportingEndpoint: '/local/recent-runs', // Database API endpoint for recent runs
    testDetailsEndpoint: '/local/test-details', // Database API endpoint for test details
    refreshInterval: 30000, // 30 seconds - polling interval for external API
    externalApiBaseUrl: '/api', // Use our proxy server instead of direct connection
    maxReportsToShow: 25, // Maximum number of reports to show in the table by default
    autoRefresh: false // Disable auto-refresh by default
};

// Constants
const DEFAULT_LIMIT = 25; // Default number of records to fetch

// DOM Elements
const elements = {
    reportsTable: document.getElementById('reports-table'),
    refreshBtn: document.getElementById('refresh-btn'),
    exportBtn: document.getElementById('export-btn'),
    timeRangeDropdown: document.getElementById('timeRangeDropdown'),
    dropdownItems: document.querySelectorAll('.dropdown-item'),
    environmentDisplay: document.getElementById('environment-display'),
    testDetailsSection: document.getElementById('test-details-section'),
    testDetailsTitle: document.getElementById('test-details-title'),
    closeDetailsBtn: document.getElementById('close-details-btn'),
    // Test details elements
    detailTestId: document.getElementById('detail-test-id'),
    detailTestType: document.getElementById('detail-test-type'),
    detailEnvironment: document.getElementById('detail-environment'),
    detailStartTime: document.getElementById('detail-start-time'),
    detailStatus: document.getElementById('detail-status'),
    detailDuration: document.getElementById('detail-duration'),
    detailUser: document.getElementById('detail-user'),
    detailTrigger: document.getElementById('detail-trigger'),
    detailTotalCases: document.getElementById('detail-total-cases'),
    detailPassedCases: document.getElementById('detail-passed-cases'),
    detailFailedCases: document.getElementById('detail-failed-cases'),
    detailSkippedCases: document.getElementById('detail-skipped-cases'),
    testCasesTable: document.getElementById('test-cases-table'),
    // Chart elements
    successRateChart: document.getElementById('success-rate-chart'),
    durationTrendChart: document.getElementById('duration-trend-chart'),
    // Rerun failed tests button
    rerunFailedBtn: document.getElementById('rerun-failed-btn'),
    loadingIndicator: document.getElementById('loading-indicator'),
    refreshStatus: document.getElementById('refresh-status'),
    resetButton: document.getElementById('reset-button'),
    // New loading indicator elements
    tableLoadingOverlay: document.getElementById('table-loading-overlay') || createTableLoadingOverlay(),
    progressBar: document.getElementById('loading-progress-bar') || createProgressBar()
};

// Charts
let successRateChart;
let durationTrendChart;

// Current state
let currentState = {
    activeTimeRange: '30d', // Default active time range (e.g., '24h', '7d', { type: 'custom', start: '...', end: '...' })
    customStartDate: null,    // For custom date range picker
    customEndDate: null,      // For custom date range picker
    reports: [],              // Holds ALL reports for the activeTimeRange
    totalRecordsForActiveTimeRange: 0, // Total records in DB for activeTimeRange
    lastSeenTsnId: null,      // Highest tsn_id from the last fetch, for incremental updates
    currentTestDetails: null, // Stores details of the currently viewed test
    // highestSessionId: localStorage.getItem('highestSessionId') || 0 // Review if needed, replaced by lastSeenTsnId for this flow
};

// Create table loading overlay element
function createTableLoadingOverlay() {
    // First check if it already exists
    let overlay = document.getElementById('table-loading-overlay');
    if (overlay) {
        return overlay;
    }

    overlay = document.createElement('div');
    overlay.id = 'table-loading-overlay';
    overlay.className = 'position-absolute w-100 h-100';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
    overlay.style.zIndex = '1000';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';
    overlay.style.transition = 'opacity 0.3s ease-in-out';

    const spinnerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 loading-text fw-bold">Loading data...</p>
        </div>
    `;
    overlay.innerHTML = spinnerHTML;

    // Find the table container and add the overlay
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer) {
        tableContainer.style.position = 'relative';
        tableContainer.appendChild(overlay);
        console.log('Added table loading overlay to table-responsive container');
    } else {
        // Fallback to adding it near the table itself
        const table = document.getElementById('reports-table');
        if (table && table.parentNode) {
            table.parentNode.style.position = 'relative';
            table.parentNode.appendChild(overlay);
            console.log('Added table loading overlay to table parent');
        } else {
            console.warn('Could not find a suitable container for the loading overlay');
        }
    }

    // Initially hide it (but it exists in the DOM now)
    overlay.style.display = 'none';

    return overlay;
}

// Create progress bar element
function createProgressBar() {
    // First check if it already exists
    let progressBar = document.getElementById('loading-progress-bar');
    if (progressBar) {
        return progressBar;
    }

    progressBar = document.createElement('div');
    progressBar.id = 'loading-progress-bar';
    progressBar.style.height = '4px';
    progressBar.style.width = '0%';
    progressBar.style.backgroundColor = '#007bff';
    progressBar.style.position = 'absolute';
    progressBar.style.top = '0';
    progressBar.style.left = '0';
    progressBar.style.zIndex = '1001';
    progressBar.style.transition = 'width 0.3s ease-in-out';

    // Find the table container and add the progress bar
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer) {
        tableContainer.style.position = 'relative';
        tableContainer.appendChild(progressBar);
        console.log('Added progress bar to table-responsive container');
    } else {
        // Fallback to adding it to the card that contains the table
        const tableCard = document.querySelector('.card');
        if (tableCard) {
            tableCard.style.position = 'relative';
            tableCard.appendChild(progressBar);
            console.log('Added progress bar to card container');
        } else {
            console.warn('Could not find a suitable container for the progress bar');
        }
    }

    return progressBar;
}

// Show loading indicators
function showLoadingIndicators(isIncrementalRefresh = false) {
    console.log('Showing loading indicators');

    // Always show the main loading indicator (the existing one from HTML)
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'block';
        console.log('Showing main loading indicator');
    }

    // For the table overlay
    if (elements.tableLoadingOverlay) {
        // Make sure the overlay exists in the DOM
        elements.tableLoadingOverlay = createTableLoadingOverlay();

        // Show it with appropriate opacity based on refresh type
        if (isIncrementalRefresh) {
            elements.tableLoadingOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
        } else {
            elements.tableLoadingOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
        }

        elements.tableLoadingOverlay.style.display = 'flex';
        console.log('Showing table loading overlay');
    }

    // Start the progress bar animation
    if (elements.progressBar) {
        // Make sure the progress bar exists in the DOM
        elements.progressBar = createProgressBar();

        elements.progressBar.style.display = 'block';
        elements.progressBar.style.width = '0%';

        // Simulate progress (goes to 90% then completes when data is loaded)
        setTimeout(() => { elements.progressBar.style.width = '40%'; }, 200);
        setTimeout(() => { elements.progressBar.style.width = '70%'; }, 600);
        setTimeout(() => { elements.progressBar.style.width = '90%'; }, 1200);
        console.log('Animating progress bar');
    }

    // Apply pulse animation to loading cell if it exists
    const loadingCell = document.querySelector('td.text-center');
    if (loadingCell) {
        // Add a pulsing animation
        loadingCell.innerHTML = '<div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div> Loading data...';

        // Add animation if not already applied
        if (!document.getElementById('loading-animations-style')) {
            const style = document.createElement('style');
            style.id = 'loading-animations-style';
            style.textContent = `
                @keyframes pulse {
                    0% { opacity: 0.6; }
                    50% { opacity: 1; }
                    100% { opacity: 0.6; }
                }
                .loading-pulse {
                    animation: pulse 1.5s infinite ease-in-out;
                }
            `;
            document.head.appendChild(style);
        }

        loadingCell.classList.add('loading-pulse');
        console.log('Enhanced loading cell with animation');
    }
}

// Hide loading indicators
function hideLoadingIndicators() {
    console.log('Hiding loading indicators');

    // Hide the main loading indicator
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }

    // Hide the table overlay
    if (elements.tableLoadingOverlay) {
        // Complete with fade out effect
        elements.tableLoadingOverlay.style.opacity = '0';
        setTimeout(() => {
            elements.tableLoadingOverlay.style.display = 'none';
        }, 300);
    }

    // Complete the progress bar and then hide it
    if (elements.progressBar) {
        elements.progressBar.style.width = '100%';
        setTimeout(() => {
            elements.progressBar.style.display = 'none';
        }, 500);
    }

    // Remove animation from loading cell
    const loadingCell = document.querySelector('td.text-center.loading-pulse');
    if (loadingCell) {
        loadingCell.classList.remove('loading-pulse');
    }
}

// Load reports data from the server
function loadReportsData(options = {}) {
    const {
        isIncrementalRefresh = false,
        forceFullReload = false,
        showLoading = true
    } = options;

    let timeRangeToSend = currentState.activeTimeRange;
    if (options.timeRange) { // If a specific timeRange is passed (e.g., from event listener)
        timeRangeToSend = options.timeRange;
        currentState.activeTimeRange = options.timeRange; // Update activeTimeRange
    }

    if (showLoading) {
        // Use our enhanced loading indicators
        showLoadingIndicators(isIncrementalRefresh);
    }

    const apiParams = {
        time_range: typeof timeRangeToSend === 'object' ? JSON.stringify(timeRangeToSend) : timeRangeToSend
    };

    if (isIncrementalRefresh && currentState.lastSeenTsnId) {
        apiParams.since_tsn_id = currentState.lastSeenTsnId;
    } else if (forceFullReload || !isIncrementalRefresh) {
        // For full reload, reset lastSeenTsnId to ensure all data for the range is fetched
        currentState.lastSeenTsnId = null;
    }
    // Note: We are NOT sending a client-side pagination `limit` here for full range fetches.
    // The backend will return all records for the given time_range.

    console.log(`Loading reports with params:`, apiParams);

    if (window.apiService) {
        if (!window.apiService.credentials.uid) {
            window.apiService.loadCredentials();
        }

        window.apiService.getTestReports(apiParams)
            .then(apiResponse => {
                // console.log('API Response:', apiResponse); // Commented out to prevent excessive logging
                let reportsToProcess = null;
                let success = false;
                let totalRecords = 0;
                let highestTsnId = null;
                let rawErrorMessage = null;

                if (apiResponse && apiResponse.success === true) { // Standard object response {success: true, reports: [], ...}
                    success = true;
                    reportsToProcess = apiResponse.reports || apiResponse.newRuns || [];
                    totalRecords = apiResponse.totalRecords || reportsToProcess.length;
                    highestTsnId = apiResponse.highestTsnIdInResponse || apiResponse.latestTsnIdInDelta;
                } else if (Array.isArray(apiResponse)) { // Direct array response [...]
                    success = true;
                    reportsToProcess = apiResponse;
                    totalRecords = apiResponse.length;
                    // highestTsnId might not be available with a direct array response, defaults to null
                } else { // Error or unexpected format
                    if (apiResponse && apiResponse.message) { // e.g. {success: false, message: "..."}
                        rawErrorMessage = apiResponse.message;
                    } else {
                        rawErrorMessage = 'Failed to load reports. Bad or unexpected API response format.';
                        console.error('Error or unsuccessful/malformed response from API:', apiResponse);
                    }
                }

                if (!success) {
                    displayError(rawErrorMessage || 'Failed to load reports due to an unspecified error.');
                    currentState.reports = []; // Clear reports on error
                    currentState.totalRecordsForActiveTimeRange = 0;
                    currentState.lastSeenTsnId = null;
                    displayReports([]); // Update table to show error or no data
                    updateCounters();
                    return;
                }

                // If we reach here, success is true and reportsToProcess is set
                if (isIncrementalRefresh) {
                    // Filter out duplicates based on tsn_id before merging
                    const existingTsnIds = new Set(currentState.reports.map(r => r.tsn_id));
                    const newUniqueReports = reportsToProcess.filter(r => !existingTsnIds.has(r.tsn_id));

                    currentState.reports = [...newUniqueReports, ...currentState.reports];
                    // Sort by start_time descending after merge if necessary, or rely on API order for new runs
                    currentState.reports.sort((a, b) => new Date(b.start_time) - new Date(a.start_time));

                    console.log(`Incremental refresh: Added ${newUniqueReports.length} new reports. Total now: ${currentState.reports.length}`);
                } else {
                    currentState.reports = reportsToProcess;
                }

                currentState.totalRecordsForActiveTimeRange = totalRecords; // This might be an estimate if API doesn't provide it for direct array
                if (highestTsnId) { // Only update if we got one from an object response
                    currentState.lastSeenTsnId = highestTsnId;
                }

                // Display the processed reports and update counters
                displayReports(currentState.reports);
                updateCounters();
                // Hide loading indicators before updating the UI
                hideLoadingIndicators();

                // Apply fade-in effect to the new data
                const tableBody = elements.reportsTable.querySelector('tbody');
                if (tableBody) {
                    tableBody.style.opacity = '0';
                    setTimeout(() => {
                        tableBody.style.transition = 'opacity 0.3s ease-in-out';
                        tableBody.style.opacity = '1';
                    }, 50);
                }

                updateCharts(currentState.reports); // Assuming updateCharts uses currentState.reports
                updateRefreshStatus();
            })
            .catch(error => {
                // Hide loading indicators even on error
                hideLoadingIndicators();

                console.error('Error loading reports:', error);
                displayError('Failed to load reports. Please try again later.');
                currentState.reports = [];
                currentState.totalRecordsForActiveTimeRange = 0;
                currentState.lastSeenTsnId = null;
                displayReports([]);
                updateCounters();
            })
            .finally(() => {
                if (showLoading) {
                    hideLoadingIndicators();
                }
                updateRefreshStatus();
            });
    } else {
        // Check for API service with more resilient approach
        if (!window.apiService) {
            console.warn('API Service not available yet, attempting to initialize connection...');
            // Wait briefly and retry once to see if API service becomes available
            setTimeout(() => {
                if (window.apiService) {
                    console.log('API Service now available, proceeding with data load');
                    loadReportsData(options);
                } else {
                    console.error('API Service not available after retry');
                    displayError('Failed to load reports data. API service not available. Please refresh the page.');
                    hideLoadingIndicators();
                }
            }, 500);
            return;
        }
    }
}

function refreshReports() {
    console.log('Refreshing reports (incremental)...');
    loadReportsData({
        isIncrementalRefresh: true,
        showLoading: true
    });
}

// Get the currently selected time range
function getSelectedTimeRange() {
    return currentState.activeTimeRange || '30d'; // Fallback to 30d if not set
}

// Get the currently selected timezone
function getSelectedTimezone() {
    return localStorage.getItem('selected_timezone') || 'local';
}

// Save the selected timezone to localStorage
function saveSelectedTimezone(timezone) {
    localStorage.setItem('selected_timezone', timezone);
}

// Initialize charts
function initializeCharts() {
    console.log('Initializing charts...');

    // Get the chart canvases
    const successRateCanvas = elements.successRateChart;
    const durationTrendCanvas = elements.durationTrendChart;

    if (!successRateCanvas || !durationTrendCanvas) {
        console.error('Chart canvases not found');
        return;
    }

    // Destroy existing charts if they exist to prevent errors
    if (successRateChart) {
        successRateChart.destroy();
        successRateChart = null;
    }

    if (durationTrendChart) {
        durationTrendChart.destroy();
        durationTrendChart = null;
    }

    // Create success rate chart if the element exists
    if (elements.successRateChart) {
        successRateChart = new Chart(elements.successRateChart.getContext('2d'), {
            type: 'doughnut',
            data: {
                labels: ['Passed', 'Failed', 'Skipped'],
                datasets: [{
                    data: [0, 0, 0], // Initial data will be updated later
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom'
                }
            }
        });
    }

    // Create duration trend chart if the element exists
    if (elements.durationTrendChart) {
        durationTrendChart = new Chart(elements.durationTrendChart.getContext('2d'), {
            type: 'line',
            data: {
                labels: [], // Will be populated with timestamps
                datasets: [{
                    label: 'Duration (seconds)',
                    data: [], // Will be populated with durations
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    display: true,
                    position: 'top'
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Duration (seconds)'
                        },
                        beginAtZero: true
                    }
                }
            }
        });
    }

    console.log('Charts initialized');
}

// Function to update charts with new data
function updateCharts(reports) {
    if (!reports || !Array.isArray(reports)) {
        console.warn('No valid reports data to update charts');
        return;
    }

    // Update success rate chart
    if (successRateChart) {
        const passedCount = reports.filter(r => r.status?.toLowerCase() === 'passed').length;
        const failedCount = reports.filter(r => r.status?.toLowerCase() === 'failed').length;
        const skippedCount = reports.filter(r => r.status?.toLowerCase() === 'skipped').length;

        successRateChart.data.datasets[0].data = [passedCount, failedCount, skippedCount];
        successRateChart.update();
    }

    // Update duration trend chart
    if (durationTrendChart) {
        // Get the 10 most recent reports with duration data
        const recentReports = reports
            .filter(r => r.start_time && r.end_time)
            .sort((a, b) => new Date(b.start_time) - new Date(a.start_time))
            .slice(0, 10);

        // Reverse to show oldest to newest (left to right)
        recentReports.reverse();

        const labels = recentReports.map(r => {
            const date = new Date(r.start_time);
            return date.toLocaleTimeString();
        });

        const durations = recentReports.map(r => {
            const start = new Date(r.start_time);
            const end = new Date(r.end_time);
            return (end - start) / 1000; // Duration in seconds
        });

        durationTrendChart.data.labels = labels;
        durationTrendChart.data.datasets[0].data = durations;
        durationTrendChart.update();
    }

    console.log('Charts updated with new data');
}

// Format a user email for display
// @param {string} email - User email address
// @returns {string} - Formatted user name
function formatUserEmail(email) {
    if (!email) return 'Unknown';

    // Extract name from email address
    try {
        // Handle common email formats
        if (email.includes('@')) {
            // Extract the part before the @ symbol
            const namePart = email.split('@')[0];

            // Replace dots and underscores with spaces
            const nameWithSpaces = namePart.replace(/[._]/g, ' ');

            // Capitalize each word
            return nameWithSpaces.split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');
        }
        return email; // Return as is if not an email
    } catch (error) {
        console.warn('Error formatting email:', error);
        return email; // Return original on error
    }
}

// Update the refresh status with the current time
function updateRefreshStatus() {
    const refreshStatus = document.getElementById('refresh-status');
    if (!refreshStatus) return;

    const now = new Date();

    // Store the original timestamp as ISO string in a data attribute for timezone conversions
    refreshStatus.dataset.originalTime = now.toISOString();

    const selectedTimezone = getSelectedTimezone();

    try {
        // Format the time based on the selected timezone
        let timeOptions = {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        };

        if (selectedTimezone !== 'local') {
            timeOptions.timeZone = selectedTimezone;
        }

        refreshStatus.textContent = `Last updated: ${now.toLocaleTimeString('en-US', timeOptions)}`;
        console.log(`Updated refresh status with timezone ${selectedTimezone}`);
    } catch (error) {
        console.error('Error updating refresh status:', error);
        refreshStatus.textContent = `Last updated: ${now.toLocaleTimeString()}`;
    }
}

// Helper function to create a user cell with email tooltip
function createUserCell(email, displayName) {
    const cell = document.createElement('td');
    cell.textContent = displayName || 'Unknown';
    if (email) {
        cell.setAttribute('title', email);
    }
    return cell;
}

// Format a date/time string into a readable format
function formatDateTime(dateTimeString) {
    if (!dateTimeString || dateTimeString === 'NULL' || dateTimeString === 'null' || dateTimeString.includes('@')) {
        return '-';
    }

    try {
        // Try standard date parsing first
        let dateObj = new Date(dateTimeString);

        // If date is invalid, try manual parsing for MySQL format "YYYY-MM-DD HH:MM:SS"
        if (isNaN(dateObj.getTime()) && typeof dateTimeString === 'string') {
            const match = dateTimeString.match(/(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/);
            if (match) {
                // Parse date components (month is 0-indexed in JavaScript Date)
                dateObj = new Date(
                    parseInt(match[1]), // year
                    parseInt(match[2]) - 1, // month (0-indexed)
                    parseInt(match[3]), // day
                    parseInt(match[4]), // hour
                    parseInt(match[5]), // minute
                    parseInt(match[6])  // second
                );
            }
        }

        // Check if date is valid after all parsing attempts
        if (isNaN(dateObj.getTime())) {
            console.warn('Invalid date after parsing attempts:', dateTimeString);
            return dateTimeString; // Return original string if we can't parse it
        }

        // Get the selected timezone
        const selectedTimezone = getSelectedTimezone();

        // Format options
        const options = {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        };

        // Add timeZone option if not local
        if (selectedTimezone !== 'local') {
            options.timeZone = selectedTimezone;
        }

        // Format the date according to the selected timezone
        return dateObj.toLocaleString('en-US', options);
    } catch (error) {
        console.error('Error formatting date:', error, dateTimeString);
        return dateTimeString; // Return original string on error
    }
}

// Calculate the duration between two timestamps
function calculateDuration(startTime, endTime) {
    if (!startTime || !endTime) return 'N/A';

    try {
        // Create date objects from the strings
        const startDate = new Date(startTime);
        const endDate = new Date(endTime);

        // Check if these are valid dates
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            return 'Invalid date';
        }

        // Calculate the difference in milliseconds
        const diffMs = endDate - startDate;

        // Convert to seconds, minutes, hours
        const seconds = Math.floor((diffMs / 1000) % 60);
        const minutes = Math.floor((diffMs / (1000 * 60)) % 60);
        const hours = Math.floor(diffMs / (1000 * 60 * 60));

        // Format the duration
        if (hours > 0) {
            return `${hours}h ${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        } else {
            return `${seconds}s`;
        }
    } catch (error) {
        console.error(`Error calculating duration: ${error}`);
        return 'Error';
    }
}

// Refresh all timestamps with the current timezone
function refreshAllTimestamps() {
    console.log('Refreshing all timestamps with timezone:', getSelectedTimezone());

    // Refresh timestamps in the table
    const startTimeColumns = document.querySelectorAll('#reports-table tbody td:nth-child(5)');
    const endTimeColumns = document.querySelectorAll('#reports-table tbody td:nth-child(6)');

    startTimeColumns.forEach(cell => {
        if (cell.dataset.originalTime) {
            cell.textContent = formatDateTime(cell.dataset.originalTime);
        }
    });

    endTimeColumns.forEach(cell => {
        if (cell.dataset.originalTime) {
            cell.textContent = formatDateTime(cell.dataset.originalTime);
        }
    });

    // Refresh the last updated timestamp
    const refreshStatus = document.getElementById('refresh-status');
    if (refreshStatus && refreshStatus.dataset.originalTime) {
        const now = new Date(refreshStatus.dataset.originalTime);
        const selectedTimezone = getSelectedTimezone();
        const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };

        if (selectedTimezone !== 'local') {
            timeOptions.timeZone = selectedTimezone;
        }

        refreshStatus.textContent = `Last updated: ${now.toLocaleTimeString('en-US', timeOptions)}`;
    }
}

// Setup event listeners for the reports page
function setupEventListeners() {
    console.log('Setting up event listeners for reports page');

    // Time range dropdown event listeners
    const timeRangeDropdownItems = document.querySelectorAll('#timeRangeDropdown + .dropdown-menu .dropdown-item');
    const timeRangeDropdownButton = document.getElementById('timeRangeDropdown');
    const customRangeControls = document.getElementById('custom-range-controls');
    const customStartDateInput = document.getElementById('custom-start-date');
    const customEndDateInput = document.getElementById('custom-end-date');
    const applyCustomRangeBtn = document.getElementById('apply-custom-range');

    if (timeRangeDropdownItems) {
        timeRangeDropdownItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const range = this.getAttribute('data-range');
                if (!range) return;

                // Show an immediate visual feedback that something is happening
                elements.timeRangeDropdown.innerHTML = `${this.textContent} <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>`;

                // Clear existing data with fade-out effect before loading new data
                const tableBody = elements.reportsTable.querySelector('tbody');
                if (tableBody) {
                    tableBody.style.transition = 'opacity 0.2s ease-in-out';
                    tableBody.style.opacity = '0.5';
                }

                // Get full reload for new time range
                loadReportsData({
                    timeRange: range,
                    forceFullReload: true,
                    showLoading: true
                });

                // Update dropdown button text properly after loading is complete
                setTimeout(() => {
                    elements.timeRangeDropdown.textContent = this.textContent;
                }, 2000);
            });
        });
    }

    if (applyCustomRangeBtn && customStartDateInput && customEndDateInput) {
        applyCustomRangeBtn.addEventListener('click', function() {
            const startDate = customStartDateInput.value;
            const endDate = customEndDateInput.value;

            if (startDate && endDate) {
                if (new Date(startDate) > new Date(endDate)) {
                    alert('Start date cannot be after end date.');
                    return;
                }
                currentState.activeTimeRange = { type: 'custom', start: startDate, end: endDate };
                console.log('Custom time range applied:', currentState.activeTimeRange);
                if (timeRangeDropdownButton) {
                    timeRangeDropdownButton.textContent = `${startDate} to ${endDate}`;
                }
                loadReportsData({ forceFullReload: true, showLoading: true });
                if (customRangeControls) customRangeControls.style.display = 'none'; // Optionally hide after apply
            } else {
                alert('Please select both start and end dates for custom range.');
            }
        });
    }

    // Refresh button event listener
    if (elements.refreshBtn) {
        elements.refreshBtn.addEventListener('click', refreshReports);
    }

    // Export button event listener
    if (elements.exportBtn) {
        elements.exportBtn.addEventListener('click', function() {
            // TODO: Implement export functionality
            alert('Export functionality not yet implemented.');
        });
    }

    // Reset button event listener
    if (elements.resetButton) {
        elements.resetButton.addEventListener('click', function() {
            // Reset filters and reload data
            currentState.timeRange = '24h';

            // Update dropdown button text
            const dropdownButton = document.getElementById('timeRangeDropdown');
            if (dropdownButton) {
                dropdownButton.textContent = 'Last 24 Hours';
            }

            // Reload reports data
            loadReportsData();
        });
    }

    // Close details button event listener
    if (elements.closeDetailsBtn) {
        elements.closeDetailsBtn.addEventListener('click', function() {
            if (elements.testDetailsSection) {
                elements.testDetailsSection.classList.add('d-none');
            }
        });
    }

    // Timezone selector event listener
    const timezoneSelector = document.getElementById('timezone-selector');
    if (timezoneSelector) {
        timezoneSelector.addEventListener('change', function() {
            const selectedTimezone = this.value;
            saveSelectedTimezone(selectedTimezone);
            refreshAllTimestamps();
        });
    }

    console.log('Event listeners set up successfully');
}

// Display error message to the user
function displayError(message) {
    console.error(message);
    const tableBody = document.querySelector('#reports-table tbody');
    if (tableBody) {
        tableBody.innerHTML = `<tr><td colspan="11" class="text-center text-danger">${message}</td></tr>`;
    }

    const refreshStatus = document.getElementById('refresh-status');
    if (refreshStatus) {
        refreshStatus.textContent = `Error: ${message}`;
        refreshStatus.classList.add('text-danger');
    }
}

// Initialize filter controls
function initializeFilters() {
    console.log('Initializing filter controls...');

    // Reset filters button
    const resetFiltersBtn = document.getElementById('reset-filters');
    if (resetFiltersBtn) {
        resetFiltersBtn.addEventListener('click', function() {
            // Reset all filter inputs
            const userFilter = document.getElementById('filter-user');
            const statusFilter = document.getElementById('filter-status');
            const testIdFilter = document.getElementById('filter-test-id');

            if (userFilter) userFilter.value = '';
            if (statusFilter) statusFilter.value = '';
            if (testIdFilter) testIdFilter.value = '';

            // If DataTable is initialized, reset its filters too
            if (reportsDataTable) {
                reportsDataTable.search('').columns().search('').draw();
            }

            console.log('Filters have been reset');
        });
    }

    console.log('Filter controls initialized');
}

// Initialize application on document ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Reports page initialization started');

    // DIAGNOSTIC: Check service availability on page load
    console.log('🔍 [INIT] Service availability check:');
    console.log('- window.externalApiService:', !!window.externalApiService, window.externalApiService);
    console.log('- window.enhancedExternalApiService:', !!window.enhancedExternalApiService, window.enhancedExternalApiService);
    console.log('- window.apiService:', !!window.apiService, window.apiService);

    // DIAGNOSTIC: Add cookie debugging function
    window.debugCookieAuth = function() {
        console.log('🍪 Cookie Authentication Debug:');
        console.log('- Document cookies:', document.cookie);
        console.log('- External API session ID:', window.externalApiService?.jsessionId);
        console.log('- Enhanced External API session ID:', window.enhancedExternalApiService?.jsessionId);
        console.log('- Session valid (external):', window.externalApiService?.isSessionValid());
        console.log('- Session valid (enhanced):', window.enhancedExternalApiService?.isSessionValid());

        // Test a simple external API call
        if (window.enhancedExternalApiService || window.externalApiService) {
            const service = window.enhancedExternalApiService || window.externalApiService;
            const credentials = getCredentials();

            console.log('🧪 Testing external API connectivity...');
            fetch('/api/external/ReportSummary?tsn_id=14847', {
                credentials: 'include',
                headers: { 'Cache-Control': 'no-cache' }
            }).then(response => {
                console.log('🧪 Test API call result:', response.status, response.statusText);
                if (response.status === 401) {
                    console.log('❌ 401 Unauthorized - Cookie authentication is not working');
                } else if (response.ok) {
                    console.log('✅ API call successful - Cookie authentication is working');
                } else {
                    console.log('⚠️ API call returned:', response.status);
                }
            }).catch(error => {
                console.error('❌ Test API call failed:', error);
            });
        }
    };

    console.log('💡 Use debugCookieAuth() in console to debug cookie authentication issues');

    // Set up refresh button event listener
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshReports);
    }

    // Initialize timezone selector
    const timezoneSelector = document.getElementById('timezone-select');
    if (timezoneSelector) {
        const savedTimezone = localStorage.getItem('selected_timezone');
        if (savedTimezone) {
            timezoneSelector.value = savedTimezone;
        }
        timezoneSelector.addEventListener('change', function() {
            saveSelectedTimezone(this.value);
            refreshAllTimestamps();
        });
        console.log('Timezone selector initialized with timezone:', getSelectedTimezone());
    } else {
        console.warn('Timezone selector not found');
    }

    // Initialize filters (including time range event listeners setup by setupEventListeners)
    initializeFilters(); // This should be called before setupEventListeners if it also sets up parts of filters
    setupEventListeners(); // Sets up timeRangeDropdown listeners etc.

    // Initialize charts
    initializeCharts();

    // Initial refresh of timestamps to ensure they're formatted with the correct timezone
    // This should be called AFTER data is potentially loaded if it formats data in the table
    // refreshAllTimestamps();

    // Load initial data for the default time range (e.g., currentState.activeTimeRange which is '30d')
    // DataTable will be initialized after data is loaded and displayReports is called.
    loadReportsData({ forceFullReload: true, showLoading: true });

    // Call refreshAllTimestamps after the initial data load and table population might be better.
    // Or ensure formatDateTime is robust enough to be called on empty/loading table.
});

/**
 * Get credentials for API calls
 * @returns {Object} Credentials object with uid and password
 */
function getCredentials() {
    // Default test credentials
    let credentials = { uid: '<EMAIL>', password: 'test' };

    // Try to get from window.apiService
    if (window.apiService && window.apiService.credentials) {
        credentials = window.apiService.credentials;
        console.log('Using credentials from API service');
    }
    // Try session storage as fallback
    else {
        const sessionUid = sessionStorage.getItem('smarttest_uid');
        const sessionPwd = sessionStorage.getItem('smarttest_pwd');

        if (sessionUid && sessionPwd) {
            credentials = { uid: sessionUid, password: sessionPwd };
            console.log('Using credentials from session storage');
        } else {
            console.log('Using default test credentials');
        }
    }

    return credentials;
}

/**
 * Load reports data from the external API directly
 * @param {Object} credentials - User credentials
 */
async function loadReportsFromExternalApi(credentials) {
    try {
        // Use the unified external API service from shared services
        const externalApiService = window.enhancedExternalApiService;

        if (!externalApiService) {
            console.warn('Enhanced External API Service not available');
            currentState.reports = [];
            return;
        }

        // Get recent session IDs using the enhanced external API service
        const sessionIds = await externalApiService.getRecentSessionIds(credentials, config.maxReportsToShow);

        if (!sessionIds || sessionIds.length === 0) {
            console.warn('No session IDs found');
            currentState.reports = [];
            return;
        }

        console.log(`Found ${sessionIds.length} session IDs, fetching report data...`);

        // Get report data for each session ID using the enhanced external API service
        const reports = await externalApiService.getRecentTestRuns(
            sessionIds,
            credentials.uid,
            credentials.password,
            config.maxReportsToShow
        );

        // Transform the reports to match the expected format
        currentState.reports = reports.map(report => ({
            id: report.tsn_id,
            tsn_id: report.tsn_id, // Ensure tsn_id is available for view details
            test_id: report.test_id,
            test_name: report.test_name || '',
            type: report.type || 'Unknown',
            environment: report.environment || 'Unknown',
            status: report.status || 'Unknown',
            startTime: report.start_time,
            endTime: report.end_time,
            duration: report.duration || null,
            user: credentials.uid || 'System',
            trigger: 'Manual', // External API doesn't provide trigger info
            totalCases: report.total_cases || 0,
            passedCases: report.passed_cases || 0,
            failedCases: report.failed_cases || 0,
            skippedCases: 0, // External API doesn't provide skipped cases
            passRate: report.pass_rate || 0
        }));

        console.log(`Loaded ${currentState.reports.length} reports from external API`);
    } catch (error) {
        console.error('Error loading reports from external API:', error);
        throw error;
    }
}

/**
 * Load reports data from the database API
 * @param {Object} credentials - User credentials
 */
async function loadReportsFromDatabaseApi(credentials) {
    try {
        const uid = credentials.uid;
        const password = credentials.password;

        console.log(`🔍 Reports: Loading data for user: ${uid}`);

        // Get the highest session ID from localStorage for incremental updates
        const highestId = parseInt(localStorage.getItem('highestSessionId')) || 0;

        // Add parameters to the URL following naming conventions from parameter-mapping.md
        let url = `/local/recent-runs?uid=${encodeURIComponent(uid)}&password=${encodeURIComponent(password)}`;

        console.log(`🔍 Reports: Making request to ${url}`);
        document.getElementById('loading-indicator').style.display = 'block';

        // Make the request
        const response = await fetch(url);
        console.log(`🔍 Reports: Response status: ${response.status} ${response.statusText}`);

        if (!response.ok) {
            console.error(`API error status: ${response.status}`);
            document.getElementById('loading-indicator').style.display = 'none';
            return;
        }

        // Parse the response according to the standard format from frontend-api-database-integration.md
        const responseData = await response.json();

        // Log detailed diagnostic information
        console.log(`📊 Reports: Response structure:`, {
            success: responseData.success,
            messageType: responseData.message ? typeof responseData.message : 'undefined',
            dataType: responseData.data ? (Array.isArray(responseData.data) ? 'array' : typeof responseData.data) : 'undefined',
            dataLength: Array.isArray(responseData.data) ? responseData.data.length : 'N/A'
        });

        // Log a sample of the data received (first item only)
        if (responseData.data && responseData.data.length > 0) {
            console.log(`📊 Reports: Sample data received (first item):`, JSON.stringify(responseData.data[0], null, 2));
        }

        // Check if the response is in the correct format (success/data/message)
        if (responseData && responseData.success === true && Array.isArray(responseData.data)) {
            console.log(`🔍 Reports: Retrieved ${responseData.data.length} sessions`);

            // If we got new data, store the highest session ID for future incremental updates
            if (responseData.data.length > 0) {
                const newHighestId = Math.max(...responseData.data.map(session => parseInt(session.tsn_id)));
                console.log(`🔍 Reports: New highest ID: ${newHighestId}`);

                if (newHighestId > highestId) {
                    localStorage.setItem('highestSessionId', newHighestId);
                }

                // Display the data as an incremental update if we had a previous highest ID
                displayReports(responseData.data, highestId > 0);
            } else {
                console.log(`🔍 Reports: No new sessions found`);
            }
        } else {
            console.error('Invalid response format:', responseData);
            if (!document.querySelector('#reports-table tbody tr')) {
                // Only show "No reports found" if the table is empty
                displayEmptyMessage();
            }
        }

        // Update the UI
        document.getElementById('loading-indicator').style.display = 'none';
        // Format refresh time according to selected timezone
        const now = new Date();
        const selectedTimezone = getSelectedTimezone();
        const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };

        // Add timezone option if not local
        if (selectedTimezone !== 'local') {
            timeOptions.timeZone = selectedTimezone;
        }

        const refreshStatus = document.getElementById('refresh-status');
        refreshStatus.textContent = `Last updated: ${now.toLocaleTimeString('en-US', timeOptions)}`;
        // Store original time for timezone changes
        refreshStatus.dataset.originalTime = now.toISOString();
    } catch (error) {
        console.error('Error loading reports:', error);
        document.getElementById('loading-indicator').style.display = 'none';
        if (!document.querySelector('#reports-table tbody tr')) {
            displayEmptyMessage();
        }
    }
}

/**
 * Transform recent runs data from the database API to the frontend format
 * @param {Array} recentRuns - Recent runs data from the database API
 * @returns {Array} - Transformed data for the frontend
 */
function transformRecentRunsData(recentRuns) {
    // IMPORTANT: This function is no longer used directly
    // since the API now returns data in the correct format.
    // The displayReports function handles both formats.

    return recentRuns;
}

// DataTable instance
let reportsDataTable = null;

/**
 * Display reports in the table
 * @param {Array} reports - Test reports to display
 * @param {boolean} isIncremental - Whether this is an incremental update or full refresh
 */
function displayReports(reportsInput, isIncremental = false) {
    console.log('Displaying reports:', reportsInput ? reportsInput.length : 0);

    // Process the input to ensure we have a valid array of reports
    let reports = reportsInput;

    console.log('displayReports called with reports:', reports);
    if (reports && reports.length > 0) {
        console.log('First report sample:', reports[0]);
    }

    // Handle various response formats
    if (reportsInput && !Array.isArray(reportsInput)) {
        console.log('Reports data is not an array, checking for nested data structure');
        // Check for common API response formats
        if (reportsInput.data && Array.isArray(reportsInput.data)) {
            reports = reportsInput.data;
            console.log('Found reports in data property');
        } else if (reportsInput.reports && Array.isArray(reportsInput.reports)) {
            reports = reportsInput.reports;
            console.log('Found reports in reports property');
        } else {
            // Search for any array in the response
            for (let key in reportsInput) {
                if (Array.isArray(reportsInput[key])) {
                    reports = reportsInput[key];
                    console.log(`Found reports in ${key} property`);
                    break;
                }
            }
        }
    }

    // Make sure currentState.reports is updated
    currentState.reports = reports;

    // If we still don't have an array, create an empty one
    if (!Array.isArray(reports)) {
        console.warn('Could not find reports array in response, using empty array');
        reports = [];
    }
    if (!reports || !Array.isArray(reports) || reports.length === 0) {
        console.log('No reports to display, showing empty message');
        displayEmptyMessage();
        return;
    }

    console.log(`Displaying ${reports.length} reports${isIncremental ? ' (incremental update)' : ''}`);

    // Get the table body
    const tableBody = elements.reportsTable.querySelector('tbody');
    if (!tableBody) {
        console.error('Table body not found');
        return;
    }

    if (!isIncremental) {
        tableBody.innerHTML = '';
    }

    // Use a safer sort approach with error handling
    try {
        // Sort reports by start time (newest first)
        reports.sort((a, b) => {
            try {
                const aTime = a.start_time || a.startTime || a.start_ts ? new Date(a.start_time || a.startTime || a.start_ts).getTime() : 0;
                const bTime = b.start_time || b.startTime || b.start_ts ? new Date(b.start_time || b.startTime || b.start_ts).getTime() : 0;
                return bTime - aTime;
            } catch (err) {
                console.warn('Error comparing dates during sort:', err);
                return 0; // If comparison fails, don't change order
            }
        });
    } catch (sortError) {
        console.error('Error sorting reports:', sortError);
        // Continue with unsorted reports
    }

    // Display each report
    reports.forEach(report => {
        try {
            console.log('Processing report for display:', report);

            // Correctly access the time properties based on API response
            // The API returns start_time and end_time (with underscores)
            const startTime = formatDateTime(report.start_time || report.startTime);

            // For 'Running' status tests, always show 'Not completed' as end time
            const status = report.status || 'Unknown';
            const endTime = (status.toLowerCase() === 'running' || status.toLowerCase() === 'queued') ?
                'Not completed' :
                ((report.end_time || report.endTime) ? formatDateTime(report.end_time || report.endTime) : 'Not completed');

            // Calculate duration
            let duration = 'N/A';
            if (report.start_time && report.end_time &&
                status.toLowerCase() !== 'running' &&
                status.toLowerCase() !== 'queued') {
                duration = calculateDuration(report.start_time, report.end_time);
            }

            // Status - used to determine row color
            const statusClass = getStatusClass(status);

            // Test ID - use tsn_id as the primary identifier (this is the session ID)
            const displayId = report.tsn_id || report.id || '';

            // Test case or suite ID - use test_id from API which is either tc_id or ts_id
            const testCaseOrSuiteId = report.test_id || (report.tc_id || report.ts_id || '-');

            // Test name - use test_name from API
            const testName = report.test_name || report.name || `Test ${testCaseOrSuiteId}`;

            // Determine passed/failed count
            const passedTests = report.passed_cases || report.passed || 0;
            const failedTests = report.failed_cases || report.failed || 0;

            // Create a new row for this report
            const row = document.createElement('tr');
            row.className = `table-${statusClass}`;

            // Process user data - prioritize the uid field since that's where emails are coming from
            // Terminal output shows that user emails are stored in the uid field
            const userData = report.uid || report.user_id || report.user || report.created_by || report.username || '';
            console.log('User data for report:', {
                reportId: report.tsn_id,
                rawUid: report.uid,
                user_display: report.user_display,
                userData: userData
            });

            // Use user_display from backend if available, otherwise format the userData
            const formattedUser = report.user_display || formatUserEmail(userData);

            // Add data attributes for filtering AFTER formattedUser is defined
            row.setAttribute('data-user-id', report.uid || '');
            row.setAttribute('data-user-display', report.user_display || formattedUser || 'Unknown');
            console.log('Formatted user name:', formattedUser);

            // Store processed user data in the report object for filtering
            report.processed_user = formattedUser;

            // Create individual cells instead of using innerHTML - with the new duration cell
            const cells = [
                createCell(displayId),
                createCell(testName),
                createCell(testCaseOrSuiteId),
                createCell(status),
                createCell(startTime),
                createCell(endTime),
                createDurationCell(duration, report.start_time, report.end_time), // Use specialized duration cell with sorting attribute
                createCell(formattedUser), // User cell with properly formatted user data
                createCell(passedTests, 'text-success'),
                createCell(failedTests, 'text-danger'),
                createActionCell(report.tsn_id || report.id || '')
            ];

            // Append all cells to the row
            cells.forEach(cell => row.appendChild(cell));

            // Add row to the table
            tableBody.appendChild(row);
        } catch (error) {
            console.error(`Error displaying report for ID ${report.tsn_id || 'unknown'}:`, error);
            console.error('Report data that caused error:', JSON.stringify(report, null, 2));
        }
    });

    // Update counters
    updateCounters();

    // Initialize DataTable if it doesn't exist
    if (!reportsDataTable) {
        initializeDataTable();
    } else {
        // DataTable already exists
        if (isIncremental) {
            // For incremental, if you are adding rows directly to DOM and not using reportsDataTable.row.add(),
            // then invalidating might be necessary.
            // However, it's generally better to add new data via reportsDataTable.row.add().
            reportsDataTable.rows().invalidate().draw();
            console.log('DataTable incrementally updated and redrawn.');
        } else {
            // Full Reload: DataTable exists. The tableBody has been cleared and new <tr> elements
            // have been manually appended by the loop above.
            reportsDataTable.clear(); // Clear data from DataTable's cache

            // Find all `tr` elements in the tbody (these are the new rows we just added)
            const rowsToAdd = $(tableBody).find('tr');

            if (rowsToAdd.length > 0) {
                reportsDataTable.rows.add(rowsToAdd); // Add the new DOM rows to DataTable
            }

            reportsDataTable.draw(false); // Redraw the table. `false` preserves paging.
            console.log('DataTable cleared and redrawn with new full dataset from updated DOM.');
        }
    }

    // Populate user filter dropdown with unique users
    populateUserFilter();
}

/**
 * Display an empty message in the table
 */
function displayEmptyMessage() {
    const tbody = document.querySelector('#reports-table tbody');
    tbody.innerHTML = '<tr><td colspan="10" class="text-center">No reports found</td></tr>';
}

/**
 * Update the reports table with current data
 */
function updateReportsTable() {
    console.log('Updating reports table with current data:', currentState.reports);

    // Check if we have any reports
    if (!currentState.reports || currentState.reports.length === 0) {
        // Update the colspan to accommodate all columns
        elements.reportsTable.querySelector('tbody').innerHTML = '<tr><td colspan="10" class="text-center">No reports found</td></tr>';
        return;
    }

    // Sort reports by start time (newest first)
    const sortedReports = [...currentState.reports].sort((a, b) => {
        const aTime = a.startTime || a.start_time || a.start_ts ? new Date(a.startTime || a.start_time || a.start_ts).getTime() : 0;
        const bTime = b.startTime || b.start_time || b.start_ts ? new Date(b.startTime || b.start_time || b.start_ts).getTime() : 0;
        return bTime - aTime;
    });

    // Get table body
    const tableBody = elements.reportsTable.querySelector('tbody');
    tableBody.innerHTML = '';

    // Build table rows
    sortedReports.forEach(report => {
        const row = document.createElement('tr');

        // Set row class based on status
        if (report.status && report.status.toLowerCase() === 'failed') {
            row.classList.add('table-danger');
        } else if (report.status && report.status.toLowerCase() === 'passed') {
            row.classList.add('table-success');
        }

        // Format dates
        const startTime = formatDateTime(report.startTime || report.start_time || report.start_ts);
        const endTime = formatDateTime(report.endTime || report.end_time || report.end_ts);

        // Get user info for initiator column
        const userEmail = report.uid || report.user_id || report.user || '';
        const userName = formatUserEmail(userEmail);

        // Get test ID and name
        const tsnId = report.tsn_id || report.id || '';
        const testId = report.test_id || report.tc_id || report.ts_id || '';
        const testName = report.test_name || report.name || `Test ${testId}`;

        // Determine passed/failed count
        const passedCount = report.passed_cases || report.passed || 0;
        const failedCount = report.failed_cases || report.failed || 0;

        // Create individual cells instead of using innerHTML
        const cells = [
            createCell(tsnId),
            createCell(testName),
            createCell(testId),
            createCell(report.status || 'Unknown'),
            createCell(startTime),
            createCell(endTime),
            createUserCell(userEmail, userName),
            createCell(passedCount, 'text-success'),
            createCell(failedCount, 'text-danger'),
            createActionCell(tsnId)
        ];

        // Append all cells to the row
        cells.forEach(cell => row.appendChild(cell));

        // Add the row to the table
        tableBody.appendChild(row);
    });

    // Add event listeners to view details buttons
    document.querySelectorAll('.view-details').forEach(button => {
        button.addEventListener('click', function() {
            const tsnId = this.getAttribute('data-tsn-id');
            console.log('View details clicked for report ID:', tsnId);
            loadTestDetails(tsnId);
        });
    });
}

/**
 * Create a table cell with content
 * @param {*} content - Cell content
 * @param {string} className - Optional CSS class for the cell
 * @returns {HTMLTableCellElement} - The created table cell
 */
function createCell(content, className) {
    const cell = document.createElement('td');
    cell.textContent = content;
    if (className) {
        cell.classList.add(className);
    }
    return cell;
}

/**
 * Create a cell specifically for duration with proper sorting attribute
 * @param {string} durationText - Formatted duration text (e.g., "1h 2m 30s")
 * @param {string} startTime - Start time string
 * @param {string} endTime - End time string
 * @returns {HTMLTableCellElement} - The created duration cell with data attribute for sorting
 */
function createDurationCell(durationText, startTime, endTime) {
    const cell = document.createElement('td');
    cell.textContent = durationText;

    // Calculate duration in seconds for sorting
    let durationSeconds = 0;

    try {
        if (startTime && endTime) {
            const start = new Date(startTime);
            const end = new Date(endTime);

            if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
                durationSeconds = Math.floor((end - start) / 1000);
            }
        }
    } catch (error) {
        console.error('Error calculating duration seconds:', error);
    }

    // Set data attribute for sorting
    cell.setAttribute('data-duration-seconds', durationSeconds.toString());

    return cell;
}

/**
 * Create a cell for the user info with a tooltip
 * @param {string} email - User email
 * @param {string} name - Formatted user name
 * @returns {HTMLTableCellElement} - The created table cell
 */
function createUserCell(email, name) {
    const cell = document.createElement('td');
    cell.textContent = name;
    cell.title = email; // Add tooltip with full email
    return cell;
}

/**
 * Create a cell with action buttons
 * @param {string} tsnId - Test session ID
 * @returns {HTMLTableCellElement} - The created table cell
 */
function createActionCell(tsnId) {
    const cell = document.createElement('td');

    // Create Details button
    const detailsBtn = document.createElement('button');
    detailsBtn.className = 'btn btn-primary btn-sm view-details';
    detailsBtn.textContent = 'Details';
    detailsBtn.setAttribute('data-tsn-id', tsnId);

    // Add direct click event to the button
    detailsBtn.addEventListener('click', function() {
        console.log('Details button clicked for test ID:', tsnId);
        loadTestDetails(tsnId);
    });

    cell.appendChild(detailsBtn);
    return cell;
}

/**
 * Get CSS class for status
 * @param {string} status - Status text
 * @returns {string} - CSS class for the status
 */
function getStatusClass(status) {
    if (!status) return 'secondary';

    switch(status.toLowerCase()) {
        case 'success':
        case 'passed':
            return 'success';
        case 'failed':
            return 'danger';
        case 'running':
            return 'primary';
        case 'queued':
            return 'info';
        case 'completed':
            return 'secondary';
        default:
            return 'secondary';
    }
}

/**
 * Format user email to display name
 * @param {string} email - User email or ID
 * @returns {string} - Formatted user name
 */
function formatUserEmail(email) {
    // Log the input to debug user data handling
    console.log('formatUserEmail input:', email);

    if (!email) {
        console.log('Empty email value, returning Unknown');
        return 'Unknown';
    }

    // If it's not a string, convert to string
    if (typeof email !== 'string') {
        email = String(email);
        console.log('Converted non-string to:', email);
    }

    // If it's an empty string or 'null', return 'Unknown'
    if (email === '' || email === 'null' || email === 'NULL') {
        console.log('Email is empty string or null value');
        return 'Unknown';
    }

    // If it looks like an email address, extract the username part
    if (email.includes('@')) {
        const username = email.split('@')[0];
        console.log('Extracted username from email:', username);
        return username;
    }

    // Otherwise, return the original value
    console.log('Using original value as username:', email);
    return email;
}

// Load details for a specific test
async function loadTestDetails(testId) {
    try {
        console.log(`Loading details for test ${testId}...`);

        // Show loading indicator
        if (elements.testDetailsSection) {
            elements.testDetailsSection.classList.remove('d-none');
            // Scroll to the details section
            elements.testDetailsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });

            if (elements.testDetailsTitle) {
                elements.testDetailsTitle.textContent = `Loading Test Details...`;
            }
        } else {
            console.error('Test details section element not found');
            return;
        }

        // Get credentials
        let credentials = { uid: '<EMAIL>', password: 'test' };

        // Try to get from window.apiService
        if (window.apiService && window.apiService.credentials) {
            credentials = window.apiService.credentials;
            console.log('Using credentials from API service');
        } else {
            // Try to get credentials from session storage as fallback
            const sessionUid = sessionStorage.getItem('smarttest_uid');
            const sessionPwd = sessionStorage.getItem('smarttest_pwd');
            if (sessionUid && sessionPwd) {
                credentials = { uid: sessionUid, password: sessionPwd };
                console.log('Using credentials from session storage');
            } else {
                console.log('Using default test credentials');
            }
        }

        // Find basic test info in current state - will be used only as a fallback
        const basicTestInfo = currentState.reports.find(r => r.id === testId || r.tsn_id === testId);
        let testDetails = null;

        try {
            // DIAGNOSTIC: Check what services are available
            console.log('[DIAGNOSTIC] Available services check:');
            console.log('- window.externalApiService:', !!window.externalApiService);
            console.log('- window.enhancedExternalApiService:', !!window.enhancedExternalApiService);
            console.log('- window.apiService:', !!window.apiService);

            // FORCE EXTERNAL API USAGE: Always try external API first if any external service is available
            const externalApiService = window.externalApiService || window.enhancedExternalApiService;

            if (externalApiService) {
                console.log('🚀 FORCING External API Service for test details');
                console.log('Service type:', window.externalApiService ? 'externalApiService' : 'enhancedExternalApiService');

                try {
                    const details = await loadTestDetailsFromExternalApi(testId, credentials);
                    console.log('[DEBUG] loadTestDetails: External API details response:', details);

                    if (details && details.tsn_id) {
                        currentState.currentTestDetails = details;
                        console.log('✅ Successfully loaded test details from External API');
                    } else {
                        console.warn('⚠️ External API returned invalid/empty details, falling back to database API');
                        await loadTestDetailsFromDatabaseApi(testId, credentials);
                    }
                } catch (externalApiError) {
                    console.error('❌ External API failed:', externalApiError);
                    console.log('🔄 Falling back to database API due to external API error');
                    await loadTestDetailsFromDatabaseApi(testId, credentials);
                }
            } else if (window.apiService) {
                console.log('⚠️ No External API Service available, using Database API via ApiService');

                // Set credentials if they're not already set
                if (!window.apiService.credentials.uid) {
                    window.apiService.setCredentials(credentials.uid, credentials.password);
                }

                // Use the getTestDetails method from ApiService (this calls database API)
                const details = await window.apiService.getTestDetails(testId);
                console.log('[DEBUG] loadTestDetails: Database API details response:', details);

                if (!details) {
                    console.error('[DEBUG] loadTestDetails: No details returned from Database API');
                    throw new Error('No test details returned from Database API');
                }

                // Store the test details in current state
                currentState.currentTestDetails = details;
            } else {
                // Fallback to database API if no API services are available
                console.log('❌ No API Services available, falling back to direct database API');
                await loadTestDetailsFromDatabaseApi(testId, credentials);
            }

            // Get the updated test details from current state
            testDetails = currentState.currentTestDetails;

            // If still not found, fall back to basic info (if available)
            if (!testDetails && basicTestInfo) {
                console.log('[DEBUG] loadTestDetails: Falling back to basic test info from cache');
                testDetails = basicTestInfo;
                currentState.currentTestDetails = testDetails;
            }

            // If still not found, show error
            if (!testDetails) {
                if (elements.testDetailsTitle) {
                    elements.testDetailsTitle.textContent = `Test Details Not Found`;
                }
                return;
            }
        } catch (error) {
            console.error(`Error fetching test details:`, error);
            if (elements.testDetailsTitle) {
                elements.testDetailsTitle.textContent = `Error Loading Test Details: ${error.message}`;
            }
            return;
        }

        // Make sure we have a test cases array
        if (!testDetails.test_cases || testDetails.test_cases.length === 0) {
            // Create mock test cases if they're missing
            testDetails.test_cases = [];
            if (testDetails.tc_id) {
                // This is a single test case run
                testDetails.test_cases.push({
                    tc_id: testDetails.tc_id,
                    description: testDetails.test_name || testDetails.name || `Test Case ${testDetails.tc_id}`,
                    status: testDetails.status || 'Unknown',
                    duration: testDetails.duration || '0:00',
                    error_message: testDetails.error_message || ''
                });
            } else if (testDetails.test_id || testDetails.ts_id) {
                // This is a test suite that should have test cases
                // Try to extract test cases from any other available fields
                // testDetails.test_cases = []; // Already initialized above

                // Try to calculate total cases from passed/failed/skipped counts
                const totalCases = (testDetails.passed_cases || 0) + (testDetails.failed_cases || 0) + (testDetails.skipped_cases || 0);

                if (totalCases > 0) {
                    // We know how many cases there are, but not their details
                    // Create a placeholder entry
                    // Commented out the 'Multiple' placeholder push:
                    // testDetails.test_cases.push({
                    //     tc_id: 'Multiple',
                    //     description: `This test suite contains ${totalCases} test cases`,
                    //     status: 'See summary',
                    //     duration: testDetails.duration || '0:00',
                    //     error_message: testDetails.error || '' // Use main session error for placeholder
                    // });
                }
            }
        }

        // Update UI
        displayTestDetails();

        // If the test has test_cases, update the test cases table
        if (testDetails.test_cases && testDetails.test_cases.length > 0) {
            updateTestCasesTable(testDetails.test_cases);
        } else {
            // If no test cases are available, show empty table with clear message
            updateTestCasesTable([]);
        }

        console.log('Test details loaded:', testDetails);
    } catch (error) {
        console.error('Error loading test details:', error);
        if (elements.testDetailsTitle) {
            elements.testDetailsTitle.textContent = `Error Loading Test Details`;
        }
    }
}

/**
 * Load test details from the external API directly
 * @param {string} testId - Test session ID
 * @param {Object} credentials - User credentials
 */
async function loadTestDetailsFromExternalApi(testId, credentials) {
    try {
        console.log(`🔍 Loading test details from external API for ${testId}...`);
        console.log('📋 Credentials:', { uid: credentials?.uid, hasPassword: !!credentials?.password });

        // Use the external API service if available
        const externalApiService = window.externalApiService || window.enhancedExternalApiService;
        console.log('🔧 External API Service found:', !!externalApiService);
        console.log('🔧 Service type:', window.externalApiService ? 'externalApiService' : 'enhancedExternalApiService');

        if (!externalApiService) {
            throw new Error('External API Service is not available');
        }

        // Check session validity
        const isSessionValid = externalApiService.isSessionValid();
        console.log('🔐 Session valid:', isSessionValid);

        // Ensure we have valid credentials
        if (!isSessionValid && credentials) {
            console.log('🔑 Logging in to external API...');
            try {
                await externalApiService.login(credentials.uid, credentials.password);
                console.log('✅ Login successful');
            } catch (loginError) {
                console.error('❌ Login failed:', loginError);
                throw new Error(`External API login failed: ${loginError.message}`);
            }
        }

        // Get report summary from external API - direct HTML response format
        console.log('📡 Fetching report summary...');

        // FIXED: Use proper cookie handling - browsers automatically include cookies with credentials: 'include'
        // Do NOT manually set Cookie header as browsers block this for security reasons
        const fetchOptions = {
            credentials: 'include', // This ensures cookies are automatically included
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        };

        // Log session status for debugging
        if (externalApiService.jsessionId) {
            console.log(`🔑 External API session available: ${externalApiService.jsessionId === 'BROWSER_MANAGED' ? 'Browser-managed' : 'Explicit ID'}`);
        } else {
            console.warn('⚠️ No JSESSIONID found in ExternalApiService - this may cause 401 errors');
        }

        console.log('📡 Making request with credentials: include to ensure cookies are sent');
        const reportSummaryResponse = await fetch(`/api/external/ReportSummary?tsn_id=${testId}`, fetchOptions);

        console.log('📡 Summary response status:', reportSummaryResponse.status);
        console.log('📡 Summary response headers:', Object.fromEntries(reportSummaryResponse.headers.entries()));

        if (!reportSummaryResponse.ok) {
            if (reportSummaryResponse.status === 401) {
                console.error('❌ 401 Unauthorized - Authentication failed!');
                console.log('🔍 Debugging 401 error:');
                console.log('- Session ID:', externalApiService.jsessionId);
                console.log('- Session valid:', externalApiService.isSessionValid());
                console.log('- Document cookies:', document.cookie);
                console.log('- Request was made with credentials: include');

                // Try to re-login and retry once
                console.log('🔄 Attempting to re-login and retry...');
                try {
                    await externalApiService.login(credentials.uid, credentials.password);
                    console.log('✅ Re-login successful, retrying request...');

                    const retryResponse = await fetch(`/api/external/ReportSummary?tsn_id=${testId}`, fetchOptions);
                    if (retryResponse.ok) {
                        console.log('✅ Retry successful!');
                        // Continue with the retry response
                        const retryHtml = await retryResponse.text();
                        const retrySummaryData = parseReportSummaryHtml(retryHtml, testId);

                        // Continue with details request using retry pattern
                        const retryDetailsResponse = await fetch(`/api/external/ReportDetails?tsn_id=${testId}`, fetchOptions);
                        if (retryDetailsResponse.ok) {
                            const retryDetailsHtml = await retryDetailsResponse.text();
                            const retryDetailsData = parseReportDetailsHtml(retryDetailsHtml, testId);

                            // Build the final result
                            const testDetails = {
                                id: testId,
                                tsn_id: testId,
                                test_id: retrySummaryData.suite_id || '',
                                test_name: retrySummaryData.suite_name || 'Unknown Test',
                                name: retrySummaryData.suite_name || 'Unknown Test',
                                type: retrySummaryData.type || 'Test Suite',
                                environment: retrySummaryData.environment || 'Unknown',
                                status: retrySummaryData.status || 'Unknown',
                                start_time: retrySummaryData.start_time,
                                end_time: retrySummaryData.end_time,
                                duration: calculateDuration(retrySummaryData.start_time, retrySummaryData.end_time) || '0:00',
                                user: retrySummaryData.owner || credentials.uid || 'System',
                                trigger: retrySummaryData.trigger || 'Manual',
                                total_cases: retrySummaryData.total_cases || retryDetailsData.test_cases.length || 0,
                                passed_cases: retrySummaryData.passed_cases || 0,
                                failed_cases: retrySummaryData.failed_cases || 0,
                                skipped_cases: retrySummaryData.skipped_cases || 0,
                                test_cases: retryDetailsData.test_cases || [],
                                report: retryDetailsData.report_html || ''
                            };

                            currentState.currentTestDetails = testDetails;
                            console.log('✅ Test details loaded successfully after retry');
                            return testDetails;
                        }
                    }
                } catch (retryError) {
                    console.error('❌ Retry failed:', retryError);
                }
            }

            throw new Error(`Failed to get report summary: ${reportSummaryResponse.status} ${reportSummaryResponse.statusText}`);
        }

        const summaryHtml = await reportSummaryResponse.text();
        console.log('📄 Summary HTML length:', summaryHtml.length);
        const summaryData = parseReportSummaryHtml(summaryHtml, testId);
        console.log('📊 Parsed summary data:', summaryData);

        // Get report details with all test cases - direct HTML response format
        console.log('📡 Fetching report details...');

        // Use the same fetchOptions to ensure cookies are automatically sent
        console.log('📡 Making details request with credentials: include to ensure cookies are sent');
        const reportDetailsResponse = await fetch(`/api/external/ReportDetails?tsn_id=${testId}`, fetchOptions);

        console.log('📡 Details response status:', reportDetailsResponse.status);
        if (!reportDetailsResponse.ok) {
            throw new Error(`Failed to get report details: ${reportDetailsResponse.status} ${reportDetailsResponse.statusText}`);
        }

        const detailsHtml = await reportDetailsResponse.text();
        console.log('📄 Details HTML length:', detailsHtml.length);
        const detailsData = parseReportDetailsHtml(detailsHtml, testId);
        console.log('📊 Parsed details data:', detailsData);

        // Combine summary and details into a unified format for our UI
        const testDetails = {
            id: testId,
            tsn_id: testId,
            test_id: summaryData.suite_id || '',
            test_name: summaryData.suite_name || 'Unknown Test',
            name: summaryData.suite_name || 'Unknown Test',
            type: summaryData.type || 'Test Suite',
            environment: summaryData.environment || 'Unknown',
            status: summaryData.status || 'Unknown',
            start_time: summaryData.start_time,
            end_time: summaryData.end_time,
            duration: calculateDuration(summaryData.start_time, summaryData.end_time) || '0:00',
            user: summaryData.owner || credentials.uid || 'System',
            trigger: summaryData.trigger || 'Manual',
            total_cases: summaryData.total_cases || detailsData.test_cases.length || 0,
            passed_cases: summaryData.passed_cases || 0,
            failed_cases: summaryData.failed_cases || 0,
            skipped_cases: summaryData.skipped_cases || 0,
            test_cases: detailsData.test_cases || [],
            report: detailsData.report_html || ''
        };

        // Update current state
        currentState.currentTestDetails = testDetails;

        console.log('Test details loaded from external API:', testDetails);

        // Return the test details so they can be used by the caller
        return testDetails;
    } catch (error) {
        // Log the specific error message and the full error object for more context
        const errorMessage = error.message || 'Unknown error loading test details from external API';
        console.error(`Error in loadTestDetailsFromExternalApi for ${testId}: ${errorMessage}`, error);
        // Re-throw a new error with a clear message, possibly including the original if it's useful
        throw new Error(`Failed to load details for ${testId} from external API: ${errorMessage}`);
    }
}

/**
 * Parse HTML from the Report Summary endpoint
 * @param {string} html - The HTML content to parse
 * @param {string} testId - The test ID
 * @returns {Object} The parsed summary data
 */
function parseReportSummaryHtml(html, testId) {
    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // Extract basic info
        const summary = {
            tsn_id: testId,
            status: 'Unknown',
            start_time: null,
            end_time: null,
            owner: null,
            suite_id: null,
            suite_name: null,
            environment: null,
            passed_cases: 0,
            failed_cases: 0,
            skipped_cases: 0,
            variables: {}
        };

        // Extract text content from the document
        const textContent = doc.body.textContent;
        console.log('Parsing summary HTML for test ID:', testId);

        // Extract test session ID
        const sessionIdMatch = textContent.match(/Test Session ID:\s*([0-9]+)/);
        if (sessionIdMatch) {
            summary.tsn_id = sessionIdMatch[1].trim();
            console.log('Found session ID:', summary.tsn_id);
        }

        // Extract owner
        const ownerMatch = textContent.match(/Session Owner:\s*([^\n]+)/);
        if (ownerMatch) {
            summary.owner = ownerMatch[1].trim();
            console.log('Found owner:', summary.owner);
        }

        // Extract suite ID
        // Look for Test Suite ID in the text, including the link if present
        const suiteIdLink = doc.querySelector('a[href*="SuiteEditor"]');
        if (suiteIdLink) {
            const href = suiteIdLink.getAttribute('href');
            const suiteIdMatch = href.match(/[?&]t[cs]_id=([0-9]+)/);
            if (suiteIdMatch) {
                summary.suite_id = suiteIdMatch[1];
                console.log('Found suite ID from link:', summary.suite_id);
            }
        } else {
            // Fallback to text parsing
            const suiteIdMatch = textContent.match(/Test Suite ID:\s*([0-9]+)/);
            if (suiteIdMatch) {
                summary.suite_id = suiteIdMatch[1].trim();
                console.log('Found suite ID from text:', summary.suite_id);
            }
        }

        // Extract suite name from the report section
        // Look for the suite name in the Report section using the DOM
        const reportSection = textContent.substring(textContent.indexOf('Report'));
        const suiteMatch = reportSection.match(/Suite:\s*[^\n]*?([0-9]+)[^\n]*?\s+([^<\n]+)/);
        if (suiteMatch && suiteMatch.length > 2) {
            summary.suite_name = suiteMatch[2].trim();
            console.log('Found suite name:', summary.suite_name);

            // If we didn't find suite ID earlier, use it from here
            if (!summary.suite_id && suiteMatch[1]) {
                summary.suite_id = suiteMatch[1].trim();
                console.log('Using suite ID from report section:', summary.suite_id);
            }
        }

        // Extract times
        const startTimeMatch = textContent.match(/Start Time:\s*([^\n]+)/);
        if (startTimeMatch) {
            summary.start_time = startTimeMatch[1].trim();
            console.log('Found start time:', summary.start_time);
        }

        const endTimeMatch = textContent.match(/End Time:\s*([^\n]+)/);
        if (endTimeMatch) {
            summary.end_time = endTimeMatch[1].trim();
            console.log('Found end time:', summary.end_time);
        }

        // Extract error information if present
        const errorMatch = textContent.match(/Error:\s*([^\n]+)/);
        if (errorMatch) {
            summary.error = errorMatch[1].trim();
            console.log('Found error info:', summary.error);
        }

        // Determine overall status
        if (textContent.includes('<span style=\'color:green\'>PASS</span>') && !textContent.includes('<span style=\'color:red\'>FAIL</span>')) {
            summary.status = 'Success';
        } else if (textContent.includes('<span style=\'color:red\'>FAIL</span>')) {
            summary.status = 'Failed';
        } else if (textContent.includes('PASS') && !textContent.includes('FAIL')) {
            summary.status = 'Success';
        } else if (textContent.includes('FAIL')) {
            summary.status = 'Failed';
        }
        console.log('Determined status:', summary.status);

        // Extract case counts
        const passedMatch = textContent.match(/Case\(s\) passed:\s*([0-9]+)/);
        if (passedMatch) {
            summary.passed_cases = parseInt(passedMatch[1], 10);
            console.log('Found passed cases:', summary.passed_cases);
        }

        const failedMatch = textContent.match(/Case\(s\) failed:\s*([0-9]+)/);
        if (failedMatch) {
            summary.failed_cases = parseInt(failedMatch[1], 10);
            console.log('Found failed cases:', summary.failed_cases);
        }

        // Extract environment
        const envMatch = textContent.match(/environment=([^\n]+)/);
        if (envMatch) {
            summary.environment = envMatch[1].trim();
            console.log('Found environment:', summary.environment);
        } else {
            // Try to get environment from variables
            const envirMatch = textContent.match(/envir=([^\n]+)/);
            if (envirMatch) {
                summary.environment = envirMatch[1].trim();
                console.log('Found environment from envir variable:', summary.environment);
            }
        }

        // Calculate total cases
        summary.total_cases = summary.passed_cases + summary.failed_cases + summary.skipped_cases;
        console.log('Total cases:', summary.total_cases);

        // Extract variables section
        const variablesMatch = textContent.match(/Variables:([\s\S]*?)(?:ID:|$)/);
        if (variablesMatch && variablesMatch[1]) {
            const variableText = variablesMatch[1];
            const variableLines = variableText.split('\n');

            variableLines.forEach(line => {
                const varMatch = line.match(/([^=]+)=([^\n]+)/);
                if (varMatch && varMatch.length > 2) {
                    const key = varMatch[1].trim();
                    const value = varMatch[2].trim();
                    summary.variables[key] = value;
                }
            });

            // Look for test name in variables if we didn't find it in the suite section
            if (!summary.suite_name && summary.variables['name']) {
                summary.suite_name = summary.variables['name'];
                console.log('Using suite name from variables:', summary.suite_name);
            }
        }

        // Get the full report HTML for reference
        // Find the report section that contains the pass/fail details
        const reportElement = doc.evaluate('//*[contains(text(), "Report")]', doc, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
        if (reportElement) {
            let reportNode = reportElement;
            let reportHtml = '';

            // Find the complete report section
            while (reportNode && !reportNode.textContent.includes('Case(s) passed:')) {
                reportNode = reportNode.nextSibling;
            }

            if (reportNode) {
                reportHtml = reportNode.textContent;
                summary.report_html = reportHtml;
            }
        }

        return summary;
    } catch (error) {
        console.error('Error parsing report summary HTML:', error);
        return { tsn_id: testId, error: error.message };
    }
}

/**
 * Parse HTML from the Report Details endpoint
 * @param {string} html - The HTML content to parse
 * @param {string} testId - The test ID
 * @returns {Object} The parsed details data
 */
function parseReportDetailsHtml(html, testId) {
    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        const result = {
            tsn_id: testId,
            test_cases: [],
            report_html: '',
            pagination: {
                currentPage: 1,
                totalPages: 1
            }
        };

        console.log('Parsing details HTML for test ID:', testId);

        // Extract test session ID from the page
        const tsnIdElement = doc.getElementById('tsn_id');
        if (tsnIdElement) {
            result.tsn_id = tsnIdElement.textContent.trim();
            console.log('Found session ID in details:', result.tsn_id);
        }

        // Extract pagination information if available
        const navigationSpan = doc.querySelector('.navigation');
        if (navigationSpan) {
            const currentPage = parseInt(navigationSpan.textContent.trim(), 10) || 1;
            const totalPages = parseInt(navigationSpan.getAttribute('data-size'), 10) || 1;

            result.pagination = {
                currentPage: currentPage,
                totalPages: totalPages
            };

            console.log(`Pagination: Page ${currentPage} of ${totalPages}`);
        }

        // Create a map to track unique test cases
        const testCaseMap = new Map();

        // Extract test cases from the table
        const tableRows = doc.querySelectorAll('table#table tr');
        console.log(`Found ${tableRows.length} rows in the details table`);

        if (tableRows.length > 0) {
            tableRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length >= 6) {
                    // Extract test case ID and sequence index
                    const tcIdLink = cells[0].querySelector('a');
                    const tcId = tcIdLink ? tcIdLink.textContent.trim() : cells[0].textContent.trim();

                    const seqIndexLink = cells[1].querySelector('a');
                    const seqIndex = seqIndexLink ? seqIndexLink.textContent.trim() : cells[1].textContent.trim();

                    // Extract test case status
                    let status = 'Unknown';
                    let statusChar = '';
                    const statusLink = cells[2].querySelector('a');
                    if (statusLink) {
                        statusChar = statusLink.textContent.trim();
                        if (statusChar === 'P' || statusLink.className.includes('P')) {
                            status = 'Passed';
                        } else if (statusChar === 'F' || statusLink.className.includes('F')) {
                            status = 'Failed';
                        }
                    }

                    // Extract description, input, and output data
                    const descriptionLink = cells[3].querySelector('a');
                    const description = descriptionLink ? descriptionLink.textContent.trim() : cells[3].textContent.trim();

                    const inputLink = cells[4].querySelector('a');
                    const input = inputLink ? inputLink.textContent.trim() : cells[4].textContent.trim();

                    const outputLink = cells[5].querySelector('a');
                    const output = outputLink ? outputLink.textContent.trim() : cells[5].textContent.trim();

                    // Get any error message for failed tests
                    const errorMessage = status === 'Failed' ? output : '';

                    // Build a test case object
                    const testCase = {
                        tc_id: tcId,
                        seq_index: seqIndex,
                        status: status,
                        statusChar: statusChar,
                        description: description,
                        input_output: `Input: ${input}\nOutput: ${output}`,
                        input_data: input,
                        output_data: output,
                        error_message: errorMessage
                    };

                    // Use a composite key to track unique test cases
                    const testCaseKey = `${tcId}-${seqIndex}`;

                    if (!testCaseMap.has(testCaseKey)) {
                        testCaseMap.set(testCaseKey, testCase);
                        result.test_cases.push(testCase);
                    }
                }
            });
        } else {
            console.warn('No test case rows found in the details table');
        }

        // Create a formatted HTML report
        result.report_html = `
            <div class="external-report">
                <div class="report-header">
                    <h5>Test Steps for Session ${result.tsn_id}</h5>
                    ${result.pagination.totalPages > 1 ? `<small class="text-muted">Page ${result.pagination.currentPage} of ${result.pagination.totalPages}</small>` : ''}
                </div>
                <div class="report-content">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>Test Case</th>
                                <th>Step</th>
                                <th>Status</th>
                                <th>Description</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${result.test_cases.map(tc => `
                                <tr class="${tc.status === 'Passed' ? 'table-success' : tc.status === 'Failed' ? 'table-danger' : ''}">
                                    <td><a href="#" class="test-case-link" data-tc-id="${tc.tc_id}">${tc.tc_id}</a></td>
                                    <td>${tc.seq_index}</td>
                                    <td><span class="badge ${tc.status === 'Passed' ? 'bg-success' : tc.status === 'Failed' ? 'bg-danger' : 'bg-secondary'}">${tc.status}</span></td>
                                    <td>${tc.description}</td>
                                    <td>
                                        <div class="test-step-details">
                                            ${tc.input_data ? `<div><strong>Input:</strong> <span class="text-monospace">${tc.input_data}</span></div>` : ''}
                                            ${tc.output_data ? `<div><strong>Output:</strong> <span class="text-monospace">${tc.output_data}</span></div>` : ''}
                                            ${tc.error_message ? `<div><strong>Error:</strong> <span class="text-danger">${tc.error_message}</span></div>` : ''}
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                    ${result.pagination.totalPages > 1 ? `
                    <div class="pagination-info text-center mt-2">
                        <small>Showing page ${result.pagination.currentPage} of ${result.pagination.totalPages}.
                        Additional test steps may be available on other pages.</small>
                    </div>` : ''}
                </div>
            </div>
        `;

        return result;
    } catch (error) {
        console.error('Error parsing report details HTML:', error);
        return { tsn_id: testId, test_cases: [], error: error.message };
    }
}

/**
 * Load test details from the database API
 * @param {string} testId - Test ID
 * @param {Object} credentials - Credentials for API
 */
async function loadTestDetailsFromDatabaseApi(testId, credentials) {
    try {
        console.log(`Loading test details from database API for test ${testId}...`);

        // Use ApiService if available
        if (window.apiService) {
            console.log('Using ApiService to load test details');

            // Set credentials if they're not already set
            if (!window.apiService.credentials.uid) {
                window.apiService.setCredentials(credentials.uid, credentials.password);
            }

            // Use the getTestDetails method we added to ApiService
            const testDetails = await window.apiService.getTestDetails(testId);

            // Store the test details in current state
            currentState.currentTestDetails = testDetails;

            console.log('Test details loaded from database API via ApiService:', testDetails);

            // If we don't have the full report, try to get it from the API
            if ((!currentState.currentTestDetails.report || currentState.currentTestDetails.report === '')
                && currentState.currentTestDetails.tsn_id) {

                console.log('Test details loaded but no report found, trying to get full data...');

                // Use ApiService to get the complete test report
                const recentRuns = await window.apiService.getRecentRuns({
                    limit: 1,
                    tsn_id: currentState.currentTestDetails.tsn_id
                });

                if (recentRuns && recentRuns.length > 0 && recentRuns[0].report) {
                    console.log('Full test report found in recent runs:', recentRuns[0]);
                    currentState.currentTestDetails.report = recentRuns[0].report;

                    // Copy over any other missing data
                    if (!currentState.currentTestDetails.test_name && recentRuns[0].test_name) {
                        currentState.currentTestDetails.test_name = recentRuns[0].test_name;
                    }
                    if (!currentState.currentTestDetails.status && recentRuns[0].status) {
                        currentState.currentTestDetails.status = recentRuns[0].status;
                    }
                }
            }
        } else {
            // Fallback to direct fetch for backward compatibility
            console.log('ApiService not available, using direct fetch');

            // Build the URL with path parameter and credentials
            const url = `${config.testDetailsEndpoint}/${testId}?uid=${encodeURIComponent(credentials.uid)}&password=${encodeURIComponent(credentials.password)}`;

            // Make the request
            const response = await fetch(url);

            // Check for success
            if (!response.ok) {
                throw new Error(`API error: ${response.status} ${response.statusText}`);
            }

            // Parse the response
            const data = await response.json();

            // Check if we have valid data
            if (!data || !data.success) {
                throw new Error(data?.message || 'Invalid response from API');
            }

            console.log('Test details loaded from database API:', data);

            // Store the test details in current state
            currentState.currentTestDetails = data.test || data;

            // If we don't have the full report, try to get it from the API
            if ((!currentState.currentTestDetails.report || currentState.currentTestDetails.report === '')
                && currentState.currentTestDetails.tsn_id) {

                console.log('Test details loaded but no report found, trying to get full data...');

                // Try to get the complete test report from the recent runs endpoint
                const fullDataUrl = `${config.reportingEndpoint}?limit=1&tsn_id=${currentState.currentTestDetails.tsn_id}&uid=${encodeURIComponent(credentials.uid)}&password=${encodeURIComponent(credentials.password)}`;

                const fullDataResponse = await fetch(fullDataUrl);
                if (fullDataResponse.ok) {
                    const fullData = await fullDataResponse.json();
                    if (fullData && fullData.length > 0 && fullData[0].report) {
                        console.log('Full test report found in recent runs:', fullData[0]);
                        currentState.currentTestDetails.report = fullData[0].report;

                        // Copy over any other missing data
                        if (!currentState.currentTestDetails.test_name && fullData[0].test_name) {
                            currentState.currentTestDetails.test_name = fullData[0].test_name;
                        }
                        if (!currentState.currentTestDetails.status && fullData[0].status) {
                            currentState.currentTestDetails.status = fullData[0].status;
                        }
                    }
                }
            }
        }

        return currentState.currentTestDetails;
    } catch (error) {
        console.error('Error loading test details from database API:', error);
        throw error;
    }
}

// Display test details in the UI
function displayTestDetails() {
    const test = currentState.currentTestDetails;
    if (!test) {
        console.error('No test details available to display');
        return;
    }

    console.log('Displaying test details:', test);

    // Update title - Format it consistently with UI standards
    const fullTestName = test.test_name || test.name || `Test ${test.tsn_id || test.id}`;
    // If the test name is too long, truncate it and add ellipsis
    const maxLength = 60;
    elements.testDetailsTitle.textContent = fullTestName.length > maxLength ?
        fullTestName.substring(0, maxLength) + '...' :
        fullTestName;

    // Set basic details
    elements.detailTestId.textContent = test.test_id || test.tc_id || test.ts_id || 'N/A';
    elements.detailTestType.textContent = test.type || 'Test Suite';
    elements.detailEnvironment.textContent = test.environment || test.envir || 'Unknown';
    elements.detailStartTime.textContent = formatDateTime(test.startTime || test.start_time || test.start_ts);

    // Set status with appropriate class
    const statusElement = elements.detailStatus;
    statusElement.textContent = test.status || 'Unknown';

    // Remove all status classes
    statusElement.classList.remove('text-success', 'text-danger', 'text-warning');

    // Add appropriate status class
    if (test.status && test.status.toLowerCase().includes('pass')) {
        statusElement.classList.add('text-success');
    } else if (test.status && test.status.toLowerCase().includes('fail')) {
        statusElement.classList.add('text-danger');
    } else {
        statusElement.classList.add('text-warning');
    }

    // Set other details
    elements.detailDuration.textContent = test.duration ||
        (test.start_time && test.end_time ? calculateDuration(test.start_time, test.end_time) : 'N/A');
    elements.detailUser.textContent = formatUserEmail(test.user || test.uid || 'System');
    elements.detailTrigger.textContent = test.trigger || test.source || 'Manual';

    // Calculate total cases based on available information
    let totalCases = 0;
    const passedCases = parseInt(test.passed_cases || test.passed || 0);
    const failedCases = parseInt(test.failed_cases || test.failed || 0);
    const skippedCases = parseInt(test.skipped_cases || test.skipped || 0);

    // Sum up the known case counts
    totalCases = passedCases + failedCases + skippedCases;

    // If we have test cases array, use its length as a backup
    if (test.test_cases && test.test_cases.length > 0) {
        if (totalCases === 0) {
            totalCases = test.test_cases.length;
        }
    }

    // If we have total_cases field, use it as a backup
    if (totalCases === 0 && test.total_cases) {
        totalCases = parseInt(test.total_cases);
    }

    // Ensure we display at least 1 for single test case runs
    if (totalCases === 0 && test.tc_id) {
        totalCases = 1;
    }

    // Update test result statistics
    elements.detailTotalCases.textContent = totalCases;
    elements.detailPassedCases.textContent = passedCases;
    elements.detailFailedCases.textContent = failedCases;
    elements.detailSkippedCases.textContent = skippedCases;

    // Display HTML report if available
    if (test.report) {
        // Create an additional section for the HTML report
        let reportContainer = document.getElementById('html-report-container');
        if (!reportContainer) {
            reportContainer = document.createElement('div');
            reportContainer.id = 'html-report-container';
            reportContainer.className = 'html-report-section mt-4';

            // Add a heading
            const reportHeading = document.createElement('h5');
            reportHeading.textContent = 'Detailed Test Report';
            reportContainer.appendChild(reportHeading);

            // Create content container
            const reportContent = document.createElement('div');
            reportContent.id = 'html-report-content';
            reportContent.className = 'html-report-content p-3 border rounded';
            reportContainer.appendChild(reportContent);

            // Add to test details section after the test cases table
            const casesSection = document.querySelector('.test-results-stats').parentNode;
            casesSection.appendChild(reportContainer);
        }

        // Set the HTML report content
        const reportContent = document.getElementById('html-report-content');
        reportContent.innerHTML = test.report;

        // Add styling for the report content if not already added
        if (!document.getElementById('report-style')) {
            const style = document.createElement('style');
            style.id = 'report-style';
            style.textContent = `
                .html-report-content {
                    max-height: 500px;
                    overflow-y: auto;
                    background-color: #f8f9fa;
                }
                .html-report-content a {
                    color: #0d6efd;
                    text-decoration: underline;
                }
                .html-report-content ul {
                    padding-left: 20px;
                    margin-bottom: 1rem;
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Show the details section
    elements.testDetailsSection.classList.remove('d-none');

    // Scroll to make it visible
    elements.testDetailsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
}

/**
 * Calculate duration between two date strings
 * @param {string} startTime - Start time string
 * @param {string} endTime - End time string
 * @returns {string} - Formatted duration string (e.g., "1h 23m 45s")
 */
function calculateDuration(startTime, endTime) {
    if (!startTime || !endTime) return 'N/A';

    try {
        // Create date objects from the strings
        const startDate = new Date(startTime);
        const endDate = new Date(endTime);

        // Check if these are valid dates
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            return 'Invalid date';
        }

        // Calculate the difference in milliseconds
        const diffMs = endDate - startDate;

        // Convert to seconds, minutes, hours
        const seconds = Math.floor((diffMs / 1000) % 60);
        const minutes = Math.floor((diffMs / (1000 * 60)) % 60);
        const hours = Math.floor(diffMs / (1000 * 60 * 60));

        // Format the duration
        if (hours > 0) {
            return `${hours}h ${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        } else {
            return `${seconds}s`;
        }
    } catch (error) {
        console.error(`Error calculating duration: ${error}`);
        return 'Error';
    }
}

// Update the test cases table
function updateTestCasesTable(testCases) {
    const tableBody = document.getElementById('test-cases-table-body'); // Or your actual table body ID
    if (!tableBody) {
        console.error('Test cases table body not found. Cannot update test cases.');
        return;
    }
    tableBody.innerHTML = ''; // Clear existing rows

    if (!testCases || testCases.length === 0) {
        const row = tableBody.insertRow();
        const cell = row.insertCell();
        // Adjust colspan based on the number of columns in your test cases table
        // Common columns: Case ID, Description, Status, Duration, Error Message, Actions
        cell.colSpan = 6;
        cell.textContent = 'No test cases found for this session.';
        cell.style.textAlign = 'center';
        return;
    }

    testCases.forEach(testCase => {
        const row = tableBody.insertRow();

        row.insertCell().textContent = testCase.tc_id || 'N/A';
        row.insertCell().textContent = testCase.test_case_name || testCase.description || '-';

        const statusCell = row.insertCell();
        statusCell.textContent = testCase.status || 'Unknown';
        if (testCase.status === 'Failed') {
            statusCell.style.color = 'red';
            statusCell.style.fontWeight = 'bold';
        } else if (testCase.status === 'Passed') {
            statusCell.style.color = 'green';
        }

        // Assuming duration is part of testCase from backend, otherwise calculate or omit
        row.insertCell().textContent = testCase.duration || '-';
        // Assuming error_message is part of testCase from backend
        row.insertCell().textContent = testCase.error_message || testCase.error || '-';

        const actionsCell = row.insertCell();
        // Check for failed test case with either failure_context or output_data
        if (testCase.status === 'Failed' && (testCase.failure_context || testCase.output_data || testCase.error_message)) {
            const viewDetailsButton = document.createElement('button');
            viewDetailsButton.textContent = 'View Details';
            viewDetailsButton.className = 'btn btn-primary btn-sm'; // Bootstrap styling
            viewDetailsButton.style.marginLeft = '5px';
            viewDetailsButton.onclick = function() {
                const name = typeof testCase.test_case_name === 'string' ? testCase.test_case_name : `Test Case ${testCase.tc_id}`;
                if (testCase.failure_context && testCase.failure_context.length > 0) {
                    displayFailureContextModal(testCase.failure_context, name);
                } else {
                    // For simple output data without context
                    showFailureDetails(testCase);
                }
            };
            actionsCell.appendChild(viewDetailsButton);
        }
    });
}

/**
 * Display a modal with failure context for a test case
 * @param {Array} contextRows - The context rows to display
 * @param {string} testCaseName - The name of the test case
 */
function displayFailureContextModal(contextRows, testCaseName) {
    console.log('Displaying failure context for test case:', testCaseName, contextRows);

    // Create or get the modal for displaying failure context
    let modal = document.getElementById('failure-context-modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'failure-context-modal';
        modal.className = 'modal fade';
        modal.tabIndex = -1;
        modal.role = 'dialog';
        modal.setAttribute('aria-labelledby', 'failureContextModalLabel');
        modal.setAttribute('aria-hidden', 'true');

        modal.innerHTML = `
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="failureContextModalLabel">Failure Context</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <!-- Content will be dynamically inserted here -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    // Update modal title
    const modalTitle = modal.querySelector('.modal-title');
    if (modalTitle) {
        modalTitle.textContent = `Failure Context: ${testCaseName}`;
    }

    // Update modal body with context
    const modalBody = modal.querySelector('.modal-body');
    if (modalBody) {
        // Clear previous content
        modalBody.innerHTML = '';

        // Add each context row
        contextRows.forEach((row, index) => {
            const contextCard = document.createElement('div');
            contextCard.className = 'card mb-3';

            // Mark the failed step with a red border
            if (row.outcome === 'F') {
                contextCard.style.borderColor = '#dc3545';
            }

            // Create header
            const cardHeader = document.createElement('div');
            cardHeader.className = 'card-header d-flex justify-content-between align-items-center';
            cardHeader.innerHTML = `
                <span>Step ${row.seq_index || index + 1}</span>
                <span class="badge ${row.outcome === 'F' ? 'badge-danger' : 'badge-success'}">${row.outcome === 'F' ? 'Failed' : 'Passed'}</span>
            `;
            contextCard.appendChild(cardHeader);

            // Create body
            const cardBody = document.createElement('div');
            cardBody.className = 'card-body';

            // Add input data if available
            if (row.input_data) {
                const inputSection = document.createElement('div');
                inputSection.className = 'mb-3';
                inputSection.innerHTML = `<h6>Input:</h6><pre class="context-input">${escapeHtml(row.input_data)}</pre>`;
                cardBody.appendChild(inputSection);
            }

            // Add output data if available
            if (row.output_data) {
                const outputSection = document.createElement('div');
                outputSection.className = 'mb-3';
                outputSection.innerHTML = `<h6>Output:</h6><pre class="context-output">${escapeHtml(row.output_data)}</pre>`;
                cardBody.appendChild(outputSection);
            }

            contextCard.appendChild(cardBody);
            modalBody.appendChild(contextCard);
        });
    }

    // Show the modal
    $(modal).modal('show');
}

// Utility function to escape HTML to prevent XSS
function escapeHtml(unsafe) {
    if (unsafe === null || typeof unsafe === 'undefined') {
        return '';
    }
    return unsafe
        .toString()
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// Create HTML content for the step details modal
function createStepDetailsModalHtml(testCase) {
    return `
        <div class="modal-header">
            <h5 class="modal-title">Step Details: ${escapeHtml(testCase.tc_id || testCase.id)}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <p><strong>Description:</strong> ${escapeHtml(testCase.description || testCase.name || '-')}</p>
            <p><strong>Status:</strong> ${escapeHtml(testCase.status || 'Unknown')}</p>
            ${testCase.error_message ? `<p><strong>Error Message:</strong> <pre>${escapeHtml(testCase.error_message)}</pre></p>` : ''}
            ${testCase.input_data ? `<p><strong>Input Data:</strong> <pre style="white-space: pre-wrap; word-wrap: break-word;">${escapeHtml(testCase.input_data)}</pre></p>` : ''}
            ${testCase.output_data ? `<p><strong>Output Data:</strong> <pre style="white-space: pre-wrap; word-wrap: break-word;">${escapeHtml(testCase.output_data)}</pre></p>` : ''}
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
    `;
}

// Show the step details modal
function showStepDetailsModal(testCase) {
    let modalElement = document.getElementById('stepDetailsModal');

    if (!modalElement) {
        modalElement = document.createElement('div');
        modalElement.id = 'stepDetailsModal';
        modalElement.className = 'modal fade';
        modalElement.setAttribute('tabindex', '-1');
        modalElement.setAttribute('aria-labelledby', 'stepDetailsModalLabel');
        modalElement.setAttribute('aria-hidden', 'true');

        const modalDialog = document.createElement('div');
        modalDialog.className = 'modal-dialog modal-lg modal-dialog-scrollable'; // Added modal-lg and modal-dialog-scrollable

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';

        modalDialog.appendChild(modalContent);
        modalElement.appendChild(modalDialog);
        document.body.appendChild(modalElement);
    }

    const modalContent = modalElement.querySelector('.modal-content');
    if (modalContent) {
        modalContent.innerHTML = createStepDetailsModalHtml(testCase);
    }

    // Ensure Bootstrap 5 modal instance is correctly created and shown
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
}

// Display failure context modal
function displayFailureContextModal(failureContext, testCaseName) {
    let modalElement = document.getElementById('failureContextModal');

    if (!modalElement) {
        modalElement = document.createElement('div');
        modalElement.id = 'failureContextModal';
        modalElement.className = 'modal fade';
        modalElement.setAttribute('tabindex', '-1');
        modalElement.setAttribute('aria-labelledby', 'failureContextModalLabel');
        modalElement.setAttribute('aria-hidden', 'true');

        const modalDialog = document.createElement('div');
        modalDialog.className = 'modal-dialog modal-lg modal-dialog-scrollable'; // Added modal-lg and modal-dialog-scrollable

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';

        modalDialog.appendChild(modalContent);
        modalElement.appendChild(modalDialog);

        document.body.appendChild(modalElement);
    }

    const modalContent = modalElement.querySelector('.modal-content');
    if (modalContent) {
        modalContent.innerHTML = `
            <div class="modal-header">
                <h5 class="modal-title">Failure Context: ${testCaseName}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <pre style="white-space: pre-wrap; word-wrap: break-word;">${escapeHtml(failureContext)}</pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        `;
    }

    // Ensure Bootstrap 5 modal instance is correctly created and shown
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
}

// Update counters
function updateCounters() {
    try {
        // Skip if no counter elements
        if (!document.getElementById('total-count')) {
            return;
        }

        // Use totalRecordsForActiveTimeRange for the 'Total' count
        // Fallback to current reports length if totalRecordsForActiveTimeRange is 0 (e.g., initial state before fetch)
        const totalCount = currentState.totalRecordsForActiveTimeRange > 0 ?
                           currentState.totalRecordsForActiveTimeRange :
                           (currentState.reports ? currentState.reports.length : 0);

        document.getElementById('total-count').textContent = totalCount;

        // Passed, Failed, Running counts are derived from the currently displayed/cached reports
        let passedCount = 0;
        let failedCount = 0;
        let runningCount = 0;

        (currentState.reports || []).forEach(report => {
            const status = (report.status || '').toLowerCase();
            if (status === 'passed' || status === 'success') {
                passedCount++;
            } else if (status === 'failed') {
                failedCount++;
            } else if (status === 'running' || status === 'queued') {
                runningCount++;
            }
        });

        document.getElementById('passed-count').textContent = passedCount;
        document.getElementById('failed-count').textContent = failedCount;
        document.getElementById('running-count').textContent = runningCount;
    } catch (error) {
        console.error('Error updating counters:', error);
    }
}

/**
 * Update the test cases table with the provided test cases
 * @param {Array} testCases - Array of test cases to display
 */
function updateTestCasesTable(testCases) {
    // Get the table body element
    const tableBody = document.getElementById('test-cases-table-body');
    if (!tableBody) {
        console.error('Test cases table body element not found');
        return;
    }

    // Clear existing content
    tableBody.innerHTML = '';

    // If there are no test cases, display a message
    if (!testCases || testCases.length === 0) {
        const row = document.createElement('tr');
        const cell = document.createElement('td');
        cell.colSpan = 6;
        cell.className = 'text-center';
        cell.textContent = 'No test cases found for this test';
        row.appendChild(cell);
        tableBody.appendChild(row);
        return;
    }

    // Add each test case as a row
    testCases.forEach(testCase => {
        const row = document.createElement('tr');

        // Add Case ID cell
        const idCell = document.createElement('td');
        idCell.textContent = testCase.tc_id || testCase.id || 'N/A';
        row.appendChild(idCell);

        // Add Description cell
        const descCell = document.createElement('td');
        descCell.textContent = testCase.description || testCase.name || 'No description';
        row.appendChild(descCell);

        // Add Status cell with appropriate styling
        const statusCell = document.createElement('td');
        const status = testCase.status || 'Unknown';
        statusCell.textContent = status;

        // Add appropriate class based on status
        if (status.toLowerCase().includes('pass')) {
            statusCell.className = 'text-success';
        } else if (status.toLowerCase().includes('fail')) {
            statusCell.className = 'text-danger';
        } else if (status.toLowerCase().includes('skip')) {
            statusCell.className = 'text-warning';
        }
        row.appendChild(statusCell);

        // Add Duration cell
        const durationCell = document.createElement('td');
        durationCell.textContent = testCase.duration || 'N/A';
        row.appendChild(durationCell);

        // Add Error Message cell
        const errorCell = document.createElement('td');
        errorCell.textContent = testCase.error_message || testCase.error || '';
        if (errorCell.textContent.length > 50) {
            errorCell.textContent = errorCell.textContent.substring(0, 50) + '...';
            errorCell.title = testCase.error_message || testCase.error; // Show full error on hover
        }
        row.appendChild(errorCell);

        // Add Actions cell
        const actionsCell = document.createElement('td');

        // Add a button to view full details if an error exists
        if (testCase.error_message || testCase.error) {
            const viewButton = document.createElement('button');
            viewButton.className = 'btn btn-sm btn-outline-info';
            viewButton.textContent = 'View Error';
            viewButton.addEventListener('click', () => {
                showFailureContext(testCase.description || testCase.name || `Test Case ${testCase.tc_id || testCase.id}`,
                                    testCase.error_message || testCase.error);
            });
            actionsCell.appendChild(viewButton);
        }

        row.appendChild(actionsCell);

        // Add the row to the table
        tableBody.appendChild(row);
    });

    console.log(`Updated test cases table with ${testCases.length} cases`);
}

// Export reports to CSV
function exportReports() {
    // Generate CSV content
    let csvContent = 'data:text/csv;charset=utf-8,';

    // Add headers
    csvContent += 'Test ID,Type,Environment,Status,Start Time,Duration,Pass Rate\n';

    // Add rows
    currentState.reports.forEach(report => {
        const passRate = report.totalCases > 0
            ? Math.round((report.passedCases / report.totalCases) * 100)
            : 0;

        const row = [
            report.id,
            report.type,
            report.environment,
            report.status,
            new Date(report.startTime).toLocaleString(),
            report.duration || 'N/A',
            `${passRate}%`
        ];

        // Escape any commas in the data
        const escapedRow = row.map(field => {
            if (typeof field === 'string' && field.includes(',')) {
                return `"${field}"`;
            }
            return field;
        });

        csvContent += escapedRow.join(',') + '\n';
    });

    // Create download link
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `test_reports_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);

    // Trigger download
    link.click();

    // Clean up
    document.body.removeChild(link);
}

/**
 * Initialize the DataTable for the reports table
 */
function initializeDataTable() {
    try {
        console.log('Initializing DataTable for reports');

        if ($.fn.dataTable.isDataTable('#reports-table')) {
            $('#reports-table').DataTable().destroy();
            console.log('Destroyed existing DataTable instance');
        }

        reportsDataTable = $('#reports-table').DataTable({
            pageLength: 25, // Initial page length
            order: [[0, 'desc']], // Default sort by first column (ID/tsn_id) descending
            responsive: true,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]], // Options for number of records per page
            columnDefs: [
                { type: 'date', targets: [4, 5] }, // 5th and 6th columns are dates (Start/End Time)
                // Duration column (7th column, index 6) is now not orderable (disabled sorting)
                { orderable: false, targets: [6, 10] } // Both Duration and Actions columns are not orderable
            ],
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search reports...",
                lengthMenu: "Show _MENU_ records",
                info: "Showing _START_ to _END_ of _TOTAL_ entries" // _TOTAL_ will be based on client-side data count
            }
        });

        console.log('DataTable initialized successfully');

        // The 'length.dt' event that previously called loadReportsData is REMOVED.
        // DataTable will handle length changes purely on the client-side data.

        setupCustomFilters();

    } catch (error) {
        console.error('Error initializing DataTable:', error);
    }
}

/**
 * Set up custom filtering for the reports table
 */
function setupCustomFilters() {
    try {
        console.log('Setting up custom filters');

        // User filter
        $('#filter-user').on('change', function() {
            const selectedUserId = $(this).val();
            if (!selectedUserId) {
                // Clear filter if nothing selected
                reportsDataTable.column(7).search('').draw();
                console.log('User filter cleared');
            } else {
                // Custom filtering function to match by user ID
                $.fn.dataTable.ext.search.push(
                    function(settings, data, dataIndex) {
                        const row = reportsDataTable.row(dataIndex).node();
                        const rowUserId = $(row).attr('data-user-id');
                        return !selectedUserId || rowUserId === selectedUserId;
                    }
                );
                reportsDataTable.draw();
                // Remove the filter after drawing
                $.fn.dataTable.ext.search.pop();
                console.log('User filter applied:', selectedUserId);
            }
        });

        // Status filter
        $('#filter-status').on('change', function() {
            const value = $(this).val().toLowerCase();
            reportsDataTable.column(3).search(value).draw(); // Column 3 is Status
            console.log('Status filter changed to:', value);
        });

        // Test ID filter
        $('#filter-test-id').on('input', function() {
            const value = $(this).val().trim();
            reportsDataTable.column(2).search(value).draw(); // Column 2 is Test ID
            console.log('Test ID filter changed to:', value);
        });

        // Reset filters button
        $('#reset-filters').on('click', function() {
            // Clear all filter inputs
            $('#filter-user').val('');
            $('#filter-status').val('');
            $('#filter-test-id').val('');

            // Reset DataTable filters
            reportsDataTable.search('').columns().search('').draw();
            console.log('All filters reset');
        });

        console.log('Custom filters set up successfully');
    } catch (error) {
        console.error('Error setting up custom filters:', error);
    }
}

/**
 * Populate the user filter dropdown with unique users from the table
 */
function populateUserFilter() {
    try {
        console.log('Populating user filter dropdown');
        const userFilter = document.getElementById('filter-user');
        if (!userFilter) {
            console.error('User filter dropdown not found');
            return;
        }

        // Clear existing options except the first one (All Users)
        while (userFilter.options.length > 1) {
            userFilter.remove(1);
        }

        // Get all unique users from the reports data
        if (!currentState.reports || !currentState.reports.length) {
            console.log('No reports data available to populate user filter');
            return;
        }

        console.log('Found reports data for user filter:', currentState.reports.length, 'reports');
        if (currentState.reports.length > 0) {
            console.log('First report for user filter:', currentState.reports[0]);
        }

        // Extract unique users: Key by actual user ID (report.uid), value is display name
        const userMap = new Map();
        currentState.reports.forEach(report => {
            const userId = report.uid; // Actual ID, e.g., email
            let userDisplay = report.user_display; // Preferred display name from server

            // If user_display is not provided by the server, format it from uid
            if (!userDisplay && userId) {
                // Assuming formatUserEmail at line 309 (which makes "Vita Lipstein") is preferred for display.
                // If the one at line 1000 (making "vita.lipstein") is preferred, this call might need adjustment
                // or we rely on user_display being consistently present from server.
                // For now, let's use the more sophisticated formatting if user_display is missing.
                const emailFormattingFunction = formatUserEmail; // Relies on correct one being in scope
                userDisplay = emailFormattingFunction(userId);
            } else if (!userId) {
                // Skip if no usable ID
                return;
            }

            // Add to map if we have a valid ID and display name, and it's not already added
            if (userId && userDisplay && userDisplay !== 'Unknown' && !userMap.has(userId)) {
                userMap.set(userId, userDisplay);
            }
        });

        console.log(`Found ${userMap.size} unique users in reports data`);

        // If no users were found, add a default user for debugging (as per existing logic)
        if (userMap.size === 0) {
            console.log('No valid users found in reports data, adding a default user for testing');
            // This default user won't actually filter anything unless data attributes match.
            const defaultOption = document.createElement('option');
            defaultOption.value = "DefaultUser"; // Or some placeholder ID
            defaultOption.textContent = "DefaultUser";
            // userFilter.appendChild(defaultOption); // Keep or remove based on desired behavior
        }

        // Convert to array and sort by display name
        const sortedUsers = Array.from(userMap.entries())
            .map(([id, display]) => ({ id, display })) // id is userId (email), display is userDisplay
            .sort((a, b) => a.display.localeCompare(b.display));

        // Add options to the dropdown
        sortedUsers.forEach(user => {
            const option = document.createElement('option');
            option.value = user.id; // Use the actual ID (email) for the value attribute
            option.textContent = user.display; // Use the formatted name for the display text
            userFilter.appendChild(option);
        });

        console.log(`User filter populated with ${sortedUsers.length} users`);
    } catch (error) {
        console.error('Error populating user filter:', error);
    }
}

// Document ready function to initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Set up refresh button event listener
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshReports);
    }

    // Initialize timezone selector
    const timezoneSelector = document.getElementById('timezone-select');
    if (timezoneSelector) {
        const savedTimezone = localStorage.getItem('selected_timezone');
        if (savedTimezone) {
            timezoneSelector.value = savedTimezone;
        }
        timezoneSelector.addEventListener('change', function() {
            saveSelectedTimezone(this.value);
            refreshAllTimestamps();
        });
        console.log('Timezone selector initialized with timezone:', getSelectedTimezone());
    } else {
        console.warn('Timezone selector not found');
    }

    // Initialize filters (including time range event listeners setup by setupEventListeners)
    initializeFilters(); // This should be called before setupEventListeners if it also sets up parts of filters
    setupEventListeners(); // Sets up timeRangeDropdown listeners etc.

    // Initialize charts
    initializeCharts();

    // Initial refresh of timestamps to ensure they're formatted with the correct timezone
    // This should be called AFTER data is potentially loaded if it formats data in the table
    // refreshAllTimestamps();

    // Load initial data for the default time range (e.g., currentState.activeTimeRange which is '30d')
    // DataTable will be initialized after data is loaded and displayReports is called.
    loadReportsData({ forceFullReload: true, showLoading: true });

    // Call refreshAllTimestamps after the initial data load and table population might be better.
    // Or ensure formatDateTime is robust enough to be called on empty/loading table.
});

/**
 * Refresh all timestamps on the page to reflect the selected timezone
 */
function refreshAllTimestamps() {
    console.log('Refreshing all timestamps with timezone:', getSelectedTimezone());

    try {
        // Update timestamps in the reports table
        const reportRows = document.querySelectorAll('#reports-table tbody tr');

        reportRows.forEach(row => {
            // Find the start time and end time cells (columns 4 and 5)
            const startTimeCell = row.cells[4]; // 5th column (0-indexed)
            const endTimeCell = row.cells[5];   // 6th column (0-indexed)

            if (startTimeCell) {
                // Get the original timestamp if we stored it as a data attribute
                const originalStartTime = startTimeCell.dataset.originalTime || startTimeCell.textContent;
                if (originalStartTime && originalStartTime !== '-' && originalStartTime !== 'Not completed') {
                    // Store the original value if we haven't already
                    if (!startTimeCell.dataset.originalTime) {
                        startTimeCell.dataset.originalTime = originalStartTime;
                    }
                    // Format according to the selected timezone
                    startTimeCell.textContent = formatDateTime(originalStartTime);
                }
            }

            if (endTimeCell) {
                // Only update if not "Not completed"
                if (endTimeCell.textContent !== 'Not completed') {
                    const originalEndTime = endTimeCell.dataset.originalTime || endTimeCell.textContent;
                    if (originalEndTime && originalEndTime !== '-') {
                        // Store the original value if we haven't already
                        if (!endTimeCell.dataset.originalTime) {
                            endTimeCell.dataset.originalTime = originalEndTime;
                        }
                        // Format according to the selected timezone
                        endTimeCell.textContent = formatDateTime(originalEndTime);
                    }
                }
            }
        });

        // Update timestamps in test details if open
        const detailStartTime = document.getElementById('detail-start-time');
        if (detailStartTime && detailStartTime.dataset.originalTime) {
            detailStartTime.textContent = formatDateTime(detailStartTime.dataset.originalTime);
        }

        // Update the 'Last updated' timestamp in the refresh status div
        const refreshStatus = document.getElementById('refresh-status');
        if (refreshStatus && refreshStatus.dataset.originalTime) {
            const originalTime = new Date(refreshStatus.dataset.originalTime);
            const selectedTimezone = getSelectedTimezone();
            const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };

            // Add timezone option if not local
            if (selectedTimezone !== 'local') {
                timeOptions.timeZone = selectedTimezone;
            }

            refreshStatus.textContent = `Last updated: ${originalTime.toLocaleTimeString('en-US', timeOptions)}`;
        }
    } catch (error) {
        console.error('Error refreshing timestamps:', error);
    }
}

// Rerun failed tests from a specific test run
async function rerunFailedTests(testId) {
    try {
        console.log(`Rerunning failed tests for test ${testId}...`);

        // Get credentials from window.apiService or use default test credentials
        let credentials = { uid: '<EMAIL>', password: 'test' };

        // Try to get credentials from the API service if available
        if (window.apiService && window.apiService.credentials) {
            credentials = window.apiService.credentials;
            console.log('Using credentials from API service');
        } else {
            // Try to get credentials from session storage as fallback
            const sessionUid = sessionStorage.getItem('smarttest_uid');
            const sessionPwd = sessionStorage.getItem('smarttest_pwd');
            if (sessionUid && sessionPwd) {
                credentials = { uid: sessionUid, password: sessionPwd };
                console.log('Using credentials from session storage');
            } else {
                console.log('Using default test credentials');
            }
        }

        // Check if we have access to the dashboard API integration
        if (window.parent && window.parent.dashboardApiIntegration) {
            // Use the parent window's dashboard API integration
            window.parent.dashboardApiIntegration.rerunFailedTests(testId)
                .then(newTestId => {
                    if (newTestId) {
                        // Redirect to the dashboard to view the new test run
                        window.location.href = `../dashboard/index.html?testId=${newTestId}`;
                    }
                })
                .catch(error => {
                    console.error('Error rerunning failed tests:', error);
                    alert(`Failed to rerun tests: ${error.message}`);
                });
        } else {
            // If we don't have access to the dashboard API integration,
            // use the API directly

            // Construct the URL for the rerun failed endpoint
            const url = new URL('/api/rerun-failed', window.location.origin);
            url.searchParams.append('uid', credentials.uid);
            url.searchParams.append('password', credentials.password);
            url.searchParams.append('tsn_id', testId);

            // Call the API to rerun failed tests
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ tsn_id: testId })
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Failed to rerun tests: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            console.log('Rerun failed tests response:', data);

            // Check for success in the refactored API response format
            if (!data.success) throw new Error(data.message || 'API error');

            // Extract the new test ID from the response
            const newTestId = data.data?.tsn_id;

            if (newTestId) {
                // Redirect to the dashboard to view the new test run
                window.location.href = `../dashboard/index.html?testId=${newTestId}`;
            } else {
                // If no new test ID is returned, redirect to the dashboard with a parameter to rerun the tests
                window.location.href = `../dashboard/index.html?rerunFailed=${testId}`;
            }
        }
    } catch (error) {
        console.error('Error rerunning failed tests:', error);
        alert(`Failed to rerun tests: ${error.message}`);
    }
}

// Utility function to escape HTML to prevent XSS
function escapeHtml(unsafe) {
    if (unsafe === null || typeof unsafe === 'undefined') return '';
    return String(unsafe)
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}