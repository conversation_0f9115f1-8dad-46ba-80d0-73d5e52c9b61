<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Automation Dashboard</title>
    <!-- Replace Bootstrap with Fluent UI -->
    <link href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css" rel="stylesheet">
    <link href="https://res-1.cdn.office.net/files/fabric-cdn-prod_20230815.002/office-ui-fabric-core/11.0.0/css/fabric.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <!-- Load shared services in the correct order -->
    <script src="../shared/services/base-api-service.js"></script>
    <script src="../shared/services/unified-api-service.js"></script>
    <script src="../shared/services/external-api-service.js"></script>
    <!-- Load module-specific API service wrapper -->
    <script src="api-service.js"></script>
</head>
<body class="ms-Fabric">
    <!-- Login Modal for API Credentials -->
    <div id="login-modal" class="ms-modal active">
        <div class="ms-modal-content">
            <h2 class="ms-font-xl">Login to Test System</h2>
            <form id="login-form">
                <div class="ms-form-group">
                    <label class="ms-Label">Username</label>
                    <input class="ms-TextField-field" type="text" id="username" placeholder="Enter your username" required>
                </div>
                <div class="ms-form-group">
                    <label class="ms-Label">Password</label>
                    <input class="ms-TextField-field" type="password" id="password" placeholder="Enter your password" required>
                </div>
                <div class="ms-form-actions">
                    <button type="submit" class="ms-Button ms-Button--primary">
                        <span class="ms-Button-label">Login</span>
                    </button>
                    <div class="login-status" id="login-status" style="margin-top: 10px; color: #d13438; display: none;">
                        Invalid credentials. Please try again.
                    </div>
                </div>
            </form>
        </div>
    </div>

    <header class="ms-header">
        <div class="ms-header-title">
            <a class="ms-header-brand" href="#">Test Automation Framework</a>
        </div>
        <div class="ms-header-controls">
            <span class="ms-environment-display" id="environment-display">Environment: Development</span>
            <span class="ms-user-info" id="user-display">Not logged in</span>
            <button class="ms-Button ms-Button--default" id="login-button">
                <span class="ms-Button-label">Login</span>
            </button>
            <button class="ms-Button ms-Button--default" id="logout-button" style="display: none;">
                <span class="ms-Button-label">Logout</span>
            </button>
        </div>
    </header>

    <div class="ms-container">
        <div class="ms-layout">
            <nav id="sidebarMenu" class="ms-nav">
                <div class="ms-nav-content">
                    <ul class="ms-nav-list">
                        <li class="ms-nav-item">
                            <a class="ms-nav-link ms-nav-link-active" href="#">
                                Dashboard
                            </a>
                        </li>
                        <li class="ms-nav-item">
                            <a class="ms-nav-link" href="/config/index.html">
                                Configuration
                            </a>
                        </li>
                        <li class="ms-nav-item">
                            <a class="ms-nav-link" href="/reports/index.html">
                                Reports
                            </a>
                        </li>
                        <li class="ms-nav-item">
                            <a class="ms-nav-link" href="/config/api-explorer.html" target="_blank">
                                API Explorer
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="ms-content">
                <div class="ms-content-header">
                    <h1 class="ms-font-xxl">Dashboard</h1>
                    <div class="ms-content-actions">
                        <button type="button" class="ms-Button ms-Button--default" id="refresh-btn">
                            <span class="ms-Button-label">Refresh</span>
                        </button>
                    </div>
                </div>

                <div class="ms-grid">
                    <div class="ms-grid-row">
                        <div class="ms-grid-col ms-sm3">
                            <div class="ms-stat-card ms-bgColor-themePrimary">
                                <div class="ms-stat-title ms-fontColor-white">Total Tests</div>
                                <div class="ms-stat-value ms-fontColor-white" id="total-tests">0</div>
                            </div>
                        </div>
                        <div class="ms-grid-col ms-sm3">
                            <div class="ms-stat-card ms-bgColor-green">
                                <div class="ms-stat-title ms-fontColor-white">Passed</div>
                                <div class="ms-stat-value ms-fontColor-white" id="successful-tests">0</div>
                            </div>
                        </div>
                        <div class="ms-grid-col ms-sm3">
                            <div class="ms-stat-card ms-bgColor-red">
                                <div class="ms-stat-title ms-fontColor-white">Failed</div>
                                <div class="ms-stat-value ms-fontColor-white" id="failed-tests">0</div>
                            </div>
                        </div>
                        <div class="ms-grid-col ms-sm3">
                            <div class="ms-stat-card ms-bgColor-orange">
                                <div class="ms-stat-title ms-fontColor-white">Skipped</div>
                                <div class="ms-stat-value ms-fontColor-white" id="running-tests">0</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Active Tests Section -->
                <div class="ms-section-header">
                    <h2 class="ms-font-xl ms-section-title">Active Tests</h2>
                    <div class="active-tests-filter">
                        <button class="ms-Button ms-Button--default active-tests-filter-btn active" data-filter="all">
                            <span class="ms-Button-label">All Tests</span>
                        </button>
                        <button class="ms-Button ms-Button--default active-tests-filter-btn" data-filter="mine">
                            <span class="ms-Button-label">My Tests</span>
                        </button>
                        <button class="ms-Button ms-Button--default active-tests-filter-btn" data-filter="others">
                            <span class="ms-Button-label">Others' Tests</span>
                        </button>
                    </div>
                </div>
                <div id="active-tests-container" class="ms-card-container">
                    <!-- Active tests will be populated here -->
                    <div class="ms-empty-message">No active tests</div>
                </div>

                <!-- Predefined Test Suites Section -->
                <h2 class="ms-font-xl ms-section-title">Predefined Test Suites</h2>
                <!-- Custom Test Case Runner -->
                <div class="ms-custom-run-container" style="display: flex; align-items: center; margin-bottom: 16px;">
                    <input type="number" id="custom-tc-id-input" class="ms-TextField-field" placeholder="Enter Test Case ID" style="width: 150px; margin-right: 8px;">
                    <button id="run-custom-tc-button" class="ms-Button ms-Button--primary">
                        <span class="ms-Button-label">Run Test Case</span>
                    </button>
                </div>
                <div id="predefined-suites-container" class="ms-card-container">
                    <div class="ms-predefined-grid">
                        <div class="ms-predefined-card">
                            <h3 class="ms-font-l">Smoke Test</h3>
                            <p>Quick critical path verification for core system functions</p>
                            <button class="ms-Button ms-Button--primary" data-suite-id="322" data-suite-name="DEMO PE2.1 Smoke Test">
                                <span class="ms-Button-label">Run Smoke Test</span>
                            </button>
                        </div>
                        <div class="ms-predefined-card">
                            <h3 class="ms-font-l">Heartbeat Test</h3>
                            <p>Continuous monitoring tests to verify system availability</p>
                            <button class="ms-Button ms-Button--primary" data-suite-id="323" data-suite-name="DEMO PE2.1 Heartbeat Test">
                                <span class="ms-Button-label">Run Heartbeat Test</span>
                            </button>
                        </div>
                        <div class="ms-predefined-card">
                            <h3 class="ms-font-l">Sanity Test</h3>
                            <p>In-depth validation of key business and processing flows</p>
                            <button class="ms-Button ms-Button--primary" data-suite-id="312" data-suite-name="DEMO PE2.1 Sanity Test">
                                <span class="ms-Button-label">Run Sanity Test</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Custom Test Suite Builder -->
                <h2 class="ms-font-xl ms-section-title">Custom Test Suite Builder</h2>
                <div class="ms-card">
                    <div class="ms-card-content">
                        <div class="ms-form-group">
                            <label class="ms-Label">Suite Name</label>
                            <input class="ms-TextField-field" type="text" id="custom-suite-name" placeholder="Enter a name for your custom suite">
                        </div>
                        <div class="ms-form-group">
                            <label class="ms-Label">Select Test Cases</label>
                            <div id="available-testcases" class="ms-checkbox-container">
                                <!-- Test cases will be loaded here dynamically -->
                                <div class="ms-empty-message">Loading test cases...</div>
                            </div>
                        </div>
                        <div class="ms-form-actions">
                            <button id="create-custom-suite" class="ms-Button ms-Button--primary">
                                <span class="ms-Button-label">Create & Run Custom Suite</span>
                            </button>
                        </div>
                    </div>
                </div>

                <h2 class="ms-font-xl ms-section-title">Recent Test Executions</h2>
                <div class="ms-table-container">
                    <table class="ms-Table ms-Table--fixed" id="reports-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Test Name</th>
                                <th>Test ID</th>
                                <th>Status</th>
                                <th>Start Time</th>
                                <th>End Time</th>
                                <th class="text-success">Passed</th>
                                <th class="text-danger">Failed</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Test data will be populated here -->
                        </tbody>
                    </table>
                </div>

                <h2 class="ms-font-xl ms-section-title">Test Results Overview</h2>
                <div class="ms-grid">
                    <div class="ms-grid-row">
                        <div class="ms-grid-col ms-sm6">
                            <canvas id="test-results-chart"></canvas>
                        </div>
                        <div class="ms-grid-col ms-sm6">
                            <canvas id="test-duration-chart"></canvas>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Test Report Modal -->
    <div id="report-modal" class="ms-modal">
        <div class="ms-modal-content">
            <span class="ms-modal-close">&times;</span>
            <h2 class="ms-font-xl">Test Report</h2>
            <div id="report-content">
                <!-- Report content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="ms-loading-overlay" style="display: none;">
        <div class="ms-Spinner ms-Spinner--large"></div>
        <div class="ms-loading-message">Loading...</div>
    </div>

    <!-- Notification Container -->
    <div id="notification-container" class="ms-notification-container"></div>

    <!-- Dashboard utilities - Load in correct order with fallbacks -->
    <script>
        // Global error handler for script loading issues
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('Script error:', { message, source, lineno, colno });
            if (source && source.includes('api-service.js')) {
                console.error('API Service failed to load correctly');
                alert('API Service could not be loaded correctly. Try refreshing the page.');
            }
            return false; // Allow default error handling as well
        };
    </script>

    <!-- Load scripts in proper order -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="notifications.js"></script>
    <script>
        // Fallback for notifications
        if (typeof showNotification !== 'function') {
            window.showNotification = function(title, message, type) {
                console.log(`Notification: ${title} - ${message} (${type})`);
                alert(`${title}: ${message}`);
            };
            console.log('Using fallback notification system');
        }
    </script>

    <script src="api-integration.js"></script>
    <script src="dashboard.js"></script>

    <script>
        // Ensure Unified API Service is initialized
        document.addEventListener('DOMContentLoaded', function() {
            if (!window.apiService) {
                console.error('Unified API Service not found! Make sure api-service.js is loaded correctly.');
                showNotification('Error', 'Unified API Service not found. Please refresh the page.', 'error');
            } else {
                console.log('Unified API Service is available');
            }
        });
    </script>

    <script>
        // Main initialization function
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, starting initialization');

            // Initialize UI elements
            const loginForm = document.getElementById('login-form');
            const loginModal = document.getElementById('login-modal');
            const userDisplay = document.getElementById('user-display');
            const logoutButton = document.getElementById('logout-button');

            console.log('Login elements:', {
                loginForm: !!loginForm,
                loginModal: !!loginModal,
                userDisplay: !!userDisplay,
                logoutButton: !!logoutButton
            });

            // Safe initialization of API integration
            if (typeof DashboardApiIntegration !== 'undefined') {
                if (!window.dashboardApiIntegration) {
                    try {
                        window.dashboardApiIntegration = new DashboardApiIntegration();
                        console.log('Dashboard API Integration created successfully');
                    } catch (e) {
                        console.error('Error creating DashboardApiIntegration:', e);
                        showNotification('Error', 'Failed to initialize dashboard integration', 'error');
                    }
                }
            } else {
                console.error('DashboardApiIntegration class not found!');
                showNotification('Error', 'Dashboard API Integration not available', 'error');
            }

            // Handle existing credentials and login state
            if (window.apiService) {
                const isLoggedIn = window.apiService.loadCredentials();

                if (isLoggedIn) {
                    console.log('User is logged in, initializing dashboard');
                    userDisplay.textContent = `Logged in as: ${window.apiService.credentials.uid}`;
                    logoutButton.style.display = 'inline-block';
                    loginModal.style.display = 'none';
                    loginModal.classList.remove('active');

                    // Initialize the dashboard
                    if (window.dashboardApiIntegration) {
                        try {
                            window.dashboardApiIntegration.initialize();
                        } catch (e) {
                            console.error('Error initializing dashboard:', e);
                        }
                    }
                } else {
                    console.log('User is not logged in, showing login modal');
                    userDisplay.textContent = 'Not logged in';
                    logoutButton.style.display = 'none';
                    loginModal.style.display = 'block';
                    loginModal.classList.add('active');
                }
            } else {
                console.error('API Service not available for login state check');
                showNotification('Error', 'API Service not available', 'error');
            }

            // Login form submission
            loginForm.addEventListener('submit', function(event) {
                event.preventDefault();

                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                console.log('Login attempt with username:', username);

                if (!username || !password) {
                    showNotification('Error', 'Username and password are required', 'error');
                    return;
                }

                // Save credentials
                if (window.apiService) {
                    window.apiService.setCredentials(username, password);

                    // Update UI
                    userDisplay.textContent = `Logged in as: ${username}`;
                    logoutButton.style.display = 'inline-block';
                    loginModal.style.display = 'none';
                    loginModal.classList.remove('active');

                    // Initialize dashboard
                    if (!window.dashboardApiIntegration) {
                        window.dashboardApiIntegration = new DashboardApiIntegration();
                    }
                    window.dashboardApiIntegration.initialize();

                    console.log('Login successful, dashboard initialized');
                } else {
                    console.error('Unified API Service not available for login');
                    showNotification('Error', 'Unified API Service not available', 'error');
                }
            });

            // Logout button
            logoutButton.addEventListener('click', function() {
                // Clear credentials
                sessionStorage.removeItem('smarttest_uid');
                sessionStorage.removeItem('smarttest_pwd');

                if (window.apiService) {
                    window.apiService.credentials = { uid: '', password: '' };
                }

                // Update UI
                userDisplay.textContent = 'Not logged in';
                logoutButton.style.display = 'none';
                loginModal.style.display = 'block';
                loginModal.classList.add('active');

                console.log('Logged out successfully');
            });

            // Setup modal close buttons
            const reportModal = document.getElementById('report-modal');
            const reportModalClose = reportModal.querySelector('.ms-modal-close');

            reportModalClose.addEventListener('click', function(event) {
                event.stopPropagation();
                reportModal.style.display = 'none';
            });

            // Close modals when clicking outside
            window.addEventListener('click', function(event) {
                if (event.target === reportModal) {
                    reportModal.style.display = 'none';
                }
            });

            // Setup refresh button
            document.getElementById('refresh-btn').addEventListener('click', function() {
                if (window.dashboardApiIntegration) {
                    window.dashboardApiIntegration.loadDashboardData();
                }
            });

            // Set up the Create & Run Custom Suite button
            const createCustomSuiteBtn = document.getElementById('create-custom-suite');
            if (createCustomSuiteBtn) {
                createCustomSuiteBtn.addEventListener('click', function() {
                    if (window.dashboardApiIntegration) {
                        window.dashboardApiIntegration.createAndRunCustomSuite();
                    } else {
                        window.showNotification('Error', 'API Integration not available', 'error');
                    }
                });
            }
        });
    </script>

    <!-- Direct login form handler with improved API service detection -->
    <script>
        // Handle login form submission
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const loginModal = document.getElementById('login-modal');
            const loginStatus = document.getElementById('login-status');
            const userDisplay = document.getElementById('user-display');
            const logoutButton = document.getElementById('logout-button');
            const loginButton = document.getElementById('login-button');

            // Show login modal when login button is clicked
            if (loginButton) {
                loginButton.addEventListener('click', function() {
                    loginModal.style.display = 'block';
                    loginModal.classList.add('active');
                });
            }

            // Listen for API service ready event
            document.addEventListener('apiservice-ready', function(event) {
                console.log('API service is now ready for use in login handler');
                const apiService = event.detail.apiService;
                // Store in window for backward compatibility
                if (!window.apiService) {
                    window.apiService = apiService;
                }
            });

            // Function to attempt login with API service
            async function attemptLogin(username, password) {
                // First check if API service is available
                if (!window.apiService) {
                    console.warn('API Service not immediately available, waiting briefly...');
                    // Wait a bit and try again
                    return new Promise(resolve => {
                        setTimeout(async () => {
                            if (window.apiService) {
                                console.log('API Service now available, proceeding with login');
                                resolve(await performLogin(username, password));
                            } else {
                                console.error('API Service still not available after waiting');
                                resolve({
                                    success: false,
                                    error: 'API Service not available. Please refresh the page.'
                                });
                            }
                        }, 500);
                    });
                } else {
                    return performLogin(username, password);
                }
            }

            // Function to perform the actual login
            async function performLogin(username, password) {
                window.apiService.setCredentials(username, password);

                try {
                    const response = await window.apiService.getRecentRuns({ limit: 1 });
                    if (response && response.success) {
                        return { success: true };
                    } else {
                        return { 
                            success: false, 
                            error: 'Invalid credentials. Please try again.' 
                        };
                    }
                } catch (error) {
                    console.error('Error during login:', error);
                    return { 
                        success: false, 
                        error: 'Error connecting to server. Please try again.' 
                    };
                }
            }

            // Handle login form submission
            if (loginForm) {
                loginForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const username = document.getElementById('username').value;
                    const password = document.getElementById('password').value;

                    if (!username || !password) {
                        loginStatus.textContent = 'Please enter username and password';
                        loginStatus.style.display = 'block';
                        return;
                    }

                    console.log('Attempting to log in with:', username);
                    loginStatus.textContent = 'Logging in...';
                    loginStatus.style.display = 'block';

                    // Try to login with retry
                    const result = await attemptLogin(username, password);
                    
                    if (result.success) {
                        console.log('Login successful');
                        loginStatus.style.display = 'none';
                        userDisplay.textContent = `Logged in as: ${username}`;
                        logoutButton.style.display = 'inline-block';
                        loginButton.style.display = 'none';
                        loginModal.style.display = 'none';
                        loginModal.classList.remove('active');

                        // Force reload dashboard with real data
                        if (window.dashboardApiIntegration) {
                            window.dashboardApiIntegration.initialize();
                        } else {
                            // Refresh page as fallback
                            window.location.reload();
                        }
                    } else {
                        console.error('Login failed:', result.error);
                        loginStatus.textContent = result.error;
                        loginStatus.style.display = 'block';
                    }
                });
            }
        });
    </script>
</body>
</html>