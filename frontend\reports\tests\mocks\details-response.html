<!DOCTYPE html>
<html>
<head>
    <title>Test Report Details</title>
    <style>
        .P { color: green; }
        .F { color: red; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Test Report Details</h1>
    <h2>Test Session: <span>13782</span></h2>
    
    <table>
        <tr>
            <th>TC ID</th>
            <th>Seq</th>
            <th>Outcome</th>
            <th>Description</th>
            <th>Input/Output</th>
        </tr>
        <tr>
            <td>3180</td>
            <td>1</td>
            <td><a class="P" href="javascript:void(0);">P</a></td>
            <td>Login to the system</td>
            <td>Input: username=test, password=test
Output: Login successful</td>
        </tr>
        <tr>
            <td>3181</td>
            <td>2</td>
            <td><a class="F" href="javascript:void(0);">F</a></td>
            <td>Verify balance</td>
            <td>Input: account=123
Output: Error: Account not found</td>
        </tr>
        <tr>
            <td>3182</td>
            <td>3</td>
            <td><a class="P" href="javascript:void(0);">P</a></td>
            <td>Logout from the system</td>
            <td>Input: None
Output: Logout successful</td>
        </tr>
    </table>
    
    <div class="pagination">
        Page 1 of 2
    </div>
</body>
</html>
