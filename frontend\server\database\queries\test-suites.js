/**
 * Test Suite Queries
 * Provides functions for querying test suites
 */
const QueryBuilder = require('../utils/query-builder');
const formatter = require('../utils/result-formatter');

/**
 * Get test suites with optional filtering
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Test suites
 */
async function getTestSuites(connection, filters = {}) {
  const { name, status, uid, limit = 100 } = filters;

  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Base query - Using the verified test_suite table
  queryBuilder.select('test_suite ts', [
    'ts.ts_id',
    'ts.uid',
    'ts.status',
    'ts.tp_id',
    'ts.comments',
    'ts.name'
  ]);

  // Filter by name if provided
  if (name) {
    queryBuilder.where('ts.name', 'LIKE', `%${name}%`);
  }

  // Filter by status if provided
  if (status) {
    queryBuilder.where('ts.status', '=', status);
  }

  // Filter by user ID if provided
  if (uid) {
    queryBuilder.where('ts.uid', '=', uid);
  }

  // Add ordering and limit
  queryBuilder.orderBy('ts.ts_id', 'DESC');
  queryBuilder.limit(limit);

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    const rows = await connection.query(sql, params);
    return formatter.formatTestSuites(rows);
  } catch (error) {
    console.error('Error executing getTestSuites query:', error);

    // Try a simpler fallback query if the main query fails
    try {
      console.log('Attempting fallback query for test suites');
      const fallbackSql = `
        SELECT ts_id, uid, status, tp_id, comments, name
        FROM test_suite
        ORDER BY ts_id DESC
        LIMIT ?
      `;
      const fallbackRows = await connection.query(fallbackSql, [limit]);
      return formatter.formatTestSuites(fallbackRows);
    } catch (fallbackError) {
      console.error('Fallback query also failed:', fallbackError);
      return [];
    }
  }
}

/**
 * Get a test suite by ID
 * @param {Object} connection - Database connection
 * @param {number|string} ts_id - Test suite ID
 * @returns {Promise<Object>} - Test suite
 */
async function getTestSuiteById(connection, ts_id) {
  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Build query - Using the verified test_suite table
  queryBuilder.select('test_suite', [
    'ts_id',
    'uid',
    'status',
    'tp_id',
    'comments',
    'name'
  ]);
  queryBuilder.where('ts_id', '=', ts_id);

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    const rows = await connection.query(sql, params);

    if (rows.length === 0) {
      throw new Error(`Test suite with ID ${ts_id} not found`);
    }

    return formatter.formatTestSuites(rows)[0];
  } catch (error) {
    console.error(`Error getting test suite by ID ${ts_id}:`, error);
    throw error;
  }
}

/**
 * Get test cases in a test suite
 * @param {Object} connection - Database connection
 * @param {number|string} ts_id - Test suite ID
 * @returns {Promise<Object>} - Test suite with test cases
 */
async function getTestSuiteInfo(connection, ts_id) {
  try {
    // Get test suite metadata from test_suite table
    const testSuite = await getTestSuiteById(connection, ts_id);

    // Query for test cases in the suite from test_case_group table
    const queryBuilder = new QueryBuilder();
    queryBuilder.select('test_case_group', ['tc_id', 'seq_index']);
    queryBuilder.where('ts_id', '=', ts_id);
    queryBuilder.orderBy('seq_index', 'ASC');

    const { sql, params } = queryBuilder.build();
    const testCases = await connection.query(sql, params);

    // Prepare the result
    return {
      ...testSuite,
      testCaseCount: testCases.length,
      testCases: testCases.map(row => ({
        tc_id: row.tc_id || row.column1,
        seq_index: row.seq_index || row.column2
      }))
    };
  } catch (error) {
    console.error(`Error getting test suite info for ID ${ts_id}:`, error);
    throw error;
  }
}

module.exports = {
  getTestSuites,
  getTestSuiteById,
  getTestSuiteInfo
};
