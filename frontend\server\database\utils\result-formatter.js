/**
 * Result Formatting Utilities
 * Provides functions for formatting database query results
 */

/**
 * Format test case results
 * @param {Array} rows - Raw database rows
 * @returns {Array} - Formatted test cases
 */
function formatTestCases(rows) {
  if (!rows || rows.length === 0) {
    return [];
  }

  return rows.map(row => ({
    tc_id: row.tc_id || row.column1,
    uid: row.uid,
    status: row.status,
    case_driver: row.case_driver,
    tp_id: row.tp_id,
    comments: row.comments,
    tickets: row.tickets,
    name: row.name
  }));
}

/**
 * Format test suite results
 * @param {Array} rows - Raw database rows
 * @returns {Array} - Formatted test suites
 */
function formatTestSuites(rows) {
  if (!rows || rows.length === 0) {
    return [];
  }

  return rows.map(row => ({
    ts_id: row.ts_id,
    name: row.name,
    status: row.status,
    uid: row.uid,
    tp_id: row.tp_id,
    comments: row.comments
  }));
}

/**
 * Format active tests results
 * @param {Array} rows - Raw database rows
 * @param {string} currentUser - Current user ID
 * @returns {Array} - Formatted active tests
 */
function formatActiveTests(rows, currentUser) {
  if (!rows || rows.length === 0) {
    return [];
  }

  return rows.map(row => ({
    tsn_id: row.tsn_id,
    tc_id: row.tc_id,
    initiator_user: row.initiator_user || row.uid,
    creation_time: row.creation_time || row.start_ts,
    is_current_user: row.is_current_user || (row.uid === currentUser)
  }));
}

/**
 * Format test results
 * @param {Array} rows - Raw database rows
 * @returns {Object} - Formatted test results
 */
function formatTestResults(rows) {
  if (!rows || rows.length === 0) {
    return {
      tsn_id: null,
      total_results: 0,
      pass_count: 0,
      fail_count: 0,
      start_time: null,
      end_time: null,
      duration: '0:00',
      results: []
    };
  }

  // Group results by test case
  const resultsByTestCase = {};

  rows.forEach(row => {
    const tc_id = row.tc_id;

    if (!resultsByTestCase[tc_id]) {
      resultsByTestCase[tc_id] = {
        tc_id,
        total_steps: 0,
        passed_steps: 0,
        failed_steps: 0,
        steps: []
      };
    }

    resultsByTestCase[tc_id].total_steps++;

    if (row.outcome === 'P') {
      resultsByTestCase[tc_id].passed_steps++;
    } else if (row.outcome === 'F') {
      resultsByTestCase[tc_id].failed_steps++;
    }

    resultsByTestCase[tc_id].steps.push({
      seq_index: row.seq_index,
      outcome: row.outcome,
      creation_time: row.creation_time,
      output: row.txt
    });
  });

  // Calculate summary
  const tsn_id = rows[0].tsn_id;
  const total_results = rows.length;
  const pass_count = rows.filter(row => row.outcome === 'P').length;
  const fail_count = rows.filter(row => row.outcome === 'F').length;
  const start_time = new Date(Math.min(...rows.map(row => new Date(row.creation_time))));
  const end_time = new Date(Math.max(...rows.map(row => new Date(row.creation_time))));

  // Calculate duration
  const durationMs = end_time - start_time;
  const minutes = Math.floor(durationMs / 60000);
  const seconds = Math.floor((durationMs % 60000) / 1000);
  const duration = `${minutes}:${seconds.toString().padStart(2, '0')}`;

  return {
    tsn_id,
    total_results,
    pass_count,
    fail_count,
    start_time,
    end_time,
    duration,
    results: Object.values(resultsByTestCase)
  };
}

/**
 * Calculate duration between two timestamps
 * @param {string|Date} start - Start timestamp
 * @param {string|Date} end - End timestamp
 * @returns {string} - Formatted duration
 */
function calculateDuration(start, end) {
  if (!start || !end) {
    return '0:00';
  }

  const startTime = start instanceof Date ? start : new Date(start);
  const endTime = end instanceof Date ? end : new Date(end);

  if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
    return '0:00';
  }

  const durationMs = endTime - startTime;
  const minutes = Math.floor(durationMs / 60000);
  const seconds = Math.floor((durationMs % 60000) / 1000);

  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

module.exports = {
  formatTestCases,
  formatTestSuites,
  formatActiveTests,
  formatTestResults,
  calculateDuration
};
