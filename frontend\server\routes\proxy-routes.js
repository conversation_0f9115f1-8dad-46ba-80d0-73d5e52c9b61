/**
 * Proxy Routes
 */
const express = require('express');
const router = express.Router();
const proxyToMptsr = require('../middleware/proxy');
const { getTestStatus } = require('../services/test-status');
const { stopTest } = require('../services/stop-test');
const { validateCredentials } = require('../middleware/auth');

// Test Status API endpoint - uses ReportSummary with cookie auth
router.get('/test-status', validateCredentials, async (req, res, next) => {
  try {
    console.log('GET /api/test-status');
    // Make a copy of query params to avoid consuming them multiple times
    const params = {...req.query};

    // Extract the test session ID and credentials
    const { tsn_id, uid, password } = params;

    try {
      // Get test status using the test-status service
      const status = await getTestStatus(tsn_id, uid, password);

      // Return the status
      return res.json({
        success: true,
        ...status
      });
    } catch (error) {
      console.error(`[/api/test-status] Error getting test status:`, error);
      return res.status(500).json({
        success: false,
        message: `Failed to get test status: ${error.message}`
      });
    }
  } catch (error) {
    console.error('Error in test-status endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Stop Test API endpoint - uses RemoveSession with cookie auth
router.post('/stop-test', validateCredentials, async (req, res, next) => {
  try {
    console.log('POST /api/stop-test');
    // Make a copy of request body to avoid consuming it multiple times
    const params = {...req.body};

    // Extract the test session ID and credentials
    const { tsn_id, uid, password } = params;

    try {
      // Stop the test using the stop-test service
      const result = await stopTest(tsn_id, uid, password);

      // Return the result
      return res.json({
        success: true,
        ...result
      });
    } catch (error) {
      console.error(`[/api/stop-test] Error stopping test:`, error);
      return res.status(500).json({
        success: false,
        message: `Failed to stop test: ${error.message}`
      });
    }
  } catch (error) {
    console.error('Error in stop-test endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Login Proxy API endpoint - uses Login with cookie auth
router.post('/login-proxy', async (req, res, next) => {
  try {
    console.log('POST /api/login-proxy');
    // Make a copy of request body to avoid consuming it multiple times
    const params = {...req.body};

    // Extract the credentials
    const { uid, password } = params;

    if (!uid || !password) {
      return res.status(400).json({
        success: false,
        message: 'Missing credentials (uid/password) required for authentication'
      });
    }

    try {
      // Get a valid JSESSIONID cookie using the cookie-auth service
      const { getJsessionId } = require('../services/cookie-auth');
      const jsessionId = await getJsessionId(uid, password);

      // Set the cookie in the response
      res.cookie('JSESSIONID', jsessionId, {
        path: '/',
        httpOnly: true,
        sameSite: 'lax',
        maxAge: 30 * 60 * 1000 // 30 minutes
      });

      // Return success
      return res.json({
        success: true,
        message: 'Login successful'
      });
    } catch (error) {
      console.error(`[/api/login-proxy] Error logging in:`, error);
      return res.status(401).json({
        success: false,
        message: `Login failed: ${error.message}`
      });
    }
  } catch (error) {
    console.error('Error in login-proxy endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Apply the proxy middleware to all other routes
router.all('*', (req, res, next) => {
  return proxyToMptsr(req, res, next);
});

module.exports = router;
