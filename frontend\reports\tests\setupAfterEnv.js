/**
 * Setup file that runs after the test framework is installed
 * 
 * This file runs after Je<PERSON> is set up but before each test file.
 */

// Extend Jest matchers
expect.extend({
  toBeValidJsessionId(received) {
    const pass = typeof received === 'string' && 
                 received.length > 10 && 
                 /^[A-Z0-9]+$/.test(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid JSESSIONID`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid JSESSIONID`,
        pass: false
      };
    }
  },
  
  toBeValidHtml(received) {
    const pass = typeof received === 'string' && 
                 received.includes('<html') && 
                 received.includes('</html>');
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be valid HTML`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be valid HTML`,
        pass: false
      };
    }
  }
});

// Set timeout for all tests
jest.setTimeout(10000);

// Suppress console errors during tests
const originalConsoleError = console.error;
console.error = (...args) => {
  if (args[0].includes('Warning:')) {
    return;
  }
  originalConsoleError(...args);
};
