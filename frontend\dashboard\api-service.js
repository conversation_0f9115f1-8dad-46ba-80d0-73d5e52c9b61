/**
 * Dashboard API Service - Unified Implementation
 *
 * Direct replacement of the original dashboard API service
 * using the unified service with dashboard context.
 */

// Use the globally available UnifiedApiService class
document.addEventListener('DOMContentLoaded', function() {
  try {
    // Check if UnifiedApiService is available globally
    if (window.UnifiedApiService) {
      console.log('Creating Dashboard API Service with global UnifiedApiService');
      
      // Create dashboard-specific instance
      const apiService = new window.UnifiedApiService();
      apiService.moduleContext = 'dashboard';
      apiService.initializeConfiguration();
      
      // Make it globally available (preserving existing interface)
      window.apiService = apiService;
      
      console.log('Dashboard API Service (Unified) initialized successfully');
    } else {
      console.error('UnifiedApiService not found! Make sure unified-api-service.js is loaded before this file.');
    }
  } catch (error) {
    console.error('Error initializing Dashboard API Service:', error);
  }
});

// Legacy export support
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = window.apiService || {};
}
