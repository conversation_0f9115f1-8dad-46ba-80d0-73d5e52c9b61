// Configuration Page JavaScript

// DOM Elements
const elements = {
    environmentSelect: document.getElementById('environment-select'),
    environmentDisplay: document.getElementById('environment-display'),
    saveConfigBtn: document.getElementById('save-config-btn'),
    environmentConfigs: {
        development: document.getElementById('development-config'),
        staging: document.getElementById('staging-config'),
        production: document.getElementById('production-config')
    },
    n8nBaseUrl: document.getElementById('n8n-base-url'),
    testExecutionWebhook: document.getElementById('test-execution-webhook'),
    nlpWebhook: document.getElementById('nlp-webhook'),
    testResultsWebhook: document.getElementById('test-results-webhook'),
    refreshInterval: document.getElementById('refresh-interval'),
    enableNotifications: document.getElementById('enable-notifications'),
    autoExpandDetails: document.getElementById('auto-expand-details'),
    // Test configuration elements
    smokeTestTimeout: document.getElementById('smoke-test-timeout'),
    smokeTestRetries: document.getElementById('smoke-test-retries'),
    regressionTestTimeout: document.getElementById('regression-test-timeout'),
    regressionTestRetries: document.getElementById('regression-test-retries'),
    heartbeatTestTimeout: document.getElementById('heartbeat-test-timeout'),
    heartbeatTestFrequency: document.getElementById('heartbeat-test-frequency'),
    heartbeatAutoEnable: document.getElementById('heartbeat-auto-enable'),
    // Environment specific elements
    devDbHost: document.getElementById('dev-db-host'),
    devDbName: document.getElementById('dev-db-name'),
    stagingDbHost: document.getElementById('staging-db-host'),
    stagingDbName: document.getElementById('staging-db-name'),
    prodDbHost: document.getElementById('prod-db-host'),
    prodDbName: document.getElementById('prod-db-name')
};

// Default configuration
const defaultConfig = {
    environment: 'development',
    environments: {
        development: {
            dbHost: 'localhost',
            dbName: 'testautomation'
        },
        staging: {
            dbHost: 'staging-db.example.com',
            dbName: 'testautomation_staging'
        },
        production: {
            dbHost: 'db.example.com',
            dbName: 'testautomation_prod'
        }
    },
    api: {
        n8nBaseUrl: 'http://localhost:5678',
        testExecutionWebhook: '/webhook/test-execution',
        nlpWebhook: '/webhook/nlp-processor',
        testResultsWebhook: '/webhook/test-results'
    },
    interface: {
        refreshInterval: 30,
        enableNotifications: true,
        autoExpandDetails: true
    },
    testConfigs: {
        smoke: {
            timeout: 120,
            retries: 2
        },
        regression: {
            timeout: 600,
            retries: 1
        },
        heartbeat: {
            timeout: 60,
            frequency: 60,
            autoEnable: true
        }
    }
};

// Current configuration (loaded from storage or defaults)
let currentConfig = { ...defaultConfig };

// Initialize configuration page
function initConfigPage() {
    // Load configuration from localStorage
    loadConfig();
    
    // Update form with current configuration
    updateFormValues();
    
    // Set up event listeners
    setupEventListeners();
}

// Load configuration from localStorage
function loadConfig() {
    const storedConfig = localStorage.getItem('testAutomationConfig');
    if (storedConfig) {
        try {
            const parsedConfig = JSON.parse(storedConfig);
            currentConfig = { ...defaultConfig, ...parsedConfig };
        } catch (error) {
            console.error('Error loading configuration:', error);
            // Use default configuration if there's an error
            currentConfig = { ...defaultConfig };
        }
    }
}

// Update form values from current configuration
function updateFormValues() {
    // Environment settings
    elements.environmentSelect.value = currentConfig.environment;
    updateEnvironmentDisplay(currentConfig.environment);
    showEnvironmentConfig(currentConfig.environment);
    
    // Environment-specific settings
    elements.devDbHost.value = currentConfig.environments.development.dbHost;
    elements.devDbName.value = currentConfig.environments.development.dbName;
    elements.stagingDbHost.value = currentConfig.environments.staging.dbHost;
    elements.stagingDbName.value = currentConfig.environments.staging.dbName;
    elements.prodDbHost.value = currentConfig.environments.production.dbHost;
    elements.prodDbName.value = currentConfig.environments.production.dbName;
    
    // API settings
    elements.n8nBaseUrl.value = currentConfig.api.n8nBaseUrl;
    elements.testExecutionWebhook.value = currentConfig.api.testExecutionWebhook;
    elements.nlpWebhook.value = currentConfig.api.nlpWebhook;
    elements.testResultsWebhook.value = currentConfig.api.testResultsWebhook;
    
    // Interface settings
    elements.refreshInterval.value = currentConfig.interface.refreshInterval;
    elements.enableNotifications.checked = currentConfig.interface.enableNotifications;
    elements.autoExpandDetails.checked = currentConfig.interface.autoExpandDetails;
    
    // Test configuration settings
    elements.smokeTestTimeout.value = currentConfig.testConfigs.smoke.timeout;
    elements.smokeTestRetries.value = currentConfig.testConfigs.smoke.retries;
    elements.regressionTestTimeout.value = currentConfig.testConfigs.regression.timeout;
    elements.regressionTestRetries.value = currentConfig.testConfigs.regression.retries;
    elements.heartbeatTestTimeout.value = currentConfig.testConfigs.heartbeat.timeout;
    elements.heartbeatTestFrequency.value = currentConfig.testConfigs.heartbeat.frequency;
    elements.heartbeatAutoEnable.checked = currentConfig.testConfigs.heartbeat.autoEnable;
}

// Set up event listeners
function setupEventListeners() {
    // Environment change
    elements.environmentSelect.addEventListener('change', function() {
        const selectedEnvironment = this.value;
        showEnvironmentConfig(selectedEnvironment);
        updateEnvironmentDisplay(selectedEnvironment);
    });
    
    // Save configuration
    elements.saveConfigBtn.addEventListener('click', saveConfig);
}

// Show the correct environment configuration panel
function showEnvironmentConfig(environment) {
    // Hide all environment configs
    Object.values(elements.environmentConfigs).forEach(element => {
        element.classList.add('ms-hidden');
    });
    
    // Show the selected environment config
    if (elements.environmentConfigs[environment]) {
        elements.environmentConfigs[environment].classList.remove('ms-hidden');
    }
}

// Update the environment display in the header
function updateEnvironmentDisplay(environment) {
    elements.environmentDisplay.textContent = `Current Environment: ${environment.charAt(0).toUpperCase() + environment.slice(1)}`;
}

// Save configuration
function saveConfig() {
    // Collect all form values
    const config = {
        environment: elements.environmentSelect.value,
        environments: {
            development: {
                dbHost: elements.devDbHost.value,
                dbName: elements.devDbName.value
            },
            staging: {
                dbHost: elements.stagingDbHost.value,
                dbName: elements.stagingDbName.value
            },
            production: {
                dbHost: elements.prodDbHost.value,
                dbName: elements.prodDbName.value
            }
        },
        api: {
            n8nBaseUrl: elements.n8nBaseUrl.value,
            testExecutionWebhook: elements.testExecutionWebhook.value,
            nlpWebhook: elements.nlpWebhook.value,
            testResultsWebhook: elements.testResultsWebhook.value
        },
        interface: {
            refreshInterval: parseInt(elements.refreshInterval.value, 10),
            enableNotifications: elements.enableNotifications.checked,
            autoExpandDetails: elements.autoExpandDetails.checked
        },
        testConfigs: {
            smoke: {
                timeout: parseInt(elements.smokeTestTimeout.value, 10),
                retries: parseInt(elements.smokeTestRetries.value, 10)
            },
            regression: {
                timeout: parseInt(elements.regressionTestTimeout.value, 10),
                retries: parseInt(elements.regressionTestRetries.value, 10)
            },
            heartbeat: {
                timeout: parseInt(elements.heartbeatTestTimeout.value, 10),
                frequency: parseInt(elements.heartbeatTestFrequency.value, 10),
                autoEnable: elements.heartbeatAutoEnable.checked
            }
        }
    };
    
    // Update current configuration
    currentConfig = config;
    
    // Save to localStorage
    try {
        localStorage.setItem('testAutomationConfig', JSON.stringify(config));
        showSuccess('Configuration saved successfully!');
    } catch (error) {
        console.error('Error saving configuration:', error);
        showError('Failed to save configuration. Please try again.');
    }
}

// Show success message
function showSuccess(message) {
    const toast = document.createElement('div');
    toast.className = 'ms-toast ms-toast-success';
    toast.innerHTML = `
        <div class="ms-toast-icon">✓</div>
        <div class="ms-toast-message">${message}</div>
    `;
    
    document.body.appendChild(toast);
    
    // Show the toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // Hide and remove the toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Show error message
function showError(message) {
    const toast = document.createElement('div');
    toast.className = 'ms-toast ms-toast-error';
    toast.innerHTML = `
        <div class="ms-toast-icon">!</div>
        <div class="ms-toast-message">${message}</div>
    `;
    
    document.body.appendChild(toast);
    
    // Show the toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // Hide and remove the toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initConfigPage);