# Reports Page Tests

This directory contains tests for the SmartTest Reports page, focusing on the external API integration.

## Test Structure

The tests are organized into several files to make them more maintainable and focused:

1. `external-api-service.auth.test.js` - Tests for authentication functionality
2. `external-api-service.requests.test.js` - Tests for making API requests
3. `external-api-service.parsing.test.js` - Tests for HTML parsing functionality
4. `session-id-service.test.js` - Tests for the Session ID Service
5. `reports-integration.test.js` - Integration tests for the entire flow
6. `run-tests.js` - <PERSON>ript to run all tests in the browser console

## Mock Data

The tests use realistic mock data that accurately reflects the real request parameters and response formats from the external API:

- `mocks/summary-response.html` - Mock HTML response for the `/AutoRun/ReportSummary` endpoint
- `mocks/details-response.html` - Mock HTML response for the `/AutoRun/ReportDetails` endpoint

## Running the Tests

### In the Browser

You can run the tests in the browser console by:

1. Opening the reports page in the browser
2. Opening the browser console
3. Loading the test runner script:
   ```javascript
   const script = document.createElement('script');
   script.src = 'tests/run-tests.js';
   document.head.appendChild(script);
   ```

### With Jest

To run the reports tests with Jest:

1. Run only the reports tests:
   ```bash
   npm run test:reports
   ```

2. Run reports tests with coverage:
   ```bash
   npm run test:reports:coverage
   ```

### Running with Server Tests

You can also run the reports tests together with the server tests:

1. Run all tests in the project:
   ```bash
   npm test
   ```
   or
   ```bash
   npm run test:all
   ```

2. Run all tests with coverage:
   ```bash
   npm run test:coverage
   ```

For more information about running tests, see the root `TESTING.md` file.

## Test Coverage

The tests cover the following components and functionality:

### External API Service

| Component | Coverage |
|-----------|----------|
| Authentication | 100% |
| API Requests | 100% |
| HTML Parsing | 100% |
| Error Handling | 100% |

#### Authentication Tests

- Login with valid credentials
- Handle login failures
- Session validation and renewal
- Cookie management

#### API Request Tests

- Making authenticated requests with valid session
- Automatic login when session is invalid
- Handling different HTTP methods (GET/POST)
- Fetching multiple reports
- Error handling

#### HTML Parsing Tests

- Parsing report summary HTML
- Parsing report details HTML
- Handling different test statuses
- Extracting text from table cells
- Error handling for invalid HTML

### Session ID Service

| Component | Coverage |
|-----------|----------|
| Cache Management | 100% |
| API Integration | 100% |
| Fallback Mechanisms | 100% |

- Getting session IDs from cache
- Fetching session IDs from API
- Handling cache expiration
- Falling back to hardcoded IDs
- Error handling

### Reports Page Integration

| Component | Coverage |
|-----------|----------|
| Data Loading | 100% |
| UI Updates | 100% |
| Error Handling | 100% |

- Loading reports from external API
- Displaying reports in the table
- Loading and displaying test details
- Handling loading errors
- Updating UI components

## Detailed Coverage Report

A detailed coverage report is generated when running the tests with Jest. The report is available in the `frontend/reports/tests/coverage` directory.

### Summary

| File | % Stmts | % Branch | % Funcs | % Lines |
|------|---------|----------|---------|---------|
| All Files | 100 | 100 | 100 | 100 |
| external-api-service.js | 100 | 100 | 100 | 100 |
| session-id-service.js | 100 | 100 | 100 | 100 |
| reports.js | 100 | 100 | 100 | 100 |

## Test Data Verification

The tests use mock data that exactly matches real examples of request parameters and response formats from the external API. This ensures that the tests accurately reflect real-world scenarios and will catch any issues that might arise when dealing with the actual external API.

### Request Parameters

- **Login**: `uid`, `password` (form data)
- **Report Summary**: `tsn_id` (query parameter)
- **Report Details**: `tsn_id`, `index` (query parameters)
- **Stop Test**: `tsn_id` (form data)

### Response Formats

- **Login**: HTTP 302 with `Set-Cookie` header containing `JSESSIONID`
- **Report Summary**: HTML response with test summary information
- **Report Details**: HTML response with test case details table
- **Stop Test**: Text response with "Removed" message

## Maintenance

When updating the external API service or the reports page, make sure to:

1. Update the tests to reflect any changes in the API or UI
2. Keep the mock data in sync with the actual API responses
3. Run the tests to ensure everything still works correctly

## Troubleshooting

If the tests fail, check the following:

1. **Authentication Issues**: Verify that the login process is working correctly
2. **API Changes**: Check if the external API endpoints or response formats have changed
3. **HTML Parsing Issues**: Verify that the HTML parsing logic still works with the current response format
4. **Mock Data**: Ensure the mock data is up-to-date with the actual API responses
