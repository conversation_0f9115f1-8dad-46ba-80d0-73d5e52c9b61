/**
 * SmartTest Services Index
 * 
 * Centralized export point for all API services in the SmartTest application.
 * This provides a clean interface for importing services throughout the application.
 */

// Configuration
export { ApiConfig, apiConfig } from './api-config.js';

// Base service
export { BaseApiService, ApiError } from './base-api-service.js';

// Main API service
export { SmartTestApiService, smartTestApi } from './smarttest-api-service.js';

// External API service
export { ExternalApiService, externalApiService } from './external-api-service.js';

// Backward compatibility - make services available globally
if (typeof window !== 'undefined') {
  // Main API service (primary interface)
  window.smartTestApi = smartTestApi;
  
  // Backward compatibility aliases
  window.apiService = smartTestApi;
  window.externalApiService = externalApiService;
  
  // Configuration access
  window.apiConfig = apiConfig;
  
  console.log('SmartTest API services initialized and available globally');
}

// CommonJS compatibility
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = {
    ApiConfig,
    apiConfig,
    BaseApiService,
    ApiError,
    SmartTestApiService,
    smartTestApi,
    ExternalApiService,
    externalApiService
  };
}
