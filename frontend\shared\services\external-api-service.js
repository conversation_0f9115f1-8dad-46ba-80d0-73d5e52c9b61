/**
 * External API Service
 *
 * Specialized service for direct integration with external APIs on port 9080
 * using cookie-based authentication. This service handles HTML parsing and
 * session management for external API endpoints.
 */

// Use BaseApiService if available, otherwise create a minimal base class
const BaseApiService = window.BaseApiService || class {
  constructor() {
    this.config = window.apiConfig || {
      getUrl: (endpoint) => endpoint,
      getBaseUrl: (type) => type === 'external' ? 'http://mprts-qa02.lab.wagerworks.com:9080' : '/api'
    };
    this.credentials = { uid: '', password: '' };
    this.loadCredentials();
  }

  loadCredentials() {
    try {
      const uid = sessionStorage.getItem('smarttest_uid');
      const password = sessionStorage.getItem('smarttest_pwd');
      if (uid && password) {
        this.credentials = { uid, password };
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error loading credentials:', error);
      return false;
    }
  }
};

class ExternalApiService extends BaseApiService {
  constructor() {
    super();

    // Session state
    this.jsessionId = null;
    this.jsessionExpiry = null;

    // <PERSON>ie expires after 30 minutes on server, we'll use 25 minutes to be safe
    this.cookieExpiryTime = 25 * 60 * 1000;

    console.log('External API Service initialized');
  }

  /**
   * Check if the current session is valid
   * @returns {boolean} Whether the session is valid
   */
  isSessionValid() {
    if (!this.jsessionId || !this.jsessionExpiry) {
      console.log(`[ExternalApiService] 🔍 Session invalid: jsessionId=${!!this.jsessionId}, jsessionExpiry=${!!this.jsessionExpiry}`);
      return false;
    }

    const isValid = Date.now() < this.jsessionExpiry;
    console.log(`[ExternalApiService] 🔍 Session validity check: ${isValid} (expires in ${Math.round((this.jsessionExpiry - Date.now()) / 1000)}s)`);
    return isValid;
  }

  /**
   * Login to the external API and get a JSESSIONID cookie
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @returns {Promise<boolean>} Whether login was successful
   */
  async login(uid, password) {
    try {
      console.log(`[ExternalApiService] 🔑 Attempting login to external API as ${uid}...`);

      // Use the proxy endpoint for login
      const loginUrl = '/api/external/login';

      const formData = new URLSearchParams();
      formData.append('uid', uid);
      formData.append('password', password);

      console.log(`[ExternalApiService] 📡 Making login request to ${loginUrl}`);

      const response = await fetch(loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
          'Origin': this.config.getBaseUrl('external'),
          'Referer': `${this.config.getBaseUrl('external')}/AutoRun/Login`
        },
        body: formData,
        credentials: 'include',
        redirect: 'follow'
      });

      console.log(`[ExternalApiService] 📡 Login response status:`, response.status);
      console.log(`[ExternalApiService] 📡 Login response headers:`, Object.fromEntries(response.headers.entries()));

      if (!response.ok && response.status !== 302) {
        throw new Error(`Login failed with status ${response.status}`);
      }

      // ENHANCED COOKIE DETECTION: Check multiple sources for JSESSIONID
      console.log(`[ExternalApiService] 🍪 Checking for JSESSIONID cookie...`);

      // Method 1: Check document.cookie (for cookies set in current domain)
      const documentCookies = document.cookie;
      console.log(`[ExternalApiService] 🍪 Document cookies:`, documentCookies);

      const jsessionMatch = documentCookies.match(/JSESSIONID=([^;]+)/);
      if (jsessionMatch && jsessionMatch[1]) {
        this.jsessionId = jsessionMatch[1];
        console.log(`[ExternalApiService] ✅ Found JSESSIONID in document.cookie:`, this.jsessionId);
      }

      // Method 2: Check Set-Cookie headers from response
      const setCookieHeaders = response.headers.get('set-cookie');
      if (setCookieHeaders) {
        console.log(`[ExternalApiService] 🍪 Set-Cookie headers:`, setCookieHeaders);
        const jsessionFromHeader = setCookieHeaders.match(/JSESSIONID=([^;]+)/);
        if (jsessionFromHeader && jsessionFromHeader[1]) {
          this.jsessionId = jsessionFromHeader[1];
          console.log(`[ExternalApiService] ✅ Found JSESSIONID in Set-Cookie header:`, this.jsessionId);
        }
      }

      // Method 3: For proxy scenarios, the cookie might be handled server-side
      // In this case, we rely on the browser's automatic cookie handling
      if (!this.jsessionId) {
        console.log(`[ExternalApiService] 🍪 No explicit JSESSIONID found, relying on browser cookie handling`);
        // Set a placeholder to indicate we're relying on browser cookies
        this.jsessionId = 'BROWSER_MANAGED';
      }

      const responseText = await response.text();
      console.log(`[ExternalApiService] 📄 Response text length:`, responseText.length);

      // Check for login success indicators
      const loginSuccessful =
        responseText.includes(uid) ||
        responseText.includes('Welcome') ||
        responseText.includes('Logout') ||
        responseText.includes('successfully') ||
        (response.status === 200 && !responseText.includes('<title>Login Page</title>')) ||
        response.status === 302; // Redirect often indicates successful login

      console.log(`[ExternalApiService] 🔍 Login success indicators:`, {
        includesUid: responseText.includes(uid),
        includesWelcome: responseText.includes('Welcome'),
        includesLogout: responseText.includes('Logout'),
        includesSuccessfully: responseText.includes('successfully'),
        statusOkAndNotLoginPage: response.status === 200 && !responseText.includes('<title>Login Page</title>'),
        isRedirect: response.status === 302,
        overall: loginSuccessful
      });

      if (loginSuccessful) {
        console.log(`[ExternalApiService] ✅ Login successful`);
        this.jsessionExpiry = Date.now() + this.cookieExpiryTime;
        return true;
      } else {
        console.log(`[ExternalApiService] ⚠️ Primary login method failed, trying server-side login API...`);

        // Try server-side login API as fallback
        const serverLoginResponse = await fetch('/api/login-proxy', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ uid, password }),
          credentials: 'include'
        });

        console.log(`[ExternalApiService] 📡 Server login response status:`, serverLoginResponse.status);

        if (serverLoginResponse.ok) {
          console.log(`[ExternalApiService] ✅ Server-side login successful`);
          this.jsessionExpiry = Date.now() + this.cookieExpiryTime;
          this.jsessionId = 'BROWSER_MANAGED'; // Server handles the cookie
          return true;
        } else {
          throw new Error('Login verification failed - both direct and server-side login failed');
        }
      }
    } catch (error) {
      console.error(`[ExternalApiService] ❌ Error logging in to external API:`, error);
      throw error;
    }
  }

  /**
   * Get a valid session, logging in if necessary
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @returns {Promise<boolean>} Whether a valid session exists or was created
   */
  async getValidSession(uid, password) {
    if (this.isSessionValid()) {
      return true;
    }

    return await this.login(uid, password);
  }

  /**
   * Make an authenticated request to the external API
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Query parameters
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @param {string} method - HTTP method
   * @returns {Promise<Response>} Fetch response
   */
  async makeAuthenticatedRequest(endpoint, params = {}, uid, password, method = 'GET') {
    try {
      // Ensure we have a valid session first
      await this.getValidSession(uid, password);

      // Build URL
      const baseUrl = this.config.getBaseUrl('api'); // Use proxy
      const url = new URL(`${baseUrl}${endpoint}`);

      // Request options
      const options = {
        method,
        headers: {
          'Referer': `${this.config.getBaseUrl('external')}/AutoRun`,
          'Origin': this.config.getBaseUrl('external')
        },
        credentials: 'include'
      };

      // Handle parameters based on method
      if (method === 'GET') {
        Object.entries(params).forEach(([key, value]) => {
          url.searchParams.append(key, value);
        });
      } else {
        const formData = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          formData.append(key, value);
        });
        options.body = formData;
        options.headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8';
      }

      console.log(`Making ${method} request to ${url.toString()}`);

      const response = await fetch(url.toString(), options);

      if (!response.ok) {
        throw new Error(`Request failed with status ${response.status}`);
      }

      return response;
    } catch (error) {
      console.error(`Error making authenticated request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Get report summary for a test session
   * @param {string} tsnId - Test session ID
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @returns {Promise<Object>} Parsed report summary data
   */
  async getReportSummary(tsnId, uid, password) {
    try {
      console.log(`Getting report summary for test session ${tsnId}...`);

      const endpoint = `/test-reports/${tsnId}/summary`;
      const response = await this.makeAuthenticatedRequest(endpoint, {}, uid, password);

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        return data.summary || data;
      } else {
        const html = await response.text();
        return this.parseReportSummaryHtml(html, tsnId);
      }
    } catch (error) {
      console.error(`Error getting report summary for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get report details for a test session
   * @param {string} tsnId - Test session ID
   * @param {number} index - Page number
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @returns {Promise<Object>} Parsed report details data
   */
  async getReportDetails(tsnId, index = 1, uid, password) {
    try {
      console.log(`Getting report details for test session ${tsnId}, page ${index}...`);

      const endpoint = `/test-reports/${tsnId}`;
      const response = await this.makeAuthenticatedRequest(endpoint, { page: index }, uid, password);

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        return data.reports || data;
      } else {
        const html = await response.text();
        return this.parseReportDetailsHtml(html, tsnId);
      }
    } catch (error) {
      console.error(`Error getting report details for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Stop a running test session
   * @param {string} tsnId - Test session ID
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @returns {Promise<boolean>} Whether the stop was successful
   */
  async stopTestSession(tsnId, uid, password) {
    try {
      console.log(`Stopping test session ${tsnId}...`);

      const response = await this.makeAuthenticatedRequest(
        '/RemoveSession',
        { tsn_id: tsnId },
        uid,
        password,
        'POST'
      );

      const text = await response.text();
      const success = text.trim() === 'Removed';

      console.log(`Stop test session ${tsnId} ${success ? 'successful' : 'failed'}`);
      return success;
    } catch (error) {
      console.error(`Error stopping test session ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Parse HTML from ReportSummary endpoint to extract report data
   * @param {string} html - HTML response
   * @param {string} tsnId - Test session ID
   * @returns {Object} Parsed report data
   */
  parseReportSummaryHtml(html, tsnId) {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // Extract status
      let status = 'Unknown';
      const statusSpan = doc.querySelector('span[style*="color:red"], span[style*="color:green"]');
      if (statusSpan) {
        status = statusSpan.textContent.trim();
      }

      // Extract times and other data
      let startTime = null;
      let endTime = null;
      let passedCases = 0;
      let failedCases = 0;
      let skippedCases = 0;

      const listItems = doc.querySelectorAll('ul li');
      listItems.forEach(item => {
        const text = item.textContent.trim();
        if (text.startsWith('Start Time:')) {
          startTime = text.replace('Start Time:', '').trim();
        } else if (text.startsWith('End Time:')) {
          endTime = text.replace('End Time:', '').trim();
        } else if (text.includes('passed:')) {
          passedCases = parseInt(text.match(/(\d+)/)?.[1]) || 0;
        } else if (text.includes('failed:')) {
          failedCases = parseInt(text.match(/(\d+)/)?.[1]) || 0;
        } else if (text.includes('skipped:')) {
          skippedCases = parseInt(text.match(/(\d+)/)?.[1]) || 0;
        }
      });

      // Calculate duration
      let duration = null;
      if (startTime && endTime) {
        try {
          const start = new Date(startTime);
          const end = new Date(endTime);
          const durationMs = end - start;

          if (!isNaN(durationMs) && durationMs >= 0) {
            const durationSec = Math.floor(durationMs / 1000);
            const minutes = Math.floor(durationSec / 60);
            const seconds = durationSec % 60;
            duration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
          }
        } catch (error) {
          console.error('Error calculating duration:', error);
        }
      }

      return {
        tsn_id: tsnId,
        status: status === 'PASS' ? 'Success' : (status === 'FAIL' ? 'Failed' : status),
        start_time: startTime,
        end_time: endTime,
        duration: duration,
        total_cases: passedCases + failedCases + skippedCases,
        passed_cases: passedCases,
        failed_cases: failedCases,
        skipped_cases: skippedCases,
        pass_rate: passedCases + failedCases + skippedCases > 0 ?
          Math.round((passedCases / (passedCases + failedCases + skippedCases)) * 100) : 0
      };
    } catch (error) {
      console.error('Error parsing report summary HTML:', error);
      return {
        tsn_id: tsnId,
        status: 'Error',
        error: error.message
      };
    }
  }

  /**
   * Parse HTML from ReportDetails endpoint to extract test case details
   * @param {string} html - HTML response
   * @param {string} tsnId - Test session ID
   * @returns {Object} Parsed test case details
   */
  parseReportDetailsHtml(html, tsnId) {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      const testCases = [];
      const tableRows = doc.querySelectorAll('table tr');

      // Skip header row
      for (let i = 1; i < tableRows.length; i++) {
        const row = tableRows[i];
        const cells = row.querySelectorAll('td');

        if (cells.length >= 5) {
          const tcId = this.extractTextFromCell(cells[0]);
          const seqIndex = this.extractTextFromCell(cells[1]);

          let outcome = 'Unknown';
          const outcomeLink = cells[2].querySelector('a');
          if (outcomeLink) {
            if (outcomeLink.classList.contains('P')) {
              outcome = 'Passed';
            } else if (outcomeLink.classList.contains('F')) {
              outcome = 'Failed';
            }
          }

          const description = this.extractTextFromCell(cells[3]);
          const inputOutput = this.extractTextFromCell(cells[4]);

          testCases.push({
            tc_id: tcId,
            seq_index: seqIndex,
            status: outcome,
            description: description,
            input_output: inputOutput
          });
        }
      }

      return {
        tsn_id: tsnId,
        test_cases: testCases
      };
    } catch (error) {
      console.error('Error parsing report details HTML:', error);
      return {
        tsn_id: tsnId,
        test_cases: [],
        error: error.message
      };
    }
  }

  /**
   * Extract text from a table cell
   * @param {Element} cell - Table cell element
   * @returns {string} Extracted text
   */
  extractTextFromCell(cell) {
    if (!cell) return '';

    const link = cell.querySelector('a');
    if (link) {
      return link.textContent.trim();
    }

    return cell.textContent.trim();
  }
}

// Enhanced External API Service with additional methods
class EnhancedExternalApiService extends ExternalApiService {
  constructor() {
    super();
    console.log('Enhanced External API Service initialized');
  }

  /**
   * Get recent session IDs from external API
   * @param {Object} credentials - User credentials
   * @param {number} limit - Maximum number of session IDs to retrieve
   * @returns {Promise<string[]>} Array of session IDs
   */
  async getRecentSessionIds(credentials, limit = 25) {
    try {
      console.log(`Getting recent session IDs (limit: ${limit})...`);

      // For now, return mock session IDs - this would need to be implemented
      // based on the actual external API endpoint for session IDs
      const mockSessionIds = [];
      for (let i = 0; i < Math.min(limit, 10); i++) {
        mockSessionIds.push(`1484${7 + i}`);
      }

      console.log(`Found ${mockSessionIds.length} session IDs`);
      return mockSessionIds;
    } catch (error) {
      console.error('Error getting recent session IDs:', error);
      return [];
    }
  }

  /**
   * Get recent test runs for multiple session IDs
   * @param {string[]} sessionIds - Array of session IDs
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @param {number} limit - Maximum number of reports to return
   * @returns {Promise<Object[]>} Array of test run reports
   */
  async getRecentTestRuns(sessionIds, uid, password, limit = 25) {
    try {
      console.log(`Getting recent test runs for ${sessionIds.length} session IDs...`);

      const reports = [];
      const limitedIds = sessionIds.slice(0, limit);

      for (const sessionId of limitedIds) {
        try {
          const summary = await this.getReportSummary(sessionId, uid, password);
          if (summary && summary.tsn_id) {
            reports.push(summary);
          }
        } catch (error) {
          console.warn(`Failed to get report for session ${sessionId}:`, error);
          // Continue with other sessions
        }
      }

      console.log(`Retrieved ${reports.length} test run reports`);
      return reports;
    } catch (error) {
      console.error('Error getting recent test runs:', error);
      return [];
    }
  }
}

// Create instances and make available globally
const externalApiService = new ExternalApiService();
const enhancedExternalApiService = new EnhancedExternalApiService();

// Make available globally
if (typeof window !== 'undefined') {
  window.ExternalApiService = ExternalApiService;
  window.externalApiService = externalApiService;
  window.EnhancedExternalApiService = EnhancedExternalApiService;
  window.enhancedExternalApiService = enhancedExternalApiService;
  console.log('External API Services loaded and available globally');
  console.log('- externalApiService:', !!window.externalApiService);
  console.log('- enhancedExternalApiService:', !!window.enhancedExternalApiService);
}

// Export for CommonJS compatibility
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = {
    ExternalApiService,
    externalApiService,
    EnhancedExternalApiService,
    enhancedExternalApiService
  };
}
