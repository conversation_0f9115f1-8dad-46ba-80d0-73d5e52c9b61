/**
 * Test Cases Routes
 */
const express = require('express');
const router = express.Router();
const db = require('../database');
const { validateCredentials } = require('../middleware/auth');

// Get test cases
router.get('/test-cases', validateCredentials, async (req, res) => {
  try {
    console.log('GET /local/test-cases');
    // Use the database module to fetch test cases
    const testCases = await db.getTestCases(req.query);

    // Return as JSON with success flag
    return res.json({
      success: true,
      data: testCases || [],
      message: 'Test cases retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving test cases:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test cases',
      error: error.message
    });
  }
});

// Note: Test details route has been moved to test-details.js
// to centralize and standardize the test details retrieval logic

module.exports = router;
